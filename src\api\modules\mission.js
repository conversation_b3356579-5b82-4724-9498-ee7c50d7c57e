import request from '@/api/request';
import URL from '../urls';
import store from '@/store';

let canSeeAll = false;
function getManualWatch() {
  canSeeAll = store.getters.privileges.includes('mission_center_manual_watch');
}

/**
 * 创建任务
 */
export class MissionAPI {
  /* 父任务执行情况统计*/
  static parentExecutionResult(data) {
    return request.post(URL.missionManage.parentExecutionResult, data);
  }

  /* 子任务执行情况统计*/
  static childrenExecutionResult(data) {
    return request.post(URL.missionManage.childrenExecutionResult, data);
  }

  /* 创建twitter 公共主页 */
  static createTwitterHomePage(data) {
    return request.post(URL.missionManage.createTwitterHomePage, data);
  }

  /**
   * 创建父任务
   */
  static createParentTask(data) {
    return request.post(URL.missionManage.createParentTask, data);
  }

  /**
   * 创建子任务
   */
  static createChildTask(data) {
    return request.post(URL.missionManage.createParentTask, data);
  }

  /**
   * 创建快速子任务
   */
  static createQuickChildTask(data) {
    return request.post(URL.missionManage.createQuickChildTask, data);
  }

  /**
   * 通过模板创建任务
   */
  static createTaskByTemplate(data) {
    return request.post(URL.missionManage.createTaskByTemplate, data);
  }

  /**
   * 获取部门映射人员
   */
  static getDepartmentUserList(params) {
    return request.get(URL.missionManage.departmentUser, { params });
  }

  /**
   * 舆论上报
   */
  static postReport(data) {
    return request.post(URL.missionManage.report, data);
  }

  /**
   * 数据分析
   * @param {object} data 任务信息
   */
  // static analysis(data) {
  //   return request.post(URL.missionManage.analysis, data);
  // }

  /**
   * 子任务详情 任务拆解 作战进度
   */
  // static getMissionProcess(params) {
  //   return request.get(URL.missionManage.summarize, { params })
  // }

  /**
   * 获取子任务标签
   * @param {object} params
   */
  static getMissionTags(params) {
    return request.get(URL.missionManage.tag, { params });
  }

  // 查询当前用户能查看的所有任务标签
  static getAllMissionTags() {
    return request.post(URL.missionManage.tagAll);
  }

  /**
   * 搜索任务
   * @param {object} data
   */
  static search(data) {
    getManualWatch();

    return request.post(URL.missionManage.search, { ...data, canSeeAll });
  }

  /**
   * 获取mission信息
   * @param {*} params
   */
  // src\views\mission\pages\detail\index.vue
  // src\views\mission\pages\edit\index.vue
  // src\views\mission\pages\sub-detail\index.vue
  static getMission(params) {
    return request.get(URL.missionManage.mission, { params });
  }

  static mission(data) {
    return request.post(URL.missionManage.mission, data);
  }

  /**
   * 获取流程图信息
   * @param {*} params
   */
  static getGraph(params) {
    return request.get(URL.missionManage.missionGraph, { params });
  }

  /**
   * 保存流程图
   * @param {*} data
   */
  static missionGraph(data) {
    return request.post(URL.missionManage.missionGraph, data);
  }

  /**
   * 获取行为统计
   * @param {*} params
   */
  static getStats(params) {
    return request.get(URL.missionManage.missionStats, { params });
  }

  /**
   * 上报历史
   * @param {*} params
   */
  static getHistory(data) {
    return request.post(URL.missionManage.history, data);
  }

  /**
   * 置顶任务
   * @param {number} id 任务ID
   */
  static starTask(id) {
    return request.post(URL.missionManage.star, { id });
  }

  /**
   * 取消置顶任务
   * @param {number} id 任务ID
   */
  static unstarTask(id) {
    return request.post(URL.missionManage.unstar, { id });
  }

  static dashboard(params) {
    return request.get(URL.missionManage.dashboard, { params });
  }

  static timeline(params) {
    return request.get(URL.missionManage.timeline, { params });
  }

  // 获取任务详情
  static detail(taskId, params) {
    getManualWatch();
    const url = URL.missionManage.taskId.replace(/\{id\}/u, taskId);
    return request.get(url, { params: { ...params, canSeeAll } });
  }

  // 获取子任务列表
  static childList(data) {
    return request.post(URL.missionManage.childList, { ...data, canSeeAll });
  }

  // 获取子任务列表筛选平台列表
  static getTaskChildrenPlatformList(data) {
    return request.post(URL.missionManage.getTaskChildrenPlatformList, { ...data, canSeeAll });
  }

  // src\views\mission\components\EditSubMissionDialog.vue
  // src\views\mission\pages\edit\index.vue
  static modify(data) {
    return request.put(URL.missionManage.mission, data);
  }

  // src\views\mission\components\MissionDetailCard.vue
  // src\views\mission\components\MissionFields.vue
  // 待定 -- 后续再删除
  // static delMission(data) {
  //   return request.delete(URL.missionManage.mission, { data })
  // }

  // 执行任务的各种操作(启动、暂停、停止、重跑、恢复等)
  static execute(data) {
    return request.post(URL.missionManage.execute, data);
  }

  // 删除任务
  static deleteTask(id) {
    const url = URL.missionManage.taskId.replace(/\{id\}/u, id);
    return request.delete(url);
  }

  // 复制任务
  static cloneTask(id) {
    return request.post(URL.missionManage.clone, { id: id });
  }

  // 父任务状态异常时，重试父任务
  static retry(data) {
    const params = { ...data };
    params.start_type = 'retry';
    return request.post(URL.missionManage.start, { ...params });
  }

  static getResult(params) {
    return request.get(URL.missionManage.result, { params });
  }

  // 获取step日志
  static getLogs(data) {
    return request.post(URL.missionManage.stepLogs, data);
  }

  // 获取step日志 新街口
  static getStepLogs(data) {
    return request.post(URL.missionManage.getStepLogs, data);
  }

  static postLogs(data) {
    return request.post(URL.missionManage.logs, data);
  }

  static clone(data) {
    return request.post(URL.missionManage.clone, data);
  }

  // 增加标签
  static postTags(data) {
    return request.post(URL.missionManage.addTag, data);
  }

  // 删除标签
  static deleteTag(data) {
    return request.post(URL.missionManage.deleteTag, data);
  }

  // 更新标签
  static updateTag(data) {
    return request.post(URL.missionManage.updateTag, data);
  }

  // 更新任务
  static updateTask(data) {
    if (!data.taskId) {
      return Promise.reject('taskId 不能为空');
    }
    const url = URL.missionManage.taskId.replace(/\{id\}/u, data.taskId);
    return request.put(url, data);
  }

  // 批量创建注册任务
  static batchCreateRegisterTask(data) {
    return request.post(URL.missionManage.batchCreateRegisterTask, data);
  }

  // 舆论总结上报
  static reportSummary(form) {
    return request.post(URL.missionManage.reportSummary, form, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });
  }

  // 更新任务周期
  static updateTaskFrequency(data) {
    return request.post(URL.missionManage.updateFrequency, data);
  }

  // 任务置顶
  static updateTaskTop(data) {
    return request.post(URL.missionManage.updateTaskTop, data);
  }

  // 获取子任务详情数据
  static getSubtask(params) {
    return request.get(URL.missionManage.subtask, { params });
  }

  // 获取任务概述
  static getIntro(params) {
    return request.get(URL.missionManage.summarize, { params });
  }

  // 主任务详情-舆论态势
  static getPublicInfo(data) {
    return request.post(URL.missionManage.missionStatistics, data);
  }

  // 主任务详情-舆论态势-舆论走势
  static getPublicOpinionTrend(data) {
    return request.post(URL.missionManage.getPublicOpinionTrend, data);
  }

  // 主任务详情-舆论态势-传播时序
  static getEventTime(data) {
    return request.post(URL.missionManage.getEventTime, data);
  }

  // 新主任务详情-舆论态势
  static getPublicOpinionObj(data) {
    return request.post(URL.missionManage.getPublicOpinion, data);
  }

  // 新主任务详情-舆论态势 热点网民
  static getUserOpinionReportUser(data) {
    return request.post(URL.missionManage.getUserOpinionReportUser, data);
  }

  // 新主任务详情-舆论态势 热门信息 网民观点
  static getEQuery(data) {
    return request.post(URL.missionManage.getEQuery, data);
  }

  // 主任务详情-舆论态势-网民观点
  static getPublicOpinion(data) {
    return request.post(URL.missionManage.netOpinion, data);
  }

  // 主任务详情-舆论态势-团体饼图数据
  static getCorpsInfo(data) {
    return request.post(URL.missionManage.corpsStatistic, data);
  }

  // 主任务详情-舆论态势-行为记录数据
  static getBehaviorRecord(data) {
    return request.post(URL.missionManage.listContent, data);
  }

  // 配置采集信息 列表
  static getCollectionMissionList(params) {
    return request.get(URL.missionManage.collectionMission, { params });
  }

  // 获取采集信息 标签下拉列表
  static getCollectionMissionTagList(data) {
    return request.post(URL.missionManage.getTagList, data);
  }

  // 配置采集信息 创建
  static postCollectionMission(data) {
    return request.post(URL.missionManage.collectionMission, data);
  }

  // 配置采集信息 删除
  static delCollectionMission(data) {
    return request.delete(URL.missionManage.collectionMission, { data });
  }

  // 配置采集信息 启动
  static collectStart(data) {
    return request.post(URL.missionManage.groupMission.start, data);
  }

  // 配置采集信息 暂停
  static collectPause(data) {
    return request.post(URL.missionManage.groupMission.pause, data);
  }

  // 配置采集信息 继续
  static collectResume(data) {
    return request.post(URL.missionManage.groupMission.resume, data);
  }

  // 配置采集信息 日志
  static getCollectLog(data) {
    return request.post(URL.missionManage.groupMission.log, data);
  }

  // 配置采集信息 获取graph
  static getCollectGraph(params) {
    return request.get(URL.missionManage.groupMission.graph, { params });
  }

  // 配置采集信息 获取graph
  static saveCollectGraph(data) {
    return request.post(URL.missionManage.groupMission.graph, data);
  }

  // 编辑保存注册任务
  static saveCreateAccount(id, data) {
    return request.put(URL.missionManage.groupMission.update(id), data);
  }

  // 编辑保存周期时间频率
  static saveMissionCycle(data) {
    return request.post(URL.missionManage.groupMission.updateMissuonCycle, data);
  }

  // 自动任务 新增一个任务配置
  static createNewDataMission(data) {
    return request.post(URL.missionManage.groupMission.createMission, data);
  }

  static deleteMission(id) {
    return request.delete(URL.missionManage.groupMission.delMission(id));
  }

  // 配置采集信息 获取推荐采集组合
  static getCollectRecommend(id) {
    return request.get(URL.missionManage.collectionRecommend(id));
  }

  /**
   * 保存流程图
   * @param {*} data
   */
  static operationGraph(data) {
    return request.post(URL.missionManage.operationGraph, data);
  }

  /**
   * 上传发帖图片或视频
   */
  static uploadFile(data, cb) {
    const formData = new FormData();
    formData.append('file', data);
    return request.post(URL.missionManage.uploadFile, formData, {
      header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
      onUploadProgress: cb
    });
  }

  /**
   * 从公/私有云上面下载文件 返回二进制流数据
   */
  static downloadFile(params) {
    return request({
      method: 'get',
      url: URL.missionManage.downloadFile,
      responseType: 'blob',
      params
    });
  }

  /**
   * 删除发帖图片或视频
   */
  static delFile(data) {
    return request.delete(URL.missionManage.uploadFile, { data });
  }

  // 获取所有创建过任务的人员
  static allTaskCreator() {
    return request.post(URL.missionManage.allTaskCreator);
  }

  // 获取所有协作过任务的人员
  static assignPersonList() {
    return request.post(URL.missionManage.allAssignPerson);
  }

  // 获取平台列表
  static getCodePlatform(params) {
    return request.get(URL.missionManage.getCodePlatformList, { params });
  }

  // 获取全部平台列表
  static getCodeOldPlatformList(params) {
    return request.get(URL.missionManage.getCodeOldPlatformList, { params });
  }

  // 获取采集方向
  static getCollectionRegions() {
    return request.get(URL.missionManage.getCollectionRegionsList);
  }

  // 获取采集数据类型
  static getDataTypeByPlatformCode(params) {
    return request.get(URL.missionManage.getDataTypeByPlatformCodeList, { params });
  }

  // 获取采集数据分类类型
  static getCollectTypes(params) {
    return request.get(URL.missionManage.getCollectTypesList, { params });
  }

  // 获取通用配置码表
  static getCommonCodeDict(formData) {
    return request.post(URL.missionManage.getCommonCodeDict, formData, { headers: { 'content-type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /** 举报任务获取级联举报理由*/
  static getReportReasonList(data) {
    return request.post(URL.missionManage.getReportReasonList, data);
  }

  /** 举报任务获取级联举报理由lable*/
  static getReportReasonListLabel(data) {
    return request.post(URL.missionManage.getReportReasonListLabel, data);
  }

  /** 举报培育任务的的历史人物列表*/
  static getBreedTaskHistoryList(data) {
    return request.post(URL.missionManage.getBreedTaskHistoryList, data);
  }

  /** 检测虚拟团体的账号是否有过期的和导出的*/
  static checkCropsAccount(params) {
    return request.get(URL.missionManage.checkCropsAccount, { params });
  }

  /** 获取注册账号的类型*/
  /*
    allDevices: true  返回全部
    不传默认false 仅返回支持的数据
  */
  static getRegisterTypeList(params) {
    return request.get(URL.missionManage.getRegisterTypeList, { params });
  }

  /* 获取所有平台是否有联合登录 */
  static getPlatformRegisterMode(params) {
    return request.get(URL.missionManage.getPlatformRegisterMode, { params });
  }

  static getAllDeviceType(params) {
    return request.get(URL.missionManage.getAllDeviceType, { params });
  }

  /* 获取任务上报的人员 */
  static getReportUser(params) {
    return request.get(URL.missionManage.getReportUser, { params });
  }

  /* 获取拥有接码平台的平台列表 */
  static getHaveCodePlatform(data) {
    return request.post(URL.registerMission.haveCodePlatform, data);
  }

  /* 获取任务的状态码表接口  */
  static getTaskAllExecutionStatus() {
    return request.get(URL.missionManage.getTaskAllExecutionStatus);
  }

  /* 获取分组列表 */
  static getTaskGroupList(data) {
    return request.post(URL.missionManage.getTaskGroupList, data);
  }

  /* 创建任务分组  */
  static createTaskGroup(data) {
    return request.post(URL.missionManage.createTaskGroup, data);
  }

  /* 删除任务分组  */
  static delTaskGroup(data) {
    return request.delete(URL.missionManage.delTaskGroup, { data: data });
  }

  /* 编辑任务分组  */
  static updateTaskGroup(data) {
    return request.patch(URL.missionManage.updateTaskGroup, data);
  }

  static getCommentGifCodeList(data) {
    return request.post(URL.missionManage.getCommentGifCodeList, data);
  }

  static getBreedStrategy(params) {
    return request.get(URL.missionManage.getBreedStrategy, { params });
  }

  /* 获取注册任务或者采集任务的创建人 */
  static getAllTaskUser(data) {
    return request.post(URL.missionManage.getAllTaskUser, data);
  }

  /* 获取平台动作交集 */
  static getBreedSupportAction(params) {
    params?.page_index && delete params.page_index;
    params?.page_size && delete params.page_size;
    return request.get(URL.missionManage.getBreedSupportAction, { params });
  }

  /* 新增培育策略 */
  static addBreedStrategy(data) {
    return request.post(URL.missionManage.addBreedStrategy, data);
  }

  /* 获取培育策略列表*/
  static fetchStarategyList(data) {
    return request.post(URL.missionManage.fetchStarategyList, data);
  }

  /* 新增培育策略 */
  static deleteBreedStrategy(data) {
    return request.post(URL.missionManage.deleteBreedStrategy, data);
  }

  /* 动作序列预估 */
  static getActiEnestimate(data) {
    return request.post(URL.missionManage.getActiEnestimate, data);
  }

  /* 删除培育计划 */
  static deleteBreedPlan(data) {
    return request.delete(URL.missionManage.deleteBreedPlan, { data });
  }

  /* 更新培育计划 */
  static updateActionDetail(data) {
    return request.post(URL.missionManage.updateActionDetail, data);
  }

  static getAllTargetErrorType(params) {
    return request.get(URL.missionManage.getAllTargetErrorType, { params });
  }

  static createCollectTask(data) {
    return request.post(URL.missionManage.createCollectTask, data);
  }

  /* 快速发帖 */
  static createAndStartPostTask(data) {
    return request.post(URL.missionManage.createAndStartPostTask, data);
  }
}
export class DailyMissionAPI {
  /**
   * abandon
   * 获取列表数据
   */
  static getDailyMissionList(data) {
    return request.post(URL.dailyMission.list, data);
  }

  /**
   * 获取注册、培育、采集表格数据
   */
  static getPageMissionList(data) {
    return request.post(URL.dailyMission.pageList, data);
  }

  static getDailyMissionManualList(data) {
    return request.post(URL.dailyMission.ManualList, data);
  }

  /** 虚拟角色培育， 通过taskId 获取培育账号列表*/
  static getBreedMemberList(data) {
    return request.post(URL.dailyMission.breedMemberList, data);
  }

  /** 虚拟角色培育， 通过taskId查培育账号统计*/
  static getBreedSummary(data) {
    return request.post(URL.dailyMission.breedSummary, data);
  }

  /** 任务数量统计*/
  static getTaskStatistical(data) {
    return request.post(URL.dailyMission.getTaskStatistical, data);
  }

  /** 获取榜样用户的所有分类标签*/
  static getExampleTagList(query) {
    return request.get(URL.dailyMission.exampleTagList, { params: query });
  }
}
export class RegisterMissionAPI {
  /**
   * 一键启动
   */
  static start(data) {
    return request.post(URL.registerMission.start, data);
  }

  /**
   * 一键暂停
   */
  static pause(data) {
    return request.post(URL.registerMission.pause, data);
  }

  /**
   * 一键继续
   */
  static resume(data) {
    return request.post(URL.registerMission.resume, data);
  }

  /**
   * 创建任务
   */
  static createMission(data) {
    return request.post(URL.registerMission.mission, data);
  }

  /**
   * 编辑任务
   */
  static editMission(data) {
    return request.put(URL.registerMission.mission, data);
  }

  /**
   * 删除任务
   */
  static delMission(params) {
    return request.delete(URL.registerMission.mission, { params });
  }

  /**
   * 获取父任务Id
   * @param mission_type 任务类型
   */
  static getDeliveryParentId(mission_type) {
    return request.get(URL.registerMission.getParentId, { params: { mission_type } });
  }

  /**
   * 获取列表
   */
  static getMissionList(params) {
    return request.get(URL.registerMission.mission, { params });
  }

  /**
   * 获取 手动培育列表 投送列表
   */
  static getSubtask(params) {
    return request.get(URL.registerMission.subtask, { params });
  }

  /**
   * 获取任务统计数据
   */
  static statistic(data) {
    return request.post(URL.registerMission.statistic, data);
  }
}
