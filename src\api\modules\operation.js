import http from '@/api/request';
import URL from '../urls';

/**
 * 运营中心 主页审核
 */
export class AuditAPI {
  /**
   * 获取审核列表
   * @param {object} filter 筛选条件
   */
  static getAuditList(data) {
    return http.post(URL.audit.list, data);
  }

  /**
   * 获取送审人员列表
   */
  static getAuditEditors() {
    return http.post(URL.audit.editors);
  }

  /**
   * 获取详情
   */
  static getAuditDetail(data) {
    return http.post(URL.audit.Detail, data);
  }

  /**
   * 审核通过
   */
  static passAudit(data) {
    return http.post(URL.audit.pass, data);
  }

  /**
   * 拒绝审核
   */
  static refuseAudit(data) {
    return http.post(URL.audit.refuse, data);
  }
}
export class OperationAPI {
  /**
   * 获取查看指定虚拟用户下的帖子
   */
  static getByParentTaskId(data) {
    return http.post(URL.operation.getByParentTaskId, data);
  }

  // 根据公共主页用户accountId 查询其对应的任务id
  static findTaskIdByPublicPageAccount(data) {
    return http.post(URL.operation.findTaskIdByPublicPageAccount, data);
  }

  /**
   * 发帖
   */
  static sendMsg(data) {
    return http.post(URL.operation.addPost, data);
  }

  /**
   * 删除帖子
   */
  static delPosts(data) {
    return http.post(URL.operation.deleteList, data);
  }

  // 创建公共主页
  static createPagesTask(data, config) {
    return http.post(URL.operation.createPagesTask, data, config);
  }

  /**
   * 创建并编辑文章推广
   */
  static postPromotion(data) {
    return http.post(URL.operation.promotion, data);
  }

  /**
   * 推广历史
   */
  static detailGeneralizeHistory(id) {
    const url = URL.operation.promotion.replace(/\{id\}/gu, id);
    return http.get(url);
  }

  // 在子任务详情里面查询推广记录
  static getPromotionHistory(data) {
    return http.post(URL.operation.detailGeneralizeHistory, data);
  }

  // 公共主页类别
  static fetchTypeList(params) {
    return http.get(URL.operation.fetchTypeList, { params });
  }
}
