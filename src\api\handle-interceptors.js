import { Message } from 'element-ui';
import store from '@/store';
import { getToken, setToken } from '@/utils/auth';
import router from '@/router';
import i18n from '@/lang';
import { isEmpty } from '@/utils';
let notNotify = false;

/**
 * 获取当前语言
 */
function geti18n() {
  return window.localStorage.getItem('i18n') === 'zh' ? 'zh_cn' : window.localStorage.getItem('i18n') === 'en' ? 'en_us' : 'zh_tw';
}

/**
 * 获取当前所属的一级部门id
 */
function getDepartmentId() {
  return store.state.user.activeDepartment.id;
}

/**
 * 获取用户信息
 */
function getUserName() {
  return store.state.user.username;
}

/**
 * 统一处理响应拦截器
 * @param {object} response 响应数据
 */
export async function handleResponse(response) {
  const newToken = response.headers['refresh-token'];
  if (newToken) setToken(newToken); // 刷新token

  const { data } = response;
  // Object.prototype.toString.call(data) === '[object Blob]' 处理系统资源 和评论导出 数据为blob
  if (typeof data === 'string' || Object.prototype.toString.call(data) === '[object Blob]') {
    return data;
  }

  // 本地地图json
  if (data.type === 'FeatureCollection') {
    return data;
  }

  if (isEmpty(data?.code)) {
    console.error('错误接口' + response.config.url);
    return Promise.reject();
  }

  // 特殊处理 文章库详情 语种识别接口
  if (data.responseStatus) {
    if (data.responseStatus === 200) {
      return data.responseData;
    } else {
      Message({
        message: i18n.t('store["语言识别失败"]'),
        type: 'error',
        showClose: true,
        offset: 60,
        duration: 1000
      });
      return Promise.reject();
    }
  }

  // 不进行消息提示的状态码
  const noMessageCodeList = [1602001, 1603001, 1603009, 1603008, 50501, 50500, 1499000, 1403001, 1402001, 1];
  if (noMessageCodeList.includes(data.code)) {
    console.error('错误接口' + response.config.url);
    return Promise.reject(data);
  }

  // 登录信息失效，返回登录界面
  if (data.code === 401) {
    store.commit('screenList/SET_CURRENT_ACCOUNT_ID', { id: '', isFirst: false });
    !notNotify && Message.error(data.msg);
    notNotify = true;
    await store.dispatch('user/resetToken');
    // 如果当前页面不是登录页面, 则跳转到登录页面
    if (router.currentRoute.path !== '/login') {
      // 去掉路由的 #/
      const str = window.location.hash.split('#/')[1];
      router.push({ name: 'LOGIN', query: { redirect: str } });
    }
    return Promise.reject(data.data);
  }

  // 特殊处理查看无权查看的部门人员的情况
  if (data.code === 1603100) {
    return { noPermit: true };
  }
  // 特殊处理查看无权查看的部门人员的情况
  if (data.code === 9999) {
    return Promise.reject(data);
  }
  // code为403代表无权查看
  if (data.code === 403) {
    Message({
      message: data.msg,
      type: 'error',
      showClose: true,
      offset: 60,
      duration: 1000
    });
    // router.push('/');  //跳转至首页
    return Promise.reject(data.msg);
  }

  // 处理普通状态码，弹出ERROR类型的消息
  if (data.code !== 0 && data.code !== 200 && data.code !== 1603009 && data.code !== 50021 && data.code !== 10500) {
    let msg = data.msg || data.message;
    // #5830 bug
    msg =
      data.code === 2
        ? i18n.t('store["翻译失败，请手动选择源语言试试"]')
        : msg;
    Message({
      message: msg,
      type: 'error',
      showClose: true,
      offset: 60,
      duration: 1000
    });
    console.error('错误接口' + response.config.url);
    return Promise.reject(data.msg);
  }
  // 返回正常数据
  notNotify = false;
  return data;
}

function __addTaskId2MissionProxy(config) {
  const routerApp = router.app.$route;
  const isMissionRoutePath = /^\/mission\//u.test(routerApp.path);
  if (!isMissionRoutePath) return config;

  const rules = ['/proxy', '/permission_proxy'];
  const isProxy = rules.some(a => {
    const regexp = new RegExp(`^${a}`, 'gu');
    return regexp.test(config.url);
  });
  const isUpload = _.isFunction(config.onUploadProgress); // 上传文件的请求

  if (isProxy && !isUpload) {
    const taskId = routerApp.query.id || routerApp.params.id;
    if (taskId) {
      let originData;
      if (config.method === 'get') {
        originData = config.params ? config.params : {};
        config.params = { ...originData, taskId };
      }
      if (config.method === 'post') {
        originData = config.data ? config.data : {};
        config.data = { ...originData, taskId };
      }
    }
  }
  return config;
}

function __jumpErrorHandle(error) {
  if (!error.config) return true;
  const { url, method } = error.config;
  const jumpUrl = ['/permission_proxy/Translate/TranslateText'];
  if (jumpUrl.includes(url)) return true; // 有符合不校验的url直接跳过
  const regexpList = ['\\/dcms\\/tag-type\\/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}', '\\/dcms\\/all-tag\\/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}'];
  const matched = regexpList.some(reg => {
    const regex = new RegExp(reg, 'u');
    return regex.test(url);
  });
  return matched && method === 'patch';
}

/**
 * 统一处理请求拦截器
 * @param {object} config 请求数据
 */
export function handleRequest(config) {
  // do something before request is sent
  config.headers['uiType'] = '2';
  config.headers['access-token'] = getToken();
  config.headers['permission-department'] = getDepartmentId();
  config.headers['Accept-Language'] = geti18n();
  if (config.url === '/permission_proxy/import/artical') {
    config.headers['creator'] = getUserName();
  }
  // 对于任务的第三方转发，接口需要加上taskId，后端需要通过 taskId来做资源隔离
  config = __addTaskId2MissionProxy(config);
  return config;
}

/**
 * 统一处理请求错误
 * @param {object} error 错误信息
 * @param {boolean} showMessage 是否通知用户错误信息
 */
export function handleError(error, showMessage) {
  // do something with request error
  if (!__jumpErrorHandle(error) && error.message && showMessage) {
    const errMsg = error.response ? `code:${error.response.status}` : `message:${error.message}`;
    Message({
      message: `${i18n.t('store["网络请求失败，请稍后重试"]')}(${errMsg})`,
      type: 'error',
      showClose: true,
      offset: 60,
      duration: 1000
    });
  }
  return Promise.reject(error);
}
