import http from '@/api/request';
import URL from '../urls';
import store from '@/store';

let all_department = 1;
function getAllDepartment() {
  all_department = Number(store.state.user.all_department);
}

/**
 * 通用网络资源打标签
 * */
export class NetResourceTagsAPI {
  /**
   * 网络资源打标签
   * id Array 资源id
   * mark_tag Array 标签数组
   * type String 类型(post, comment, user, image, audio, video)
   */
  static editMarkTag(data) {
    return http.post(URL.netResource.article.editMarkTag, data);
  }

  /* 删除标签*/
  static delMarkTag(data) {
    return http.post(URL.netResource.article.delMarkTag, data);
  }
}

/**
 * 文章库管理
 */
export class ArticleAPI {
  /**
   * 获取原数据列表
   * @param {object} data 筛选条件
   */
  static getList(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.list, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取原文章详情
   * @param {object} data 筛选条件
   */
  static getPostDetail(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.postDetail, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取文章列表
   * @param {object} data 筛选条件
   */
  static getTransList(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.transList, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取文章详情
   */
  static getTransDetail(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.transDetail, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 修改文章情绪分析
   */
  static editEmotion(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.emotion, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取文章评论
   */
  static getComment(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.comments, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 识别语种
   */
  static getLanguageType(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.language, data, { headers: { 'Content-Type': 'application/json', all_department } });
  }

  /**
   * 翻译
   */
  static transLanguage(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.trans, data, { headers: { 'Content-Type': 'application/json', all_department } });
  }

  /**
   * 上传文件，进行翻译
   * */
  static uploadFile2translate(formData) {
    return http({
      url: URL.netResource.article.uploadFile2translate,
      headers: { 'Content-Type': 'multipart/form-data' },
      method: 'POST',
      data: formData
    });
  }

  /**
   * 文章来源列表
   */
  // static getPlatform() {
  //   getAllDepartment();
  //   return http.get(URL.netResource.article.platform, { headers: { 'all-department': all_department } });
  // }

  static analysisEmotion(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.calcu_text, data, { headers: { 'all-department': all_department } });
  }

  /* 政治倾向分析 */
  static standpointAnalysis(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.standpoint_analysis, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 删除网络文章
   */
  static delNetArticle(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.del, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 删除译文
   */
  static delTransArticle(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.deltrans, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取译文评论
   */
  static getTransComment(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.transComments, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 网络资源文章添加至系统资源
   */
  static addSysArticle(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.addSysArticle, data);
  }

  /**
   * 网络资源地区的获取
   */
  static getLocations(data) {
    getAllDepartment();
    return http.post(URL.netResource.article.getLocations, data);
  }

  /**
   * 网络资源D导出文章
   */
  static exportArticle(filter) {
    return http.post(URL.netResource.article.exportArticle, filter, { responseType: 'blob' });
  }

  /**
   * 获取标注文章
   */
  static getArticleData(data) {
    return http.post(URL.netResource.article.getArticleData, data);
  }

  /**
   * 获取某一篇标注文章
   */
  static getEditArticleData(data) {
    return http.post(URL.netResource.article.getEditArticleData, data);
  }

  /* 获取标注音视图 */
  static getWaitMarkFile(data) {
    return http.post(URL.netResource.article.getWaitMarkFile, data);
  }

  /* 获取某一个注音视图 */
  static getEditMarkFile(data) {
    return http.post(URL.netResource.article.getEditMarkFile, data);
  }

  /**
   * 标注tagList
   */
  static getTagList(data) {
    return http.post(URL.netResource.article.getTagList, data);
  }

  /**
   * 标注
   */
  static addMark(data) {
    return http.post(URL.netResource.article.addMark, data);
  }

  /**
   * 获取文章话题
   */
  static getArticleTopic(data) {
    return http.post(URL.netResource.article.getArticleTopic, data);
  }
}

/**
 * 网络账号资源
 */
export class NetAccountAPI {
  static getNewList(data) {
    getAllDepartment();
    return http.post(URL.netResource.account.getNewList, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 网络资源-获取账号列表
   * @param {object} data 筛选条件
   */
  static getList(data) {
    getAllDepartment();
    return http.post(URL.netResource.account.list, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 任务中心-获取查询条件平台
   * @param {object} data 筛选条件
   */
  static allPlatform(params) {
    getAllDepartment();
    return http.get(URL.netResource.account.allPlatform, { params }, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取粉丝数据列表
   * @param {object} data 筛选条件
   */
  static getfollowerUserList(data) {
    getAllDepartment();
    return http.post(URL.netResource.account.followerUserList, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 判断是否有分析数据
   * @param {*} data 账号
   */
  static checkUser(data) {
    getAllDepartment();
    return http.post(URL.netResource.account.check, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 判断是否被监控
   * @param {*} data 账号
   */
  static exist(data) {
    getAllDepartment();
    return http.post(URL.netResource.account.exist, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 判断是否有分析数据（自身接口）
   * @param {*} data 账号
   */
  static personInfo(data) {
    getAllDepartment();
    return http.post(URL.monitor.monitorPersonInfo, data, { headers: { 'all-department': all_department } });
  }

  /* 获取培育平台列表 */
  static getBreedPlatformList() {
    getAllDepartment();
    return http.get(URL.netResource.account.breedPlatform, { headers: { 'all-department': all_department } });
  }

  static getPlatformList() {
    getAllDepartment();
    return http.post(URL.netResource.account.platform);
  }

  static deleteAccount(data) {
    getAllDepartment();
    return http.post(URL.netResource.account.delete, { data }, { headers: { 'all-department': all_department } });
  }

  static getMonitorPlatformList() {
    getAllDepartment();
    return http.get(URL.netResource.account.monitorPlatform, { headers: { 'all-department': all_department } });
  }

  /* 获取zalo消息列表 */
  static getChatList(data) {
    return http.post(URL.netResource.account.chatList, data);
  }

  /* 获取zalo消息详情列表*/
  static getChatDetailList(data) {
    return http.post(URL.netResource.account.chatDetailList, data);
  }

  /* 入库*/
  static addTask(data) {
    return http.post(URL.netResource.account.addTask, data);
  }

  /* 账号添加至策略目标库或培育目标库*/
  static accountAddition(data) {
    return http.post(URL.netResource.account.accountAddition, data);
  }

  /* 主页添加至策略目标库或培育目标库*/
  static homePageAddition(data) {
    return http.post(URL.netResource.account.homePageAddition, data);
  }

  /* 文章添加至策略目标库或培育目标库*/
  static articleAddition(data) {
    return http.post(URL.netResource.account.articleAddition, data);
  }

  /* 文章添加至素材库*/
  static articleAdditionMaterial(data) {
    return http.post(URL.netResource.account.articleAdditionMaterial, data);
  }

  /* 评论添加至素材库*/
  static commentAdditionMaterial(data) {
    return http.post(URL.netResource.account.commentAdditionMaterial, data);
  }

  /* 视频添加至素材库*/
  static fileAdditionMaterial(data) {
    return http.post(URL.netResource.account.fileAdditionMaterial, data);
  }

  /* 社群添加至策略目标库或培育目标库*/

  static groupAdditionMaterial(data) {
    return http.post(URL.netResource.account.groupAdditionMaterial, data);
  }

  /* 群组添加至策略目标库或培育目标库*/

  static boardAdditionMaterial(data) {
    return http.post(URL.netResource.account.boardAdditionMaterial, data);
  }

  /* 话题添加至策略目标库或培育目标库*/

  static topicAdditionMaterial(data) {
    return http.post(URL.netResource.account.topicAdditionMaterial, data);
  }

  /* 导出*/
  static exportAccount(data) {
    return http.post(URL.netResource.account.exportAccount, data, { responseType: 'blob' });
  }

  /* 人物关系聚合*/
  static getUserPortraitCount(data) {
    return http.post(URL.netResource.account.getUserPortraitCount, data);
  }

  /* 人物关系列表*/
  static getUserPortrait(data) {
    return http.post(URL.netResource.account.getUserPortrait, data);
  }

  /* 共同群组*/
  static getUserGroup(data) {
    return http.post(URL.netResource.account.getUserGroup, data);
  }

  /* 点赞或转发记录*/
  static getUserCommon(data) {
    return http.post(URL.netResource.account.getUserCommon, data);
  }

  /* 所以互动记录数量*/
  static getUserCommonTotal(data) {
    return http.post(URL.netResource.account.getUserCommonTotal, data);
  }

  /* 评论记录*/
  static getUserComment(data) {
    return http.post(URL.netResource.account.getUserComment, data);
  }

  /* 私信记录*/
  static getMessage(data) {
    return http.post(URL.netResource.account.getMessage, data);
  }

  /* 目标库账号详情*/
  static getUserDetail(data) {
    return http.post(URL.netResource.account.getUserDetail, data);
  }

  /* 目标库账号详情*/
  static getMainPageDetail(data) {
    return http.post(URL.netResource.account.getMainPageDetail, data);
  }

  /* 目标库群组详情*/
  static getGroupDetail(data) {
    return http.post(URL.netResource.account.getGroupDetail, data);
  }

  /* 目标库话题详情*/
  static getTopicDetail(data) {
    return http.post(URL.netResource.account.getTopicDetail, data);
  }

  /* 目标库社群详情*/
  static getAssociationDetail(data) {
    return http.post(URL.netResource.account.getAssociationDetail, data);
  }

  /* 账号主页详情获取分析状态*/
  static getTask(data) {
    return http.post(URL.netResource.account.getTask, data);
  }

  /* 账号主页详情一键分析*/
  static createTask(data) {
    return http.post(URL.netResource.account.createTask, data);
  }

  /* 账号主页详情重试*/
  static retryTask(data) {
    return http.post(URL.netResource.account.retryTask, data);
  }
}

/**
 * 网络网页资源
 */
export class WebApi {
  /**
     * 网络资源-获取网页列表
     * @param {object} data 筛选条件
     */
  static getWebList(data) {
    return http.post(URL.netResource.web.webList, data);
  }
}
export const CollectReport = {
  getDataLocation() {
    getAllDepartment();
    return http.post(URL.netResource.CollectReport.DataLocation, { headers: { 'all-department': all_department } });
  },
  getDataStatistics(data) {
    getAllDepartment();
    return http.post(URL.netResource.CollectReport.DataStatistics, data, { headers: { 'all-department': all_department } });
  },
  getDataType() {
    getAllDepartment();
    return http.post(URL.netResource.CollectReport.DataType, { headers: { 'all-department': all_department } });
  },
  getDataToday() {
    getAllDepartment();
    return http.post(URL.netResource.CollectReport.DataToday, { headers: { 'all-department': all_department } });
  },
  getTopic(data) {
    getAllDepartment();
    return http.post(URL.netResource.CollectReport.Topic, data, { headers: { 'all-department': all_department } });
  },

  /* 获取网络资源 采集报表 爬虫封锁次数 */
  getBlockTimes(params) {
    return http.get(URL.netResource.CollectReport.BlockTimes, { params });
  },

  /* 获取网络资源 采集报表 爬虫封锁记录 */
  getBlockRecord(data) {
    return http.post(URL.netResource.CollectReport.BlockRecord, data);
  },

  /* 获取网络资源 采集报表 爬虫封锁记 */
  fetchBlockRecordPlatformList() {
    return http.get(URL.netResource.CollectReport.BlockRecordPlatformList);
  }

};

/**
 * 榜样用户
 */
export class ExampleAPI {
  /**
   * 榜样用户-获取榜样类别列表
   * @param {object} data 筛选条件
   */
  static getTypeList(data) {
    return http.post(URL.netResource.example.list, { ...data, gatherType: 'EXAMPLE' });
  }

  static addType(data) {
    return http.post(URL.netResource.example.add, data);
  }

  static deleteInvalid(data) {
    return http.post(URL.netResource.example.delete_invalid, data);
  }

  static deleteType(data) {
    return http.post(URL.netResource.example.delete, data);
  }

  static renameType(data) {
    return http.post(URL.netResource.example.rename, data);
  }

  /**
   * 网络资源-获取特定平台榜样用户
   * @param {object} data 筛选条件
   */
  static getExampleList(data) {
    getAllDepartment();
    return http.post(URL.netResource.example.exampleList, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 某类型榜样用户的统计信息
   * @param {*} params
   */
  static getMsg(data) {
    return http.post(URL.netResource.example.msg, data);
  }

  /**
   * 筛选成员的可用条件
   * @param {*} params
   */
  // static getSearch() {
  //   getAllDepartment();
  //   return http.get(URL.netResource.example.search, { headers: { 'all-department': all_department } });
  // }

  /**
   * 某类型榜样用户的用户列表
   * @param {*} params
   */
  static getUser(data) {
    getAllDepartment();
    return http.post(URL.netResource.example.member, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 创建培育任务时获取可用榜样用户人数
   */
  static getAvailable(data) {
    return http.post(URL.netResource.example.available, data);
  }

  /**
   * 新增榜样用户
   * @param {*} data
   */
  static addUser(data) {
    getAllDepartment();
    return http.post(URL.netResource.example.user, data);
  }

  /**
   * 删除榜样用户
   * @param {*} data
   */
  static deleteUser(data) {
    // getAllDepartment();
    return http.delete(URL.netResource.example.user, { data });
  }
}

/**
 * 评论管理
 */
export class CommentAPI {
  /**
   * 网络资源评论添加至系统资源
   * @param data 筛选条件
   */
  static addSysComment(data) {
    getAllDepartment();
    return http.post(URL.netResource.comment.addSysComment, data);
  }

  /**
   * 获取列表
   * @param {object} data 筛选条件
   */
  static getList(data) {
    getAllDepartment();
    return http.post(URL.netResource.comment.list, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 评论来源列表
   */
  static getPlatform() {
    getAllDepartment();
    return http.post(URL.netResource.comment.platform, { headers: { 'all-department': all_department } });
  }

  /**
   * 删除评论
   */
  static delNetComment(data) {
    getAllDepartment();
    return http.post(URL.netResource.comment.del, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 删除译文评论
   */
  static delTransComment(data) {
    getAllDepartment();
    return http.post(URL.netResource.comment.deltrans, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 获取翻译评论列表
   * @param {object} data 筛选条件
   */
  static getTransList(data) {
    getAllDepartment();
    return http.post(URL.netResource.comment.transList, data, { headers: { 'all-department': all_department } });
  }

  /**
   * 网络资源D导出评论
   */
  static exportComment(filter) {
    return http.post(URL.netResource.comment.exportComment, filter, { responseType: 'blob' });
  }
}

/**
 * 群组管理
 */
export class GroupAPI {
  // 标签下拉列表
  static tagList(params) {
    return http.get(URL.netResource.group.tagList, { params });
  }

  static groupList(data) {
    return http.post(URL.netResource.group.queryGroupList, data);
  }

  // 编辑群组
  static editGroup(data) {
    const id = data.id;
    delete data.id;
    return http({
      url: URL.netResource.group.editGroup(id),
      method: 'PATCH',
      data
    });
  }

  static addGroup(data) {
    return http.post(URL.netResource.group.addGroup, data);
  }

  static batchAddGroup(formData) {
    const config = { headers: { 'Content-Type': 'multipart/form-data' } };
    return http({
      url: URL.netResource.group.batchAddGroup,
      method: 'POST',
      ...config,
      data: formData
    });
  }

  static deleteGroup(data) {
    return http({
      url: URL.netResource.group.deleteGroup,
      method: 'DELETE',
      data
    });
  }
}

/**
 * 群组消息管理
 */
export class GroupMsgAPI {
  /**
   * 获取群组列表
   * @param {object} data 筛选条件
   */
  static getGroupListPlatforms() {
    return http.post(URL.netResource.groupMsg.filterPlatform);
  }

  /**
   * 获取群组列表
   * @param {object} data 筛选条件
   */
  static getGroupList(data) {
    return http.post(URL.netResource.groupMsg.list, data);
  }

  /**
   * 获取群组成员列表
   * @param {object} data 筛选条件
   */
  static getGroupUsersList(data) {
    return http.post(URL.netResource.groupMsg.groupUsers, data);
  }

  /**
   * 获取聊天内容列表
   * @param {object} data 筛选条件
   */
  static getGroupMessageList(data) {
    return http.post(URL.netResource.groupMsg.groupMessageList, data);
  }

  /**
   * 获取聊天内容关键词
   * @param {object} data 筛选条件
   */
  static getHotWordList(data) {
    return http.post(URL.netResource.groupMsg.messageHotWord, data);
  }

  /**
   * 获取成员加入的所有群组
   * @param {object} data 筛选条件
   */
  static getUserGroupsList(data) {
    return http.post(URL.netResource.groupMsg.userGroups, data);
  }

  /**
   * 获取成员详情
   * @param {object} data 筛选条件
   */
  static getGroupUserDetail(data) {
    return http.post(URL.netResource.groupMsg.groupUserDetail, data);
  }

  /**
   * 获取聊天内容-链接
   * @param {object} data 筛选条件
   */
  static getMessageLinksList(data) {
    return http.post(URL.netResource.groupMsg.messageLinks, data);
  }

  /**
   *  facebook群组和reddit版块
   */
  static getBoardList(data) {
    return http.post(URL.netResource.groupMsg.getBoardList, data);
  }

  /**
   *  facebook群组和reddit版块-详情
   */
  static getBoardDetail(data) {
    return http.post(URL.netResource.groupMsg.getBoardDetail, data);
  }

  /** 根据平台url获取业务id */
  static getPlatformIdByUrl(data) {
    return http.post(URL.netResource.groupMsg.getPlatformIdByUrl, data);
  }

  /** 导出群组*/
  static exportGroup(data) {
    return http.post(URL.netResource.groupMsg.exportGroup, data, { responseType: 'blob' });
  }
}

/**
 * 审核管理
 */
export class AuditManageAPI {
  /**
   * 批量审核群组
   */
  static auditGroup(data) {
    return http.post(URL.netResource.audit.auditGroup, data);
  }

  /**
   * 批量审核账号、主页
   */
  static auditUser(data) {
    return http.post(URL.netResource.audit.auditUser, data);
  }

  /**
   * 批量审核账号
   */
  static auditArticle(data) {
    return http.post(URL.netResource.audit.auditArticle, data);
  }

  /**
   * 获取群组列表
   */
  static auditGroupList(data) {
    return http.post(URL.netResource.audit.auditGroupList, data);
  }

  /**
   * 过去账号、主页列表
   */
  static auditUserList(data) {
    return http.post(URL.netResource.audit.auditUserList, data);
  }

  /**
   * 文章列表
   */
  static auditArticleList(data) {
    return http.post(URL.netResource.audit.auditArticleList, data);
  }

  /**
   * 话题列表
   */
  static auditTopicList(data) {
    return http.post(URL.netResource.audit.auditTopicList, data);
  }

  /**
   * 审核话题
   */
  static auditTopic(data) {
    return http.post(URL.netResource.audit.auditTopic, data);
  }
}

/**
 * 账号主页社群-导入
 */
export class PubImportAPI {
  // 导入
  static batchImport(file, markTag, professionTag, baseType, type) {
    const form = new FormData();
    form.append('markTag', markTag);
    form.append('professionTag', professionTag);
    form.append('baseTypes', baseType);
    form.append('file', file);
    if (type === 'user_info') {
      return http.post(URL.breedTarget.account.fileImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
    } else if (type === 'main_page') {
      return http.post(URL.breedTarget.homePage.fileImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
    } else if (type === 'group') {
      return http.post(URL.breedTarget.group.fileImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
    } else if (type === 'cluster') {
      return http.post(URL.breedTarget.cluster.fileImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
    } else if (type === 'topic') {
      return http.post(URL.breedTarget.topic.fileImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
    }
  }

  // 导出
  static groupExport(data) {
    return http.post(URL.breedTarget.group.export, data, { responseType: 'blob' });
  }

  // 话题导出
  static topicExport(data) {
    return http.post(URL.breedTarget.topic.export, data, { responseType: 'blob' });
  }
}

/**
 * 培育目标库、策略目标库，账号
 */

export class breedTargetAccountAPI {
  // 账号列表
  static accountList(data) {
    return http.post(URL.breedTarget.account.list, data);
  }

  // 分页查询账号列表
  static accountListPage(data) {
    return http.post(URL.breedTarget.account.listPage, data);
  }

  // 账号批量删除
  static accountDelete(data) {
    return http.post(URL.breedTarget.account.delete, data);
  }

  // 账号批量编辑
  static accountEdit(data) {
    return http.post(URL.breedTarget.account.edit, data);
  }

  // 账号批量导出
  static accountExport(data) {
    return http.post(URL.breedTarget.account.export, data, { responseType: 'blob' });
  }

  // 主页列表
  static homePageList(data) {
    return http.post(URL.breedTarget.homePage.list, data);
  }

  // 分页查询主页列表
  static homePageListPage(data) {
    return http.post(URL.breedTarget.homePage.listPage, data);
  }

  // 主页批量删除
  static homePageDelete(data) {
    return http.post(URL.breedTarget.homePage.delete, data);
  }

  // 主页批量编辑
  static homePageEdit(data) {
    return http.post(URL.breedTarget.homePage.edit, data);
  }

  // 主页批量导出
  static homePageExport(data) {
    return http.post(URL.breedTarget.homePage.export, data, { responseType: 'blob' });
  }

  // 群组列表
  static clusterList(data) {
    return http.post(URL.breedTarget.cluster.list, data);
  }

  // 分页群组列表
  static clusterListPage(data) {
    return http.post(URL.breedTarget.cluster.listPage, data);
  }

  // 群组批量删除
  static clusterDelete(data) {
    return http.post(URL.breedTarget.cluster.delete, data);
  }

  // 群组批量编辑
  static clusterEdit(data) {
    return http.post(URL.breedTarget.cluster.edit, data);
  }

  // 群组批量导出
  static clusterExport(data) {
    return http.post(URL.breedTarget.cluster.export, data, { responseType: 'blob' });
  }

  // 文章批量导出
  static postExport(data) {
    return http.post(URL.breedTarget.post.export, data, { responseType: 'blob' });
  }

  // 话题列表
  static topicList(data) {
    return http.post(URL.breedTarget.topic.list, data);
  }

  // 话题批量删除
  static topicDelete(data) {
    return http.post(URL.breedTarget.topic.delete, data);
  }

  // 话题批量编辑
  static topicEdit(data) {
    return http.post(URL.breedTarget.topic.edit, data);
  }

  // 目标库创始人列表
  static getCreator(data) {
    return http.post(URL.breedTarget.getCreator, data);
  }

  // 获取目标库tab
  static listConfig(param) {
    return http.get(URL.breedTarget.listConfig, param);
  }
}

export class MaterialAPI {
  // 文章批量导出
  static postExport(data) {
    return http.post(URL.material.post.export, data, { responseType: 'blob' });
  }

  // 评论批量导出
  static commentExport(data) {
    return http.post(URL.material.comment.export, data, { responseType: 'blob' });
  }

  // 素材库创始人集合
  static getCreator(data) {
    return http.post(URL.material.getCreator, data);
  }
}

/**
 * 账号主页社群-导入
 */
export class ModelCompletionAPI {
  // 导入
  static getModelCompletionList(data) {
    return http.post(URL.modelCompletion.list, data);
  }
}
