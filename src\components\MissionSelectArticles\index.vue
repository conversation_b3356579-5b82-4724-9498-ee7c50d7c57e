<template>
  <!-- 目标帖文、查找素材库文章 -->
  <titan-dialog
    v-if="showDialog"
    :title="title"
    :visible.sync="showDialog"
    class="comment-dialog"
    destroy-on-close
    width="70%"
    height="500px"
    top="50px"
    :config="{
      showCancel: false
    }"
    @cancel="handleClose"
  >
    <el-tabs
      v-model="active"
      stretch
      @tab-click="handleClickTab"
    >
      <el-tab-pane
        v-for="item in schemaNames"
        :key="item.value"
        :label="item.label"
        :name="item.value"
      />
    </el-tabs>
    <article-popover
      v-if="active === 'Optional'"
      ref="Optional"
      :platform="platform"
      :active-tab="active"
      :radio="radio"
      :page-type="pageType"
      :selected="selectedTableData"
      :content-max-length="contentMaxLength"
      :title-max-length="titleMaxLength"
      :title-min-length="titleMinLength"
      :number-limit="numberLimit"
      @selected="handleSelected"
      @cancel="handleCancel"
      @selectedSuccess="handleSelectedSuccess"
    />
    <selected-article-popover
      v-if="active === 'Selected'"
      ref="Selected"
      :show-title="showTitleBatch"
      :content-max-length="contentMaxLength"
      :title-max-length="titleMaxLength"
      :title-min-length="titleMinLength"
      :is-readonly="isReadonly"
      :platform="platform"
      :selected-table-data.sync="form.content_json_list"
    />
  </titan-dialog>
</template>

<script>
import { PostConfigMap } from '@/assets/libs/platform';
import SelectedArticlePopover from '@/schema/custom/SelectedArticlePopover';
import ArticlePopover from '@/schema/custom/ArticlePopover';

export default {
  name: 'MissionSelectArticles',
  components: { ArticlePopover, SelectedArticlePopover },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => ({})
    },
    // single-单选，batch-多选
    radio: {
      type: String,
      default: ''
    },
    // aim-目标库，material-素材库
    pageType: {
      type: String,
      default: 'aim'
    },
    selected: {
      type: Array,
      default: () => ([])
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      default: ''
    },
    numberLimit: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      active: 'Optional',
      selectedTableData: []
    };
  },
  computed: {
    title() {
      let str = '';
      switch (this.pageType) {
        case 'aim':
          str = this.$t('mission.查找策略目标库');
          break;
        case 'breed':
          str = this.$t('mission.查找培育目标库');
          break;
        case 'material':
          str = this.$t('mission.查找素材库');
          break;
      }
      return str;
    },
    showDialog: {
      set(val) {
        this.$emit('update:visible', val);
      },
      get() {
        return this.visible;
      }
    },
    schemaNames() {
      if (this.form.post_type === 'single' || this.pageType === 'aim') {
        return [{ label: this.$t('schema.可选帖文'), value: 'Optional' }];
      }
      if (this.isReadonly) {
        return [
          {
            label: `${this.$t('schema.已选帖文')}(${this.selectedTableData.length || 0})`,
            value: 'Selected'
          }
        ];
      }
      return [
        { label: this.$t('schema.可选帖文'), value: 'Optional' },
        {
          label: `${this.$t('schema.已选帖文')}(${this.selectedTableData.length || 0})`,
          value: 'Selected'
        }
      ];
    },
    showTitleBatch() {
      return (
        this.platform === 'pinterest' ||
        (PostConfigMap[this.platform].text &&
          !PostConfigMap[this.platform].text.hideTitle)
      );
    },
    contentMaxLength() {
      return PostConfigMap[this.platform].text.maxLength;
    },
    titleMaxLength() {
      return PostConfigMap[this.platform]?.text?.titleMaxLength;
    },
    titleMinLength() {
      return PostConfigMap[this.platform]?.text?.titleMinLength;
    }
  },
  watch: {
    form: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val?.content_json_list?.length) {
          this.selectedTableData = [...val.content_json_list];
        } else {
          this.selectedTableData = [];
        }
      }
    }
  },
  methods: {
    handleCancel() {
      this.showDialog = false;
    },
    handleClose() {
      this.active = 'Optional';
    },
    handleClickTab: _.debounce(function() {
      if (this.$refs['Optional'] && this.$refs['Optional'].length > 0) {
        if (this.active === 'Optional') {
          this.$refs['Optional'][0].fetchArticleList();
        }
      }
    }),
    handleSelected(val) {
      this.showDialog = false;
      this.$emit('handleSelected', val);
      // const { title, content } = val;
      // this.form.title = this.showTitle ? title : '';
      // this.form.content = content;
    },
    handleSelectedSuccess(val) {
      // val.forEach(item => {
      //   if (this.selectedTableData.findIndex(i => i.docId === item.docId) === -1) {
      //     this.selectedTableData.push(item);
      //   }
      // });
      this.selectedTableData = this.selectedTableData.concat(val);
      this.$emit('handleSelectedSuccess', this.selectedTableData);
    },
    handleClickSee() {
      this.showDialog = true;
      this.active = 'Selected';
    }
  }
};
</script>

<style lang="scss" scoped>
.comment-dialog {
  // ::v-deep .el-dialog__header {
  //   padding-top: 0;
  // }
  ::v-deep .el-dialog__body {
    padding-top: 0;
  }
}
.el-tabs {
  ::v-deep .el-tabs__header {
    width: 90%;
  }
}
::v-deep .el-tabs__nav-wrap {
  max-width: 300px;
  .el-tabs__item {
    font-size: 18px;
    max-width: 160px;
  }
  &::after {
    max-width: 300px;
    width: inherit;
  }
}
</style>

