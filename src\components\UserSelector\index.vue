<template>
  <div class="user-selector-container">
    <filter-search
      ref="filter"
      :search-params="searchParams"
      :check-all.sync="checkAll"
      :check-indeterminate="checkIndeterminate"
      :hide-items="['createDate', 'exported']"
      :selected-list="selectedList"
      @update="updateFilter"
      @checked="handleFilterChecked"
    />

    <template v-if="list && list.length > 0">
      <div
        v-loading="user_list_loading"
        class="person-list"
        :style="{
          'height': _height[0]*10 + '%'
        }"
      >
        <person-card
          v-for="item in list"
          :key="item.id"
          :person="item"
          :batch-bool="true"
          :is-del-account="true"
          :selected="selectedIds.includes(item.id)"
          @change="handleSelectUser"
        />
      </div>
      <!-- 分页器 -->
      <pagination
        class="user-pagination"
        :total="total"
        :page.sync="page"
        :limit.sync="size"
        layout="slot,->,prev,pager,next"
        @pagination="changePagination"
      >
        <span>{{ $t('components. 已选择了') }}：{{ selectedList.length }} {{ $t('components. 个用户') }}</span>
      </pagination>
    </template>

    <div
      v-else
      class="empty-warp"
    >
      <span v-show="!user_list_loading">{{ $t('components. 没有符合条件的数据') }}</span>
      <span v-show="user_list_loading">
        <i class="el-icon-loading" />  {{ $t('components. 加载中···') }}
      </span>
    </div>

    <selected-person
      v-if="showSelectedArea"
      :list="selectedList"
      :height="_height[1]*10 + '%' "
      @cancel="handleCancelUser"
      @removeAll="handleRemoveAllUser"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import FilterSearch from './components/FilterSearch';
import PersonCard from './components/PersonCard';
import Pagination from '@/components/Pagination';
import SelectedPerson from './components/selectedPerson';
import { clearCloneForm } from '@/utils';
import { SysAccountAPI } from '@/api/modules/character';

const __watch_select = function(val) {
  const length = this.selectedLimit === 0
    ? this.list.length
    : this.selectedLimit < this.list.length
      ? this.selectedLimit
      : this.list.length;

  // 获取当前页被选中的项
  const current_page_selected = val.filter(item => this.list.find(a => a.id === item.id));
  if (current_page_selected.length === 0) {
    this.checkAll = false;
    this.checkIndeterminate = false;
  } else if (current_page_selected.length > 0 && current_page_selected.length < length) {
    this.checkAll = false;
    this.checkIndeterminate = true;
  } else if (current_page_selected.length >= length) {
    this.checkAll = true;
    this.checkIndeterminate = false;
  }
};
export default {
  name: 'UserSelector',
  components: {
    FilterSearch,
    PersonCard,
    Pagination,
    SelectedPerson
  },
  props: {
    selectedLimit: {
      type: Number,
      default: 0,
      info: '最多可选择的用户数 (0 为不设上限)'
    },
    selected: {
      type: Array,
      default: () => []
    },
    searchParams: {
      type: Object,
      default: () => ({})
    },
    showSelectedArea: {
      type: Boolean,
      default: true
    },
    selectHeightRadio: {
      type: String,
      info: '账号选择区域与已选账号展示区域的高度比例',
      default: '7:3'
    }
  },
  data() {
    return {
      list: [],
      selectedList: [], // 选中的人
      page: 1,
      size: 50,
      total: 0,
      filter: {},
      user_list_loading: false,
      checkAll: false,
      checkIndeterminate: false
    };
  },
  computed: {
    ...mapState('crossTag', {
      accountSimTagDict(state) {
        return state['自主创建账号'];
      }
    }),
    selectedIds() {
      if (_.isEmpty(this.selectedList)) return [];
      return this.selectedList.map(a => a.id);
    },
    _height() {
      return this.selectHeightRadio.split(':');
    },
    ...mapGetters(['isManageByPermission']),

    /* 查看自主创建账号的权限 */
    isWatchAccountList() {
      return this.isManageByPermission(this.$qx['virtual_account_list_watch']);
    }
  },
  watch: {
    selected: {
      handler(val) {
        this.selectedList = _.unionBy(val, 'id');
      },
      immediate: true
    },
    selectedList(val) {
      __watch_select.call(this, val);
    },
    list() {
      __watch_select.call(this, this.selectedList);
    }
  },
  created() {
    this.refreshTagStore('角色培育通用');
    this.refreshTagStore('职业通用');
  },
  methods: {
    ...mapActions('tag', ['refreshTagStore']),
    handleFilterChecked(status) {
      if (status) {
        const list = !this.selectedLimit || this.selectedLimit === 0
          ? this.list.slice(0)
          : this.list.slice(0, this.selectedLimit);

        const add = list.map(a => ({
          id: a.id,
          account: a.account,
          status: a.status,
          platform: a.platform,
          account_photo: a.account_photo,
          extra_info: a.extra_info
        }));
        this.selectedList = _.uniqBy(this.selectedList.concat(add), 'id');
      } else {
        this.selectedList = this.selectedList.filter(item => !this.list.find(a => a.id === item.id));
      }
    },
    updateFilter(val) {
      this.page = 1;
      this.total = 0;
      this.filter = clearCloneForm(val);
      this.fetchUserList();
    },
    async fetchUserList() {
      let apiRoute = '';
      if (_.isNumber(this.filter.env)) {
        apiRoute = 'getSpareAccountList';
      } else {
        apiRoute = 'getList';
      }
      this.user_list_loading = true;
      try {
        const query = {
          ...this.filter,
          page_index: this.page,
          page_size: this.size
        };

        /* 没有自主创建账号列表的权限 就用接受账号的列表的账号 */
        if (!this.isWatchAccountList) {
          query['own_redistribution_only'] = true;
        }

        if (!query.accountDistribution) {
          query.redistributed_only = false;
          query.non_redistributed_only = false;
        } else if (query.accountDistribution === 1) {
          query.redistributed_only = true;
          query.non_redistributed_only = false;
        } else if (query.accountDistribution === 3) {
          query.non_redistributed_only = false;
          query.redistributed_only = false;
        } else {
          query.redistributed_only = false;
          query.non_redistributed_only = true;
        }

        const { data, total } = await SysAccountAPI[apiRoute](query);
        this.list = _.isEmpty(data) ? [] : data.map(item => {
          item.m_simple_tag = item.simple_tag.filter(id => this.accountSimTagDict[id]).map(id => this.accountSimTagDict[id]);
          if (item.m_simple_tag && item.m_simple_tag.length > 0) {
            item.m_simple_tag_tooltip = item.m_simple_tag.map(a => a.name).join('、');
          }
          return item;
        });
        this.total = total;
      } catch (err) {
        console.error(err);
      } finally {
        this.user_list_loading = false;
      }
    },
    handleSelectUser(isChoose, info) {
      if (isChoose) {
        if (this.selectedLimit && this.selectedList.length === this.selectedLimit) {
          this.$message(`${this.$t('components[" 每次只能选择"]')}${this.selectedLimit}${this.$t('components["个"]')}`);
        } else {
          this.selectedList.push({
            id: info.id,
            account: info.account,
            status: info.status,
            platform: info.platform,
            account_photo: info.account_photo,
            account_id: info.account_id
          });
        }
      } else {
        const index = this.selectedList.findIndex(item => item.id === info.id);
        index > -1 && this.selectedList.splice(index, 1);
      }
    },
    changePagination() {
      this.list = [];
      this.fetchUserList();
    },
    handleCancelUser(item) {
      const index = this.selectedList.findIndex(a => a.id === item.id);
      if (index !== -1) this.selectedList.splice(index, 1);
    },
    handleRemoveAllUser() {
      this.selectedList = [];
    }
  }
};
</script>

<style scoped lang="scss">
.user-selector-container{
  margin-top: -20px;
  position: relative;
  height: 750px;
  display: flex;
  flex-direction: column;
  .person-list{
    flex-grow: 1;
    overflow-y: auto;
    @include gird-auto(360px , 210px, 20px, 20px)
  }
  .empty-warp{
    flex-grow: 1;
    height: 70%;
    color: #888282;
    text-align: center;
    span{
      display: block;
      font-size: 20px;
      transform: translateY(200px);
      i{
        margin-right: 10px;
      }
    }
  }
  .user-pagination{
    box-shadow: none;
  }
}
</style>
