<template>
  <div
    ref="virtualScroll"
    class="virtual-list"
  >
    <virtual-list
      ref="virtualList"
      v-infinite-scroll="handleLoadData"
      class="virtual-list__scroll"
      :size="cardHeight"
      :remain="columnCount"
      :style="{ overflowY: 'scroll', height: height + 'px' }"
      :infinite-scroll-disabled="isLoadedAll"
      :infinite-scroll-distance="200"
    >
      <section
        v-for="(cards, idx) in calcList"
        :key="idx"
        class="virtual-list__row"
      >
        <template
          v-for="card in cards"
        >
          <slot :card="card" />
        </template>
        <i
          v-for="i in rowCount - cards.length"
          :key="i"
          class="placement"
          :style="{ width: `${cardWidth}px`,margin: cardMargin }"
        />
      </section>
      <!-- <div
        v-show="isLoadingMore"
        class="virtual-list__loading"
      >
        加载中...
      </div>
      <div
        v-show="hasLoadAll"
        class="virtual-list__end"
      >
        没有更多数据了
      </div> -->
    </virtual-list>
  </div>
</template>

<script>
import { isEmpty } from '@/utils';
import VirtualList from 'vue-virtual-scroll-list';
export default {
  name: 'VirtualInfiniteScroll',

  components: { VirtualList },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    isLoadedAll: {
      type: Boolean,
      default: false
    },
    isLoadingMore: {
      type: Boolean,
      default: false
    },
    cardHeight: {
      type: Number,
      default: 0
    },
    cardWidth: {
      type: Number,
      default: 0
    },
    cardGap: {
      type: Number,
      default: 0
    },
    cardMargin: {
      type: String,
      default: '0'
    },
    layout: {
      type: String,
      default: 'card',
      validator: function(value) {
        const params = ['card', 'normal'];
        if (params.includes(value)) {
          return true;
        } else {
          console.error(`prop of layout should be like ${params}`);
          return false;
        }
      }
    }
  },
  data() {
    return {
      height: 696,
      rowWidth: 1820
    };
  },
  computed: {
    hasLoadAll() {
      return !!this.list.length && this.isLoadedAll;
    },
    calcList() {
      const res = [];
      const count = this.rowCount;
      let index = 0;
      this.list.forEach((item, idx) => {
        if (!res[index]) {
          res[index] = [item];
        } else {
          res[index].push(item);
        }
        if (idx % count === count - 1) {
          index++;
        }
      });
      return res;
    },
    rowCount() {
      if (this.layout === 'normal') return 1;
      return Math.floor(this.rowWidth / (this.cardWidth + this.cardGap));
    },
    columnCount() {
      return Math.ceil(this.height / this.cardHeight);
    }
  },
  created() {
    this.initEvent();
  },
  mounted() {
    this.$nextTick(() => {
      this.resize();
    });
  },
  methods: {
    handleLoadData() {
      if (isEmpty(this.list)) {
        return;
      }
      this.$emit('load');
    },
    initEvent() {
      window.addEventListener('resize', this.resize);
      this.$once('hook:beforeDestroy', function() {
        window.removeEventListener('resize', this.resize);
      });
    },
    resize() {
      this.height = this.$refs.virtualScroll.clientHeight;
      this.rowWidth = this.$refs.virtualScroll.clientWidth;
    }
  }
};
</script>

<style lang="scss" scoped>
  .virtual-list {
    width: 100%;
    height: 100%;
    ::v-deep [role=group] {
      min-height: 100%;
      display: flex !important;
      flex-direction: column;
    }
    &__row {
      display: flex;
      justify-content: space-between;
    }
    .virtual-list__loading,
    .virtual-list__end{
      font-size: 16px;
      text-align: center;
      color: #888282;
      padding: 20px 0;
    }
  }
</style>
