<template>
  <div class="yq-step">
    <el-steps
      :active="step"
      align-center
    >
      <el-step
        v-for="(item, index) in title"
        :key="index"
        :title="item"
      />
    </el-steps>
  </div>
</template>

<script>
export default {
  name: 'YqStep',
  props: {
    title: {
      type: Array,
      default: () => []
    },
    step: {
      type: Number,
      default: 0
    }
  }
};
</script>

<style lang="scss">
.yq-step {
  width: 100%;
  .el-step {
    .el-step__head {
      .el-step__line {
        height: 6px;
        top: 10px;
        background: #c2ccdb;
        border-color: #c2ccdb;
      }
      &.is-process {
        color: #3569e7;
        border-color: #3569e7;
      }
      &.is-finish {
        color: #3569e7;
        border-color: #3569e7;
        .el-step__line {
          background: #3569e7;
          border-color: #3569e7;
        }
      }
      &.is-wait {
        color: #606266;
        border-color: #c2ccdb;
      }
    }

    .el-step__title {
      font-size: 16px;
      font-weight: normal;
      &.is-process {
        color: #3569e7;
      }
      &.is-finish {
        color: #3569e7;
      }
    }
    &:first-of-type {
      .el-step__head {
        position: relative;
        &::before {
          position: absolute;
          content: "";
          left: 0;
          top: 10px;
          right: 50%;
          height: 6px;
          border-radius: 3px 0 0 3px;
          background: #3569e7;
        }
      }
      .el-step__line {
        border-radius: 3px 0 0 3px;
      }
    }
    &:last-of-type {
      .el-step__line {
        border-radius: 0 3px 3px 0;
        display: block;
        left: 50%;
        right: 0;
      }
    }
  }
}
</style>
