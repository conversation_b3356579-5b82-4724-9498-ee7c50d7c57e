import http from '@/api/request';
import URL from '../urls';

export class SystemAPI {
  static login(data) {
    return http.post(URL.system.login, data);
  }

  static logout() {
    return http.post(URL.system.logout);
  }

  // 获取当前登录的用户
  static getCurUser() {
    return http.post(URL.system.getCurUser);
  }

  /**
   * 获取登录验证码图片
   * @param {string} captcha_id 随机UUID
   */
  static getVerificationCode(captchaId) {
    return http.get(URL.system.verificationCode, { params: { captchaId } });
  }

  /**
   * 获取系统测试验证码图片
   * @param {string} style 验证码样式,可选值 water shadow fish
   */
  static getSystemTestCaptcha(style) {
    return http.get(URL.system.testCaptcha, { params: { style } });
  }

  /**
   * 平台冷却列表
   */
  static getPersonCoolDown() {
    return http.get(URL.system.coolDown);
  }

  /**
   * 切换部门, 获取新的token
   * 切换部门之后, websocket 会推送 REFRESH 类型的消息
   * */
  static getNewToken(id) {
    return http.get(URL.system.getNewToken, { params: { newDepartmentId: id } });
  }

  /**
   * 获取登录日志
   */
  static getUserLoginLog(data) {
    return http.post(URL.system.getUserLoginLog, data);
  }
}

export class UserAPI {
  /**
   * 新增用户
   */
  static add(data) {
    return http.post(URL.system.addUser, data);
  }

  /**
   * 编辑用户
   */
  static edit(data) {
    return http.put(URL.system.editUser, data);
  }

  /**
   * 删除用户
   */
  static delete(ids) {
    const isArray = _.isArray(ids);
    return http.delete(URL.system.deleteUser, { data: isArray ? ids : [ids] });
  }

  /**
   * 获取用户列表
   */
  static getList(params) {
    return http.get(URL.system.user, { params });
  }

  /**
   * 根据单个部门id, 获取用户列表(分页)
   */
  static getUserList(data) {
    return http.post(URL.system.departUser, data);
  }

  /**
   * 查询所有用户包含猫池的账号
   */
  static getAllSystemUserList(data) {
    return http.post(URL.system.allSystemUserList, data);
  }

  /**
   * 修改当前用户密码
   */
  static editPassword(data) {
    return http.post(URL.system.editPassword, data);
  }
}

export class DepartmentAPI {
  /**
   * 获取可切换的部门列表
   */
  static getSwitchDepartList(params) {
    return http.get(URL.system.switchDepartList, { params });
  }

  /**
   * 获取可显示的部门列表
   * @param {*} data 当前用户的部门id
   */
  static getList() {
    return http.post(URL.system.depart);
  }

  /* 获取当前用户能够看到的一级部门列表*/
  static getMainList() {
    return http.post(URL.system.mainDepart);
  }

  /**
   * 重命名部门
   * @param {string} newName 新名称
   * @param {number} id 当前节点ID
   */
  static rename(data) {
    return http.put(URL.system.editDepartment, data);
  }

  /**
   * 删除单个部门
   * @param {number} id 需要删除的部门ID
   */
  static delete(id) {
    return http.delete(URL.system.deleteDepartment, { data: { id: id } });
  }

  /**
   * 创建部门
   * @param {string} newName 需要创建的部门名称
   * @param {number} pid 父节点ID，默认为1
   */
  static add(data) {
    return http.post(URL.system.addDepartment, data);
  }

  /** 根据选择的部门ids，来获取用户（不分页）*/
  static getUserListByDepIds(data) {
    return http.post(URL.system.getUserListByDepIds, data);
  }

  /* 根据当前部门，获取创建人员列表 */
  static getCreatorListByCurrentDepId(data) {
    return http.post(URL.system.getTaskCreatorListByDepId, data);
  }

  /** 根据选择的部门ids，来获取角色（不分页）*/
  static getRoleByDepIds(data) {
    return http.post(URL.system.getRoleByDepId, data);
  }
}

export class RoleAPI {
  /**
   * 新增角色
   */
  static add(data) {
    return http.post(URL.system.addRole, data);
  }

  /**
   * 获取角色列表
   */
  static getList(params) {
    return http.post(URL.system.role, params);
  }

  /* 获取对应角色的权限*/
  static getAuthByRole(params) {
    return http.post(URL.system.getMenuTreeByRoleId, params);
  }

  /**
   * 获取角色及角色相应的权限列表
   */
  static getListWithAuth(params) {
    return http.get(URL.system.roleWithAuth, { params });
  }

  /**
   * 编辑角色
   */
  static edit(data) {
    return http.put(URL.system.editRole, data);
  }

  /**
   * 删除角色
   * @param {number | array} id 需要删除的角色ID
   */
  static delete(id) {
    const isArray = Array.isArray(id);
    return http.delete(URL.system.deleteRole, { data: isArray ? id : [id] });
  }
}

export class AuthAPI {
  /**
   * 获取权限列表
   */
  static getList() {
    return http.post(URL.system.auth);
  }
}

export class SwitchAPI {
  /* 开关 */
  static getSwitch(data) {
    return http.post(URL.switch.list, data);
  }
}
