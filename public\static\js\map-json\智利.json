{"title": "Chile", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:32719"}}, "hc-transform": {"default": {"crs": "+proj=utm +zone=19 +south +datum=WGS84 +units=m +no_defs", "scale": 0.000164224477601, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": -13081.6806785, "yoffset": 8064329.73848}}, "features": [{"type": "Feature", "id": "CL.2730", "properties": {"hc-group": "admin1", "hc-middle-x": 0.54, "hc-middle-y": 0.53, "hc-key": "cl-2730", "hc-a2": "LA", "labelrank": "3", "hasc": "CL.AR", "alt-name": "IX", "woe-id": "2345021", "subregion": null, "fips": "CI04", "postal-code": null, "name": "La Araucanía", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-72.3806", "woe-name": "La Araucanía", "latitude": "-38.738", "woe-label": "Araucania Region, CL, Chile", "type": "Región"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[-780, 3927], [-793, 3938], [-796, 3954], [-779, 3942], [-780, 3927]]], [[[-143, 4027], [-142, 3993], [-132, 3948], [-126, 3938], [-103, 3919], [-100, 3903], [-110, 3886], [-108, 3873], [-128, 3856], [-146, 3856], [-169, 3841], [-185, 3838], [-197, 3826], [-216, 3815], [-226, 3801], [-229, 3775], [-224, 3754], [-221, 3716], [-224, 3686], [-236, 3674], [-232, 3660], [-249, 3632], [-239, 3623], [-244, 3608], [-261, 3607], [-270, 3612], [-280, 3641], [-295, 3652], [-306, 3649], [-334, 3585], [-346, 3573], [-362, 3568], [-395, 3567], [-411, 3575], [-429, 3569], [-443, 3586], [-468, 3599], [-521, 3607], [-560, 3604], [-563, 3612], [-557, 3656], [-561, 3664], [-578, 3667], [-601, 3659], [-624, 3659], [-619, 3672], [-623, 3698], [-636, 3746], [-656, 3791], [-675, 3855], [-672, 3829], [-679, 3841], [-696, 3900], [-698, 3913], [-668, 3928], [-659, 3928], [-646, 3913], [-641, 3924], [-647, 3944], [-646, 3957], [-631, 3972], [-606, 4013], [-608, 4024], [-623, 4038], [-618, 4082], [-603, 4106], [-604, 4117], [-615, 4134], [-605, 4152], [-585, 4148], [-555, 4158], [-523, 4157], [-480, 4148], [-472, 4126], [-443, 4096], [-413, 4084], [-383, 4056], [-359, 4061], [-342, 4050], [-309, 4052], [-300, 4043], [-306, 4034], [-299, 4026], [-293, 4002], [-284, 3994], [-275, 4003], [-242, 4020], [-226, 4040], [-206, 4049], [-185, 4047], [-152, 4034], [-143, 4027]]]]}}, {"type": "Feature", "id": "CL.BI", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.35, "hc-key": "cl-bi", "hc-a2": "BI", "labelrank": "3", "hasc": "CL.BI", "alt-name": "Bíobío|VIII", "woe-id": "2345023", "subregion": null, "fips": "CI06", "postal-code": "BI", "name": "Bío-Bío", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-72.3475", "woe-name": "Bío-Bío", "latitude": "-36.9791", "woe-label": "Biobio Region, CL, Chile", "type": "Región"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[-705, 4321], [-716, 4308], [-724, 4322], [-717, 4329], [-705, 4321]]], [[[-155, 4496], [-163, 4485], [-164, 4462], [-160, 4443], [-164, 4435], [-181, 4437], [-182, 4417], [-191, 4394], [-178, 4367], [-183, 4356], [-177, 4332], [-166, 4320], [-178, 4306], [-178, 4289], [-191, 4266], [-188, 4252], [-171, 4224], [-175, 4196], [-172, 4183], [-184, 4172], [-183, 4150], [-160, 4084], [-142, 4049], [-143, 4027], [-152, 4034], [-185, 4047], [-206, 4049], [-226, 4040], [-242, 4020], [-275, 4003], [-284, 3994], [-293, 4002], [-299, 4026], [-306, 4034], [-300, 4043], [-309, 4052], [-342, 4050], [-359, 4061], [-383, 4056], [-413, 4084], [-443, 4096], [-472, 4126], [-480, 4148], [-523, 4157], [-555, 4158], [-585, 4148], [-605, 4152], [-615, 4134], [-604, 4117], [-603, 4106], [-618, 4082], [-623, 4038], [-608, 4024], [-606, 4013], [-631, 3972], [-646, 3957], [-647, 3944], [-641, 3924], [-646, 3913], [-659, 3928], [-668, 3928], [-698, 3913], [-703, 3929], [-697, 3958], [-700, 3972], [-692, 3987], [-689, 4023], [-692, 4045], [-701, 4067], [-730, 4115], [-740, 4125], [-746, 4153], [-745, 4165], [-729, 4185], [-732, 4213], [-746, 4226], [-742, 4253], [-742, 4272], [-735, 4286], [-702, 4265], [-687, 4265], [-660, 4272], [-644, 4288], [-637, 4312], [-640, 4337], [-635, 4348], [-634, 4381], [-649, 4397], [-631, 4414], [-631, 4448], [-625, 4443], [-618, 4416], [-603, 4417], [-594, 4443], [-602, 4469], [-588, 4475], [-569, 4533], [-572, 4569], [-566, 4580], [-569, 4591], [-564, 4605], [-541, 4617], [-495, 4606], [-489, 4602], [-392, 4591], [-330, 4568], [-321, 4567], [-239, 4525], [-207, 4513], [-174, 4518], [-165, 4514], [-155, 4496]]]]}}, {"type": "Feature", "id": "CL.LL", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.22, "hc-key": "cl-ll", "hc-a2": "LL", "labelrank": "3", "hasc": "CL.LL", "alt-name": "X", "woe-id": "2345026", "subregion": null, "fips": "CI09", "postal-code": "LL", "name": "Los Lagos", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-72.90430000000001", "woe-name": "Los Lagos", "latitude": "-41.0592", "woe-label": "Los Lagos Region, CL, Chile", "type": "Región"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[-654, 2568], [-669, 2571], [-668, 2581], [-656, 2577], [-654, 2568]]], [[[-603, 2644], [-599, 2639], [-616, 2637], [-628, 2646], [-651, 2651], [-646, 2658], [-603, 2644]]], [[[-523, 2715], [-512, 2719], [-509, 2701], [-516, 2695], [-534, 2720], [-526, 2725], [-523, 2715]]], [[[-655, 2740], [-678, 2735], [-686, 2745], [-658, 2755], [-655, 2740]]], [[[-617, 2762], [-628, 2776], [-661, 2791], [-667, 2810], [-645, 2810], [-639, 2797], [-617, 2772], [-617, 2762]]], [[[-547, 2843], [-562, 2832], [-563, 2842], [-555, 2853], [-547, 2843]]], [[[-431, 2888], [-452, 2895], [-446, 2910], [-432, 2906], [-431, 2888]]], [[[-540, 2963], [-554, 2964], [-558, 2993], [-551, 2994], [-540, 2963]]], [[[-309, 3339], [-311, 3315], [-328, 3293], [-318, 3257], [-304, 3232], [-307, 3215], [-303, 3199], [-306, 3144], [-311, 3111], [-305, 3091], [-307, 3073], [-299, 3055], [-313, 3038], [-313, 3030], [-300, 3013], [-297, 2993], [-283, 2970], [-279, 2937], [-270, 2924], [-269, 2909], [-282, 2896], [-313, 2884], [-319, 2896], [-332, 2890], [-336, 2875], [-349, 2856], [-348, 2842], [-334, 2826], [-337, 2808], [-329, 2792], [-346, 2780], [-351, 2763], [-344, 2727], [-340, 2686], [-346, 2648], [-335, 2626], [-316, 2614], [-285, 2612], [-266, 2605], [-260, 2597], [-261, 2567], [-292, 2558], [-299, 2538], [-298, 2520], [-284, 2519], [-284, 2497], [-269, 2496], [-253, 2482], [-248, 2461], [-268, 2445], [-271, 2436], [-259, 2433], [-259, 2419], [-236, 2389], [-238, 2380], [-253, 2355], [-264, 2367], [-299, 2377], [-324, 2377], [-335, 2382], [-349, 2401], [-362, 2411], [-361, 2421], [-379, 2439], [-399, 2436], [-413, 2426], [-466, 2437], [-478, 2462], [-487, 2446], [-502, 2445], [-522, 2433], [-522, 2443], [-511, 2452], [-509, 2464], [-498, 2474], [-520, 2474], [-540, 2508], [-535, 2531], [-536, 2550], [-531, 2558], [-502, 2574], [-508, 2613], [-495, 2636], [-482, 2638], [-479, 2627], [-467, 2620], [-473, 2640], [-472, 2668], [-492, 2687], [-499, 2727], [-492, 2740], [-500, 2748], [-499, 2767], [-493, 2782], [-478, 2786], [-463, 2783], [-437, 2767], [-437, 2778], [-463, 2790], [-475, 2816], [-481, 2817], [-489, 2838], [-500, 2845], [-493, 2856], [-465, 2866], [-454, 2874], [-441, 2867], [-417, 2803], [-409, 2804], [-422, 2851], [-410, 2861], [-423, 2864], [-424, 2878], [-414, 2886], [-424, 2888], [-428, 2909], [-423, 2932], [-435, 2941], [-447, 2918], [-470, 2926], [-481, 2921], [-498, 2930], [-514, 2949], [-499, 2960], [-498, 2968], [-465, 3004], [-449, 3003], [-417, 3015], [-401, 3034], [-401, 3075], [-395, 3097], [-405, 3082], [-409, 3044], [-417, 3025], [-457, 3009], [-472, 3018], [-476, 3035], [-489, 3053], [-512, 3066], [-518, 3062], [-534, 3069], [-547, 3060], [-550, 3048], [-565, 3038], [-553, 3008], [-582, 2981], [-630, 2981], [-644, 2972], [-679, 2984], [-700, 2985], [-689, 3005], [-683, 3024], [-659, 3027], [-648, 3056], [-663, 3032], [-689, 3030], [-711, 3037], [-716, 3056], [-726, 3077], [-724, 3102], [-738, 3143], [-744, 3152], [-753, 3203], [-734, 3220], [-743, 3234], [-738, 3262], [-727, 3280], [-735, 3290], [-718, 3308], [-712, 3326], [-725, 3356], [-722, 3369], [-721, 3403], [-703, 3397], [-656, 3400], [-601, 3386], [-571, 3391], [-556, 3388], [-546, 3376], [-531, 3341], [-494, 3316], [-475, 3307], [-452, 3302], [-424, 3302], [-354, 3309], [-341, 3312], [-316, 3330], [-309, 3339]]], [[[-737, 2973], [-726, 2963], [-733, 2957], [-747, 2962], [-746, 2949], [-721, 2944], [-713, 2954], [-700, 2942], [-702, 2954], [-685, 2969], [-673, 2963], [-661, 2973], [-652, 2962], [-649, 2943], [-659, 2932], [-649, 2927], [-635, 2909], [-629, 2895], [-639, 2888], [-637, 2876], [-615, 2863], [-611, 2846], [-615, 2831], [-623, 2833], [-639, 2825], [-652, 2825], [-674, 2811], [-674, 2787], [-657, 2771], [-677, 2763], [-695, 2771], [-697, 2742], [-681, 2729], [-673, 2728], [-658, 2706], [-633, 2694], [-628, 2672], [-654, 2665], [-672, 2670], [-660, 2662], [-653, 2644], [-643, 2643], [-631, 2619], [-644, 2624], [-623, 2606], [-630, 2597], [-643, 2603], [-670, 2592], [-674, 2602], [-682, 2598], [-675, 2584], [-674, 2568], [-659, 2554], [-656, 2545], [-670, 2542], [-657, 2533], [-668, 2521], [-683, 2529], [-682, 2515], [-698, 2511], [-710, 2526], [-722, 2519], [-730, 2525], [-741, 2514], [-751, 2526], [-786, 2535], [-795, 2545], [-809, 2550], [-801, 2581], [-792, 2590], [-799, 2596], [-778, 2619], [-780, 2649], [-767, 2660], [-763, 2729], [-773, 2760], [-779, 2764], [-780, 2787], [-772, 2820], [-778, 2834], [-774, 2848], [-756, 2874], [-756, 2895], [-759, 2930], [-751, 2937], [-764, 2966], [-759, 2976], [-740, 2965], [-737, 2973]]]]}}, {"type": "Feature", "id": "CL.LI", "properties": {"hc-group": "admin1", "hc-middle-x": 0.93, "hc-middle-y": 0.51, "hc-key": "cl-li", "hc-a2": "LI", "labelrank": "3", "hasc": "CL.LI", "alt-name": "Libertador", "woe-id": "2345025", "subregion": null, "fips": "CI08", "postal-code": "LI", "name": "Libertador General <PERSON>", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-71.07559999999999", "woe-name": "Libertador General <PERSON>", "latitude": "-34.5255", "woe-label": "O'Higgins Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[61, 5120], [58, 5112], [59, 5089], [46, 5074], [38, 5054], [20, 5035], [15, 5004], [0, 4989], [10, 4975], [-8, 4931], [-12, 4910], [-45, 4917], [-68, 4912], [-83, 4916], [-103, 4935], [-114, 4940], [-148, 4939], [-202, 4965], [-222, 4955], [-234, 4957], [-251, 4937], [-261, 4932], [-307, 4934], [-329, 4925], [-349, 4945], [-378, 4952], [-389, 4962], [-416, 4976], [-406, 5005], [-404, 5054], [-406, 5077], [-394, 5087], [-395, 5131], [-403, 5145], [-388, 5170], [-369, 5211], [-347, 5210], [-332, 5201], [-310, 5183], [-294, 5188], [-272, 5181], [-246, 5166], [-233, 5171], [-205, 5150], [-178, 5144], [-159, 5148], [-120, 5190], [-119, 5210], [-88, 5217], [-66, 5217], [-36, 5228], [-15, 5209], [-5, 5187], [4, 5180], [24, 5180], [31, 5173], [38, 5151], [51, 5141], [61, 5120]]]}}, {"type": "Feature", "id": "CL.AI", "properties": {"hc-group": "admin1", "hc-middle-x": 0.71, "hc-middle-y": 0.41, "hc-key": "cl-ai", "hc-a2": "AI", "labelrank": "3", "hasc": "CL.AI", "alt-name": "Aisén|Aysén|Aysén del General Carlos <PERSON>", "woe-id": "2345019", "subregion": null, "fips": "CI02", "postal-code": "AI", "name": "Aisén del General Carlos <PERSON>", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-73.3899", "woe-name": "Aisén del General Carlos <PERSON>", "latitude": "-46.8716", "woe-label": "Aisen Region, CL, Chile", "type": "Región"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[-708, 1061], [-713, 1061], [-731, 1093], [-711, 1091], [-691, 1078], [-691, 1073], [-708, 1061]]], [[[-644, 1086], [-646, 1078], [-668, 1087], [-681, 1100], [-672, 1110], [-645, 1094], [-644, 1086]]], [[[-693, 1108], [-676, 1086], [-685, 1081], [-698, 1091], [-722, 1100], [-729, 1110], [-722, 1113], [-703, 1106], [-711, 1121], [-702, 1130], [-693, 1130], [-693, 1108]]], [[[-724, 1155], [-708, 1138], [-713, 1128], [-737, 1113], [-743, 1139], [-737, 1153], [-750, 1137], [-750, 1161], [-746, 1173], [-724, 1155]]], [[[-744, 1254], [-744, 1245], [-758, 1245], [-766, 1228], [-776, 1245], [-777, 1258], [-770, 1264], [-746, 1261], [-744, 1254]]], [[[-853, 1285], [-852, 1264], [-841, 1257], [-846, 1251], [-878, 1239], [-873, 1253], [-895, 1262], [-902, 1257], [-906, 1270], [-895, 1281], [-885, 1276], [-870, 1281], [-861, 1290], [-853, 1285]]], [[[-824, 1266], [-843, 1275], [-849, 1288], [-840, 1297], [-821, 1291], [-811, 1276], [-824, 1266]]], [[[-743, 1446], [-753, 1458], [-744, 1475], [-729, 1480], [-721, 1468], [-743, 1446]]], [[[-683, 1488], [-675, 1491], [-655, 1484], [-671, 1458], [-686, 1447], [-703, 1466], [-693, 1492], [-683, 1488]]], [[[-884, 1545], [-890, 1543], [-912, 1558], [-927, 1563], [-904, 1573], [-889, 1568], [-884, 1545]]], [[[-637, 1771], [-626, 1772], [-615, 1764], [-614, 1754], [-631, 1727], [-640, 1731], [-645, 1746], [-666, 1762], [-660, 1777], [-646, 1779], [-637, 1771]]], [[[-630, 1805], [-622, 1806], [-616, 1779], [-643, 1783], [-644, 1791], [-632, 1815], [-630, 1805]]], [[[-623, 1822], [-620, 1816], [-639, 1818], [-651, 1791], [-666, 1789], [-668, 1807], [-656, 1823], [-642, 1829], [-631, 1842], [-623, 1822]]], [[[-685, 1852], [-668, 1853], [-676, 1846], [-675, 1821], [-679, 1808], [-686, 1807], [-700, 1832], [-701, 1846], [-685, 1852]]], [[[-836, 1854], [-857, 1867], [-857, 1882], [-847, 1881], [-832, 1856], [-836, 1854]]], [[[-804, 1843], [-822, 1848], [-822, 1871], [-817, 1884], [-793, 1892], [-798, 1871], [-793, 1866], [-804, 1843]]], [[[-675, 1862], [-692, 1860], [-705, 1880], [-708, 1896], [-693, 1912], [-668, 1897], [-668, 1867], [-675, 1862]]], [[[-669, 1932], [-653, 1938], [-654, 1920], [-660, 1906], [-693, 1917], [-715, 1904], [-709, 1928], [-701, 1943], [-686, 1942], [-669, 1932]]], [[[-785, 1897], [-790, 1892], [-800, 1908], [-792, 1931], [-777, 1936], [-786, 1915], [-785, 1897]]], [[[-678, 1960], [-655, 1960], [-661, 1946], [-673, 1942], [-691, 1947], [-691, 1955], [-678, 1960]]], [[[-758, 1945], [-787, 1956], [-796, 1967], [-791, 1976], [-769, 1979], [-751, 1949], [-758, 1945]]], [[[-698, 1991], [-689, 1987], [-656, 1990], [-648, 1971], [-656, 1967], [-692, 1963], [-716, 1970], [-725, 1990], [-707, 2005], [-698, 1991]]], [[[-752, 1976], [-758, 1974], [-775, 1988], [-776, 2001], [-765, 2014], [-746, 1999], [-752, 1976]]], [[[-707, 2104], [-735, 2112], [-733, 2117], [-693, 2126], [-694, 2110], [-707, 2104]]], [[[-662, 2112], [-650, 2086], [-665, 2077], [-683, 2095], [-676, 2109], [-687, 2117], [-677, 2131], [-667, 2126], [-662, 2112]]], [[[-915, 2070], [-932, 2071], [-931, 2085], [-941, 2098], [-931, 2114], [-924, 2114], [-913, 2096], [-904, 2093], [-905, 2076], [-915, 2070]]], [[[-621, 2140], [-624, 2116], [-645, 2117], [-648, 2136], [-630, 2142], [-621, 2140]]], [[[-765, 2102], [-778, 2099], [-794, 2106], [-806, 2126], [-800, 2136], [-787, 2137], [-782, 2121], [-768, 2114], [-765, 2102]]], [[[-793, 2150], [-799, 2142], [-836, 2145], [-828, 2162], [-797, 2159], [-793, 2150]]], [[[-862, 2179], [-851, 2168], [-853, 2148], [-864, 2145], [-869, 2156], [-869, 2177], [-862, 2179]]], [[[-639, 2196], [-627, 2169], [-633, 2159], [-619, 2151], [-651, 2139], [-669, 2160], [-668, 2180], [-663, 2192], [-639, 2196]]], [[[-741, 2201], [-735, 2186], [-769, 2178], [-779, 2193], [-764, 2201], [-741, 2201]]], [[[-696, 2191], [-718, 2189], [-728, 2200], [-712, 2205], [-735, 2215], [-709, 2217], [-697, 2211], [-696, 2191]]], [[[-751, 2211], [-790, 2198], [-783, 2186], [-810, 2190], [-816, 2204], [-808, 2217], [-795, 2216], [-790, 2207], [-780, 2225], [-769, 2231], [-759, 2226], [-751, 2211]]], [[[-669, 2218], [-686, 2221], [-687, 2240], [-663, 2229], [-669, 2218]]], [[[-641, 2227], [-664, 2240], [-637, 2251], [-645, 2237], [-641, 2227]]], [[[-713, 2245], [-692, 2247], [-694, 2219], [-723, 2236], [-731, 2252], [-713, 2245]]], [[[-555, 2267], [-540, 2249], [-557, 2245], [-565, 2250], [-564, 2262], [-555, 2267]]], [[[-669, 2270], [-669, 2256], [-687, 2253], [-700, 2258], [-701, 2266], [-678, 2264], [-669, 2270]]], [[[-767, 2258], [-769, 2255], [-792, 2260], [-784, 2271], [-771, 2269], [-767, 2258]]], [[[-641, 2275], [-652, 2264], [-659, 2269], [-648, 2281], [-641, 2275]]], [[[-718, 2300], [-707, 2287], [-688, 2280], [-711, 2273], [-706, 2254], [-717, 2255], [-728, 2265], [-736, 2288], [-720, 2290], [-718, 2300]]], [[[-765, 2293], [-779, 2288], [-789, 2296], [-771, 2302], [-765, 2293]]], [[[-686, 2303], [-677, 2303], [-684, 2292], [-701, 2294], [-714, 2305], [-695, 2319], [-686, 2303]]], [[[-637, 2315], [-643, 2313], [-648, 2330], [-656, 2335], [-664, 2353], [-659, 2366], [-642, 2353], [-643, 2340], [-633, 2327], [-637, 2315]]], [[[-544, 2348], [-566, 2356], [-566, 2377], [-555, 2387], [-539, 2383], [-544, 2348]]], [[[-854, 2464], [-834, 2444], [-858, 2439], [-878, 2428], [-888, 2438], [-891, 2459], [-874, 2459], [-873, 2469], [-854, 2464]]], [[[-883, 1019], [-881, 1009], [-874, 1011], [-871, 991], [-880, 988], [-886, 998], [-904, 977], [-929, 967], [-936, 971], [-937, 993], [-920, 989], [-903, 996], [-883, 1019]]], [[[-910, 1023], [-935, 1013], [-946, 1026], [-945, 1054], [-936, 1068], [-920, 1060], [-905, 1073], [-903, 1065], [-893, 1067], [-887, 1075], [-889, 1043], [-881, 1040], [-882, 1022], [-911, 997], [-924, 995], [-929, 1006], [-911, 1007], [-901, 1022], [-920, 1013], [-910, 1023]]], [[[-746, 1011], [-771, 1027], [-779, 1024], [-808, 1027], [-821, 1035], [-827, 1058], [-813, 1084], [-794, 1093], [-789, 1090], [-780, 1067], [-769, 1077], [-788, 1101], [-788, 1129], [-783, 1145], [-784, 1162], [-777, 1168], [-772, 1161], [-774, 1143], [-763, 1131], [-765, 1120], [-753, 1112], [-745, 1093], [-761, 1088], [-750, 1082], [-744, 1048], [-730, 1032], [-730, 1043], [-722, 1037], [-729, 1017], [-746, 1011]]], [[[-855, 1111], [-849, 1126], [-856, 1125], [-867, 1138], [-863, 1143], [-879, 1178], [-862, 1177], [-853, 1170], [-834, 1178], [-796, 1163], [-791, 1155], [-795, 1133], [-794, 1106], [-828, 1075], [-855, 1111]]], [[[-863, 1003], [-861, 1018], [-868, 1016], [-876, 1033], [-875, 1044], [-881, 1057], [-874, 1079], [-885, 1085], [-884, 1098], [-892, 1087], [-909, 1076], [-924, 1075], [-928, 1086], [-901, 1098], [-891, 1111], [-902, 1106], [-900, 1118], [-912, 1105], [-924, 1101], [-930, 1110], [-911, 1130], [-910, 1138], [-925, 1127], [-935, 1144], [-941, 1168], [-922, 1187], [-917, 1176], [-911, 1187], [-899, 1195], [-897, 1179], [-879, 1150], [-877, 1137], [-863, 1103], [-840, 1068], [-848, 1061], [-838, 1058], [-832, 1032], [-842, 1015], [-858, 998], [-863, 1003]]], [[[-852, 1203], [-860, 1192], [-878, 1186], [-873, 1222], [-852, 1237], [-824, 1235], [-812, 1255], [-804, 1257], [-796, 1257], [-790, 1237], [-798, 1221], [-791, 1208], [-801, 1201], [-787, 1199], [-778, 1175], [-791, 1182], [-811, 1184], [-838, 1192], [-840, 1204], [-852, 1203]]], [[[-717, 1226], [-739, 1232], [-721, 1255], [-715, 1258], [-692, 1247], [-709, 1261], [-693, 1267], [-659, 1257], [-640, 1255], [-622, 1250], [-614, 1257], [-608, 1242], [-612, 1230], [-626, 1228], [-630, 1240], [-648, 1228], [-665, 1229], [-703, 1216], [-717, 1226]]], [[[-873, 1773], [-891, 1770], [-882, 1776], [-894, 1780], [-900, 1797], [-889, 1805], [-872, 1800], [-860, 1790], [-849, 1790], [-871, 1804], [-856, 1802], [-846, 1809], [-846, 1824], [-824, 1825], [-830, 1773], [-857, 1739], [-870, 1735], [-888, 1739], [-895, 1752], [-873, 1765], [-858, 1764], [-873, 1773]]], [[[-762, 1931], [-746, 1924], [-738, 1914], [-741, 1899], [-731, 1896], [-743, 1883], [-726, 1881], [-736, 1866], [-751, 1864], [-748, 1857], [-763, 1841], [-776, 1838], [-783, 1850], [-775, 1866], [-776, 1879], [-783, 1884], [-775, 1891], [-779, 1911], [-772, 1929], [-762, 1931]]], [[[-605, 1932], [-606, 1905], [-610, 1893], [-622, 1891], [-609, 1876], [-607, 1866], [-620, 1854], [-628, 1867], [-649, 1884], [-634, 1894], [-644, 1905], [-642, 1921], [-633, 1937], [-622, 1944], [-605, 1932]]], [[[-742, 2018], [-744, 2029], [-737, 2049], [-726, 2055], [-710, 2053], [-679, 2066], [-665, 2065], [-651, 2055], [-649, 2044], [-637, 2034], [-634, 2015], [-645, 1986], [-658, 1997], [-684, 1993], [-715, 2023], [-730, 2013], [-742, 2018]]], [[[-726, 2099], [-692, 2091], [-680, 2077], [-691, 2068], [-710, 2073], [-709, 2063], [-734, 2063], [-752, 2052], [-767, 2055], [-763, 2069], [-751, 2076], [-736, 2098], [-726, 2099]]], [[[-763, 2164], [-757, 2172], [-708, 2175], [-688, 2169], [-676, 2151], [-703, 2143], [-719, 2132], [-756, 2115], [-772, 2125], [-776, 2136], [-769, 2142], [-783, 2144], [-783, 2164], [-763, 2164]]], [[[-467, 2158], [-492, 2135], [-508, 2114], [-526, 2098], [-548, 2089], [-555, 2091], [-580, 2120], [-560, 2132], [-549, 2124], [-540, 2129], [-559, 2140], [-571, 2130], [-579, 2133], [-595, 2171], [-578, 2187], [-572, 2180], [-564, 2184], [-549, 2174], [-559, 2201], [-550, 2203], [-534, 2194], [-530, 2204], [-551, 2210], [-556, 2220], [-546, 2239], [-531, 2230], [-524, 2235], [-510, 2220], [-521, 2248], [-505, 2255], [-490, 2240], [-490, 2230], [-498, 2213], [-493, 2212], [-477, 2235], [-456, 2227], [-446, 2211], [-463, 2179], [-481, 2180], [-497, 2187], [-480, 2171], [-466, 2167], [-467, 2158]]], [[[-741, 2398], [-726, 2403], [-710, 2395], [-689, 2407], [-688, 2414], [-674, 2397], [-667, 2380], [-684, 2378], [-697, 2383], [-688, 2391], [-702, 2392], [-711, 2384], [-699, 2376], [-714, 2364], [-722, 2376], [-743, 2375], [-750, 2385], [-741, 2398]]], [[[-641, 1004], [-646, 1011], [-661, 1014], [-686, 1005], [-684, 1015], [-700, 1021], [-705, 1040], [-684, 1067], [-671, 1079], [-642, 1059], [-630, 1046], [-627, 1055], [-636, 1064], [-638, 1091], [-619, 1097], [-623, 1104], [-634, 1098], [-656, 1104], [-665, 1112], [-679, 1112], [-675, 1119], [-652, 1125], [-672, 1125], [-685, 1117], [-690, 1136], [-698, 1151], [-723, 1158], [-738, 1183], [-731, 1201], [-752, 1185], [-760, 1190], [-761, 1205], [-741, 1220], [-721, 1214], [-705, 1201], [-693, 1163], [-681, 1147], [-671, 1151], [-683, 1165], [-683, 1175], [-705, 1211], [-681, 1215], [-654, 1214], [-648, 1205], [-634, 1202], [-621, 1210], [-614, 1203], [-615, 1190], [-598, 1210], [-593, 1195], [-571, 1187], [-562, 1180], [-564, 1161], [-554, 1150], [-545, 1171], [-532, 1171], [-516, 1160], [-516, 1168], [-503, 1184], [-506, 1187], [-526, 1176], [-542, 1186], [-564, 1208], [-579, 1244], [-563, 1245], [-554, 1230], [-533, 1225], [-512, 1228], [-496, 1222], [-502, 1230], [-521, 1235], [-544, 1231], [-553, 1240], [-551, 1249], [-572, 1258], [-560, 1274], [-584, 1277], [-591, 1282], [-598, 1302], [-585, 1319], [-592, 1323], [-588, 1341], [-600, 1349], [-607, 1332], [-598, 1325], [-607, 1291], [-603, 1282], [-608, 1272], [-631, 1260], [-652, 1265], [-659, 1279], [-684, 1272], [-702, 1277], [-707, 1270], [-724, 1275], [-715, 1283], [-738, 1276], [-766, 1272], [-788, 1289], [-777, 1305], [-754, 1303], [-770, 1314], [-767, 1329], [-750, 1332], [-749, 1320], [-741, 1336], [-731, 1333], [-728, 1322], [-721, 1326], [-715, 1313], [-735, 1302], [-740, 1294], [-716, 1304], [-699, 1287], [-706, 1302], [-689, 1307], [-698, 1316], [-683, 1321], [-663, 1319], [-677, 1330], [-693, 1330], [-704, 1323], [-713, 1328], [-717, 1346], [-726, 1341], [-739, 1356], [-726, 1363], [-740, 1373], [-749, 1358], [-754, 1371], [-746, 1385], [-733, 1396], [-719, 1432], [-701, 1427], [-698, 1437], [-689, 1435], [-686, 1416], [-676, 1401], [-683, 1433], [-679, 1442], [-660, 1447], [-651, 1426], [-655, 1450], [-669, 1453], [-651, 1481], [-650, 1494], [-665, 1504], [-684, 1500], [-702, 1521], [-688, 1545], [-710, 1533], [-720, 1544], [-716, 1556], [-750, 1558], [-780, 1566], [-757, 1556], [-766, 1547], [-786, 1552], [-794, 1531], [-770, 1537], [-758, 1530], [-747, 1533], [-755, 1519], [-783, 1525], [-795, 1521], [-803, 1533], [-825, 1552], [-830, 1539], [-851, 1553], [-862, 1555], [-863, 1563], [-878, 1584], [-863, 1614], [-853, 1622], [-853, 1644], [-867, 1620], [-875, 1621], [-885, 1599], [-901, 1586], [-903, 1595], [-914, 1583], [-937, 1575], [-947, 1576], [-970, 1566], [-987, 1539], [-981, 1535], [-966, 1561], [-942, 1558], [-951, 1541], [-934, 1540], [-941, 1516], [-929, 1521], [-924, 1507], [-935, 1497], [-962, 1490], [-969, 1502], [-984, 1512], [-984, 1521], [-996, 1533], [-999, 1575], [-987, 1595], [-964, 1602], [-970, 1613], [-953, 1616], [-935, 1650], [-921, 1660], [-910, 1649], [-909, 1679], [-879, 1665], [-874, 1670], [-896, 1676], [-889, 1704], [-881, 1707], [-862, 1692], [-863, 1702], [-855, 1726], [-846, 1726], [-831, 1711], [-824, 1720], [-838, 1734], [-830, 1759], [-819, 1775], [-823, 1787], [-818, 1816], [-810, 1824], [-801, 1816], [-792, 1822], [-790, 1801], [-769, 1790], [-773, 1772], [-766, 1772], [-759, 1795], [-769, 1805], [-773, 1827], [-753, 1835], [-743, 1824], [-739, 1829], [-713, 1836], [-702, 1825], [-719, 1811], [-704, 1808], [-708, 1798], [-696, 1802], [-692, 1790], [-711, 1777], [-694, 1771], [-696, 1756], [-709, 1740], [-692, 1752], [-691, 1785], [-674, 1772], [-671, 1757], [-680, 1738], [-712, 1716], [-740, 1705], [-758, 1714], [-772, 1728], [-771, 1721], [-750, 1705], [-741, 1702], [-711, 1710], [-683, 1724], [-682, 1710], [-688, 1688], [-678, 1699], [-662, 1695], [-654, 1687], [-642, 1686], [-663, 1706], [-665, 1716], [-678, 1718], [-673, 1737], [-653, 1742], [-647, 1730], [-629, 1717], [-627, 1706], [-632, 1681], [-653, 1649], [-666, 1622], [-653, 1613], [-635, 1619], [-621, 1665], [-608, 1683], [-591, 1698], [-582, 1737], [-564, 1767], [-549, 1781], [-566, 1779], [-572, 1761], [-592, 1728], [-596, 1715], [-612, 1693], [-615, 1701], [-606, 1730], [-602, 1756], [-609, 1770], [-612, 1791], [-603, 1813], [-586, 1821], [-595, 1830], [-590, 1841], [-565, 1872], [-556, 1876], [-525, 1881], [-525, 1886], [-545, 1888], [-552, 1896], [-570, 1886], [-582, 1864], [-602, 1847], [-599, 1890], [-592, 1939], [-584, 1949], [-574, 1951], [-564, 1967], [-554, 1959], [-556, 1975], [-533, 1987], [-523, 1969], [-492, 1948], [-461, 1944], [-456, 1958], [-462, 1966], [-486, 1961], [-506, 1973], [-527, 1993], [-543, 2002], [-557, 1991], [-576, 1989], [-584, 1994], [-580, 2016], [-559, 2021], [-554, 2030], [-577, 2063], [-575, 2074], [-566, 2083], [-543, 2082], [-520, 2088], [-497, 2114], [-480, 2118], [-460, 2142], [-444, 2145], [-441, 2157], [-445, 2172], [-437, 2207], [-421, 2218], [-426, 2229], [-422, 2262], [-439, 2222], [-449, 2220], [-451, 2229], [-491, 2255], [-490, 2267], [-516, 2279], [-538, 2282], [-545, 2291], [-535, 2296], [-531, 2311], [-547, 2300], [-565, 2305], [-564, 2323], [-553, 2319], [-553, 2333], [-544, 2339], [-534, 2328], [-538, 2360], [-525, 2381], [-515, 2421], [-505, 2408], [-482, 2427], [-484, 2449], [-487, 2431], [-508, 2425], [-522, 2433], [-502, 2445], [-487, 2446], [-478, 2462], [-466, 2437], [-413, 2426], [-399, 2436], [-379, 2439], [-361, 2421], [-362, 2411], [-349, 2401], [-335, 2382], [-324, 2377], [-299, 2377], [-264, 2367], [-253, 2355], [-275, 2336], [-264, 2304], [-268, 2291], [-263, 2273], [-274, 2265], [-265, 2253], [-248, 2256], [-229, 2254], [-187, 2256], [-141, 2250], [-133, 2243], [-122, 2221], [-124, 2210], [-135, 2206], [-145, 2185], [-144, 2159], [-163, 2144], [-196, 2159], [-223, 2148], [-254, 2153], [-268, 2143], [-307, 2152], [-315, 2144], [-311, 2110], [-298, 2110], [-273, 2099], [-247, 2104], [-235, 2093], [-213, 2092], [-206, 2084], [-193, 2055], [-160, 2021], [-154, 2003], [-173, 1980], [-193, 1971], [-186, 1950], [-195, 1940], [-232, 1934], [-242, 1923], [-238, 1915], [-245, 1903], [-247, 1875], [-237, 1862], [-235, 1842], [-220, 1835], [-208, 1811], [-229, 1786], [-238, 1770], [-261, 1763], [-266, 1758], [-235, 1733], [-231, 1691], [-217, 1656], [-219, 1636], [-216, 1607], [-248, 1577], [-263, 1575], [-268, 1536], [-257, 1519], [-273, 1506], [-270, 1495], [-252, 1484], [-245, 1471], [-254, 1452], [-273, 1461], [-279, 1460], [-276, 1441], [-281, 1429], [-302, 1401], [-332, 1394], [-339, 1380], [-330, 1374], [-333, 1344], [-353, 1302], [-359, 1272], [-367, 1254], [-353, 1233], [-342, 1229], [-337, 1217], [-327, 1212], [-319, 1172], [-321, 1151], [-315, 1138], [-330, 1133], [-340, 1118], [-366, 1102], [-372, 1085], [-365, 1056], [-363, 1026], [-365, 1006], [-391, 970], [-406, 960], [-424, 957], [-440, 946], [-451, 926], [-454, 908], [-467, 888], [-465, 870], [-641, 1004], [-641, 1004]]]]}}, {"type": "Feature", "id": "CL.MA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.3, "hc-middle-y": 0.33, "hc-key": "cl-ma", "hc-a2": "MA", "labelrank": "3", "hasc": "CL.MA", "alt-name": "Magalhães|Magellan et Antarctique Chilienne|Región de Magallanes y de la Antártica Chilena", "woe-id": "2345027", "subregion": null, "fips": "CI10", "postal-code": "MA", "name": "Magallanes y Antártica Chilena", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-71.39749999999999", "woe-name": "Magallanes y Antártica Chilena", "latitude": "-53.4723", "woe-label": "Magallanes and Antartica Chilena Region, CL, Chile", "type": "Región"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[585, -974], [591, -981], [590, -994], [572, -984], [569, -969], [585, -974]]], [[[611, -999], [599, -989], [607, -969], [614, -996], [611, -999]]], [[[513, -913], [512, -905], [518, -894], [516, -883], [522, -877], [529, -883], [534, -904], [513, -913]]], [[[201, -840], [199, -850], [178, -852], [182, -844], [178, -831], [201, -840]]], [[[154, -822], [145, -843], [141, -828], [143, -824], [154, -822]]], [[[647, -835], [620, -833], [618, -819], [632, -805], [651, -807], [656, -827], [647, -835]]], [[[717, -795], [725, -801], [716, -805], [711, -816], [697, -823], [688, -817], [691, -801], [707, -789], [717, -795]]], [[[660, -759], [663, -774], [644, -759], [622, -747], [625, -738], [654, -748], [660, -759]]], [[[76, -696], [78, -702], [49, -710], [33, -710], [22, -702], [36, -698], [44, -691], [76, -696]]], [[[-188, -645], [-173, -671], [-182, -677], [-184, -661], [-192, -648], [-196, -655], [-203, -647], [-188, -645]]], [[[-346, -598], [-350, -612], [-369, -610], [-362, -601], [-346, -598]]], [[[-246, -560], [-240, -569], [-252, -566], [-266, -588], [-279, -565], [-260, -560], [-259, -553], [-246, -560]]], [[[77, -508], [93, -508], [105, -521], [97, -531], [65, -522], [54, -505], [59, -497], [77, -508]]], [[[92, -474], [87, -497], [73, -498], [55, -481], [68, -479], [88, -470], [92, -474]]], [[[-400, -486], [-401, -494], [-391, -489], [-388, -509], [-397, -505], [-410, -509], [-414, -496], [-438, -497], [-423, -483], [-400, -486]]], [[[-484, -344], [-499, -349], [-500, -359], [-510, -364], [-507, -349], [-518, -347], [-509, -340], [-514, -328], [-484, -344]]], [[[-572, -295], [-582, -288], [-567, -270], [-554, -265], [-560, -288], [-572, -295]]], [[[12, -149], [1, -145], [27, -125], [25, -138], [12, -149]]], [[[-614, -97], [-634, -89], [-640, -79], [-630, -64], [-624, -68], [-614, -97]]], [[[-638, -46], [-652, -38], [-652, -16], [-643, -10], [-643, -33], [-637, -34], [-638, -46]]], [[[-678, 40], [-680, 29], [-697, 21], [-703, 37], [-670, 58], [-678, 40]]], [[[-690, 50], [-689, 67], [-663, 99], [-655, 91], [-653, 79], [-690, 50]]], [[[-539, 128], [-529, 135], [-520, 130], [-515, 113], [-515, 94], [-521, 83], [-533, 91], [-540, 109], [-559, 115], [-566, 129], [-553, 140], [-539, 128]]], [[[-744, 138], [-742, 134], [-748, 107], [-759, 98], [-765, 103], [-764, 123], [-757, 147], [-742, 145], [-744, 138]]], [[[-550, 175], [-543, 161], [-535, 161], [-534, 139], [-542, 138], [-554, 163], [-560, 167], [-561, 182], [-550, 175]]], [[[-733, 200], [-749, 203], [-747, 217], [-736, 220], [-733, 200]]], [[[-632, 244], [-648, 245], [-647, 266], [-640, 265], [-631, 251], [-632, 244]]], [[[-610, 283], [-598, 258], [-597, 244], [-620, 248], [-618, 256], [-625, 263], [-625, 281], [-610, 283]]], [[[-537, 285], [-540, 266], [-560, 270], [-549, 283], [-566, 281], [-557, 295], [-544, 296], [-537, 285]]], [[[-739, 310], [-761, 303], [-765, 307], [-756, 324], [-740, 324], [-739, 310]]], [[[-666, 348], [-676, 343], [-676, 333], [-654, 327], [-652, 314], [-657, 303], [-685, 315], [-692, 336], [-675, 354], [-666, 348]]], [[[-628, 295], [-640, 331], [-645, 353], [-644, 372], [-650, 383], [-641, 380], [-635, 356], [-617, 314], [-628, 295]]], [[[-777, 446], [-770, 450], [-770, 406], [-784, 413], [-791, 430], [-779, 435], [-786, 445], [-777, 446]]], [[[-703, 482], [-715, 477], [-716, 498], [-700, 492], [-703, 482]]], [[[-703, 535], [-696, 531], [-710, 524], [-712, 514], [-726, 501], [-737, 500], [-730, 523], [-735, 526], [-724, 538], [-703, 535]]], [[[-773, 514], [-784, 501], [-808, 503], [-800, 526], [-775, 555], [-760, 554], [-772, 535], [-773, 514]]], [[[-685, 752], [-692, 756], [-694, 778], [-700, 782], [-696, 806], [-680, 796], [-667, 771], [-685, 752]]], [[[-888, 797], [-886, 784], [-878, 780], [-881, 765], [-900, 782], [-897, 803], [-888, 797]]], [[[-829, 941], [-824, 926], [-846, 917], [-858, 936], [-857, 952], [-867, 959], [-872, 972], [-853, 969], [-836, 959], [-829, 941]]], [[[-836, 968], [-861, 976], [-858, 985], [-834, 1020], [-828, 1003], [-836, 968]]], [[[-812, 1017], [-799, 1021], [-785, 1018], [-813, 992], [-824, 993], [-818, 1012], [-812, 1017]]], [[[498, -968], [536, -976], [536, -967], [551, -977], [541, -977], [539, -990], [531, -989], [523, -997], [525, -982], [515, -990], [507, -978], [498, -992], [482, -985], [480, -974], [498, -968]]], [[[562, -937], [561, -905], [571, -907], [576, -924], [569, -939], [584, -942], [584, -960], [572, -963], [572, -954], [562, -950], [539, -947], [541, -925], [546, -924], [562, -937]]], [[[617, -794], [594, -822], [586, -826], [564, -822], [557, -814], [562, -800], [559, -790], [550, -787], [532, -792], [526, -812], [501, -813], [492, -803], [511, -805], [491, -795], [486, -785], [471, -800], [456, -803], [444, -792], [450, -786], [439, -766], [443, -752], [440, -731], [416, -734], [414, -714], [436, -711], [480, -710], [502, -713], [515, -719], [527, -716], [584, -719], [603, -731], [622, -761], [623, -775], [617, -794]]], [[[101, -770], [90, -774], [83, -763], [71, -768], [85, -784], [73, -788], [65, -798], [62, -783], [56, -799], [53, -784], [61, -769], [51, -756], [29, -774], [22, -754], [13, -764], [5, -757], [-8, -761], [-18, -742], [-5, -738], [-18, -734], [-17, -726], [-4, -726], [15, -733], [14, -721], [29, -731], [52, -730], [44, -726], [63, -710], [87, -708], [92, -712], [62, -725], [59, -738], [47, -740], [27, -735], [25, -743], [38, -748], [56, -744], [78, -753], [94, -746], [99, -752], [89, -758], [101, -770]]], [[[226, -738], [211, -737], [203, -743], [181, -746], [174, -741], [158, -748], [158, -733], [152, -724], [170, -722], [159, -716], [158, -705], [170, -701], [179, -710], [181, -724], [191, -722], [191, -707], [183, -699], [197, -699], [223, -706], [224, -710], [221, -721], [237, -708], [280, -723], [265, -728], [249, -724], [253, -730], [238, -735], [226, -730], [226, -738]]], [[[-65, -704], [-71, -726], [-78, -726], [-77, -714], [-85, -726], [-95, -709], [-88, -696], [-76, -694], [-53, -698], [-44, -696], [-32, -707], [-21, -700], [-11, -704], [-14, -714], [-29, -727], [-42, -719], [-39, -711], [-53, -715], [-54, -704], [-65, -704]]], [[[-241, -498], [-258, -493], [-245, -484], [-235, -494], [-226, -505], [-240, -521], [-240, -537], [-254, -530], [-261, -539], [-274, -536], [-277, -525], [-264, -515], [-253, -514], [-247, -506], [-257, -503], [-241, -498]]], [[[-38, -476], [-28, -486], [-35, -515], [-26, -505], [-23, -533], [-35, -540], [-36, -549], [-48, -566], [-53, -551], [-58, -563], [-82, -540], [-65, -537], [-63, -520], [-74, -533], [-81, -518], [-84, -529], [-97, -521], [-99, -512], [-87, -503], [-107, -498], [-108, -520], [-114, -523], [-114, -537], [-124, -526], [-116, -512], [-126, -518], [-128, -528], [-135, -524], [-142, -508], [-126, -483], [-131, -483], [-136, -456], [-132, -448], [-120, -444], [-114, -456], [-105, -443], [-92, -477], [-90, -451], [-77, -457], [-70, -480], [-65, -506], [-56, -483], [-54, -501], [-48, -499], [-45, -478], [-38, -476]]], [[[-136, -490], [-153, -508], [-160, -504], [-181, -474], [-185, -477], [-178, -496], [-164, -508], [-157, -521], [-151, -524], [-151, -536], [-163, -533], [-169, -543], [-162, -546], [-164, -558], [-188, -553], [-175, -528], [-184, -521], [-198, -519], [-210, -508], [-199, -503], [-213, -502], [-199, -496], [-210, -481], [-220, -494], [-231, -482], [-227, -464], [-238, -445], [-216, -446], [-204, -459], [-201, -445], [-188, -433], [-194, -425], [-186, -421], [-172, -430], [-166, -440], [-165, -454], [-161, -435], [-149, -433], [-136, -440], [-158, -464], [-144, -457], [-143, -470], [-154, -476], [-149, -486], [-137, -482], [-136, -490]]], [[[52, -500], [43, -516], [27, -509], [4, -489], [6, -484], [-7, -465], [-10, -442], [-5, -434], [-11, -421], [-8, -412], [9, -407], [34, -420], [39, -407], [22, -396], [19, -372], [43, -350], [48, -331], [59, -332], [62, -345], [59, -361], [69, -399], [69, -419], [64, -423], [84, -457], [76, -469], [67, -472], [58, -465], [38, -441], [28, -447], [46, -474], [44, -484], [52, -500]]], [[[-504, -315], [-485, -328], [-464, -331], [-473, -324], [-460, -323], [-470, -315], [-464, -309], [-446, -304], [-436, -310], [-451, -321], [-434, -343], [-453, -355], [-460, -339], [-461, -347], [-478, -341], [-488, -330], [-501, -324], [-504, -315]]], [[[-494, -153], [-481, -157], [-484, -144], [-473, -153], [-469, -148], [-452, -155], [-444, -163], [-446, -179], [-461, -175], [-456, -167], [-464, -161], [-486, -161], [-512, -172], [-506, -159], [-494, -153]]], [[[-561, -185], [-536, -198], [-540, -204], [-529, -209], [-526, -199], [-514, -215], [-499, -216], [-481, -237], [-488, -212], [-468, -228], [-471, -248], [-458, -268], [-452, -256], [-459, -247], [-456, -231], [-433, -250], [-427, -260], [-434, -269], [-421, -266], [-386, -290], [-389, -294], [-409, -295], [-424, -292], [-443, -273], [-449, -284], [-466, -286], [-475, -280], [-455, -276], [-470, -271], [-477, -248], [-484, -254], [-492, -240], [-503, -239], [-505, -227], [-516, -228], [-525, -214], [-530, -221], [-552, -216], [-548, -224], [-559, -230], [-574, -227], [-589, -213], [-585, -223], [-595, -231], [-601, -217], [-616, -213], [-617, -196], [-633, -197], [-637, -189], [-650, -185], [-648, -165], [-655, -157], [-680, -140], [-668, -129], [-654, -142], [-635, -148], [-636, -156], [-624, -168], [-613, -168], [-618, -180], [-609, -189], [-604, -182], [-594, -191], [-578, -186], [-571, -198], [-561, -185]]], [[[-549, -90], [-535, -75], [-540, -74], [-560, -103], [-574, -118], [-575, -106], [-585, -102], [-587, -85], [-564, -77], [-547, -52], [-563, -52], [-563, -38], [-540, -31], [-522, -35], [-511, -28], [-502, -47], [-504, -57], [-518, -73], [-510, -81], [-518, -108], [-541, -119], [-545, -111], [-531, -88], [-547, -102], [-549, -90]]], [[[-719, 5], [-716, 19], [-707, 11], [-675, 17], [-665, 16], [-654, 0], [-661, -36], [-681, -35], [-681, -24], [-690, -29], [-701, -26], [-701, -7], [-719, 5]]], [[[-579, 30], [-577, 41], [-566, 38], [-554, 28], [-530, 18], [-523, 8], [-525, -8], [-516, -15], [-529, -26], [-538, -16], [-529, -12], [-562, 11], [-579, 30]]], [[[-598, 17], [-585, 12], [-584, 17], [-601, 28], [-588, 27], [-615, 38], [-622, 32], [-636, 42], [-617, 56], [-606, 50], [-584, 31], [-552, -3], [-554, -14], [-580, -30], [-592, -23], [-593, -10], [-603, -9], [-595, 2], [-603, 10], [-598, 17]]], [[[-559, 70], [-582, 80], [-586, 91], [-582, 105], [-571, 105], [-528, 69], [-512, 42], [-509, 19], [-542, 58], [-559, 70]]], [[[-713, 69], [-705, 110], [-701, 121], [-697, 112], [-685, 112], [-674, 122], [-678, 110], [-695, 79], [-686, 84], [-701, 50], [-711, 47], [-715, 33], [-720, 40], [-715, 56], [-703, 71], [-713, 69]]], [[[-648, 135], [-660, 160], [-637, 152], [-636, 144], [-627, 147], [-625, 132], [-612, 132], [-598, 122], [-608, 117], [-586, 117], [-593, 97], [-610, 97], [-623, 108], [-633, 110], [-646, 122], [-648, 135]]], [[[-753, 71], [-737, 91], [-749, 92], [-738, 123], [-733, 112], [-720, 106], [-715, 119], [-726, 138], [-719, 140], [-722, 155], [-730, 156], [-735, 169], [-733, 179], [-724, 175], [-716, 176], [-704, 135], [-714, 102], [-713, 88], [-724, 70], [-720, 60], [-730, 40], [-731, 55], [-741, 41], [-739, 59], [-753, 71]]], [[[-593, 213], [-587, 211], [-580, 196], [-571, 175], [-574, 168], [-560, 157], [-558, 148], [-567, 141], [-579, 141], [-613, 163], [-611, 174], [-597, 176], [-600, 194], [-587, 194], [-593, 213]]], [[[-805, 170], [-807, 197], [-793, 211], [-793, 247], [-796, 263], [-780, 275], [-783, 248], [-777, 267], [-757, 256], [-758, 236], [-767, 239], [-777, 220], [-783, 230], [-775, 200], [-780, 189], [-789, 194], [-787, 180], [-805, 170]]], [[[-716, 235], [-753, 219], [-755, 231], [-733, 248], [-720, 266], [-742, 251], [-750, 260], [-742, 272], [-729, 277], [-723, 296], [-702, 301], [-689, 300], [-678, 279], [-692, 284], [-675, 256], [-683, 246], [-693, 247], [-702, 277], [-716, 235]]], [[[-719, 345], [-707, 355], [-724, 358], [-724, 345], [-733, 334], [-744, 341], [-736, 363], [-750, 365], [-755, 359], [-758, 375], [-748, 377], [-754, 386], [-745, 387], [-699, 380], [-693, 391], [-703, 391], [-704, 401], [-691, 406], [-712, 429], [-710, 435], [-689, 430], [-675, 417], [-659, 398], [-665, 382], [-655, 367], [-658, 355], [-655, 337], [-668, 360], [-698, 347], [-696, 334], [-716, 328], [-719, 345]]], [[[-763, 430], [-742, 426], [-752, 435], [-754, 446], [-741, 438], [-741, 450], [-725, 439], [-726, 426], [-718, 413], [-729, 412], [-717, 396], [-707, 408], [-712, 388], [-755, 396], [-743, 405], [-763, 419], [-763, 430]]], [[[-824, 408], [-834, 415], [-853, 413], [-853, 427], [-845, 440], [-861, 438], [-855, 457], [-840, 457], [-838, 463], [-853, 484], [-860, 489], [-846, 499], [-842, 493], [-836, 499], [-826, 490], [-793, 494], [-810, 465], [-822, 465], [-829, 474], [-837, 472], [-822, 456], [-825, 449], [-820, 414], [-824, 408]]], [[[-646, 419], [-646, 406], [-656, 410], [-667, 428], [-662, 433], [-684, 436], [-697, 463], [-686, 479], [-678, 482], [-679, 492], [-671, 494], [-696, 499], [-705, 507], [-717, 506], [-697, 522], [-684, 509], [-665, 515], [-654, 482], [-642, 469], [-635, 455], [-654, 456], [-635, 444], [-622, 426], [-624, 407], [-631, 408], [-638, 423], [-646, 419]]], [[[-751, 605], [-746, 591], [-752, 576], [-772, 574], [-773, 564], [-791, 551], [-811, 525], [-817, 509], [-835, 510], [-836, 520], [-825, 529], [-816, 547], [-836, 534], [-832, 542], [-847, 537], [-857, 525], [-863, 530], [-862, 551], [-855, 581], [-847, 589], [-830, 584], [-847, 574], [-831, 561], [-808, 563], [-811, 571], [-806, 595], [-818, 587], [-831, 597], [-840, 596], [-855, 608], [-859, 619], [-846, 630], [-840, 623], [-835, 613], [-829, 623], [-810, 626], [-810, 610], [-800, 611], [-794, 600], [-777, 614], [-776, 606], [-796, 584], [-771, 588], [-751, 605]]], [[[-861, 709], [-863, 701], [-890, 682], [-890, 674], [-903, 665], [-908, 695], [-889, 701], [-889, 711], [-902, 715], [-908, 729], [-886, 751], [-861, 741], [-848, 714], [-831, 691], [-822, 666], [-833, 669], [-843, 680], [-838, 664], [-852, 670], [-861, 687], [-855, 692], [-861, 709]]], [[[-850, 882], [-838, 902], [-824, 907], [-808, 896], [-804, 910], [-787, 893], [-789, 843], [-807, 851], [-823, 872], [-843, 867], [-836, 880], [-850, 874], [-850, 882]]], [[[-909, 869], [-885, 895], [-891, 896], [-883, 913], [-873, 916], [-877, 901], [-861, 890], [-865, 876], [-871, 878], [-878, 862], [-891, 861], [-895, 836], [-901, 835], [-922, 844], [-919, 866], [-906, 879], [-909, 869]]], [[[-906, 902], [-901, 925], [-911, 914], [-931, 918], [-933, 939], [-928, 949], [-922, 944], [-914, 957], [-901, 956], [-907, 936], [-887, 961], [-892, 946], [-879, 936], [-884, 955], [-870, 952], [-862, 941], [-848, 906], [-854, 894], [-863, 903], [-870, 927], [-888, 919], [-895, 901], [-906, 902]]], [[[366, -679], [364, -704], [319, -673], [313, -677], [326, -682], [330, -689], [350, -707], [342, -713], [296, -721], [286, -720], [261, -707], [224, -697], [201, -686], [197, -674], [204, -672], [204, -649], [198, -667], [189, -651], [189, -679], [181, -678], [182, -659], [177, -658], [177, -677], [169, -686], [158, -686], [148, -669], [148, -646], [144, -656], [147, -687], [131, -695], [121, -680], [117, -694], [104, -697], [93, -692], [90, -681], [106, -651], [90, -666], [85, -691], [43, -677], [20, -694], [9, -689], [6, -680], [16, -672], [41, -663], [53, -653], [68, -635], [53, -632], [39, -645], [47, -651], [29, -663], [21, -660], [10, -669], [-9, -666], [-3, -655], [12, -647], [-15, -652], [-13, -661], [-22, -676], [-28, -669], [-21, -660], [-21, -645], [-13, -635], [-32, -626], [-30, -644], [-39, -638], [-48, -656], [-71, -644], [-53, -636], [-64, -633], [-66, -620], [-80, -606], [-77, -619], [-88, -617], [-90, -644], [-94, -639], [-104, -654], [-107, -641], [-119, -641], [-113, -626], [-120, -625], [-126, -645], [-132, -645], [-135, -633], [-146, -629], [-146, -638], [-156, -638], [-156, -648], [-179, -646], [-183, -637], [-168, -623], [-187, -625], [-188, -605], [-177, -608], [-188, -593], [-175, -594], [-162, -615], [-133, -623], [-146, -606], [-126, -616], [-118, -615], [-131, -606], [-141, -592], [-162, -594], [-164, -580], [-151, -573], [-143, -576], [-124, -603], [-104, -600], [-108, -592], [-123, -588], [-122, -574], [-113, -586], [-112, -576], [-87, -589], [-77, -580], [-95, -573], [-84, -565], [-54, -580], [-37, -603], [-46, -586], [-44, -581], [-25, -585], [-18, -590], [-12, -575], [-16, -553], [9, -546], [18, -554], [31, -548], [41, -552], [22, -577], [25, -592], [15, -611], [17, -626], [21, -612], [30, -624], [38, -626], [27, -607], [34, -577], [45, -566], [70, -591], [88, -601], [101, -618], [104, -611], [92, -595], [121, -609], [100, -592], [71, -579], [66, -564], [57, -557], [57, -546], [46, -533], [32, -531], [7, -533], [-3, -527], [-8, -508], [4, -512], [-9, -501], [-15, -489], [52, -528], [88, -547], [101, -556], [119, -575], [118, -565], [126, -556], [114, -553], [109, -545], [129, -525], [146, -538], [147, -561], [156, -561], [163, -573], [161, -596], [171, -586], [178, -591], [175, -606], [179, -611], [185, -592], [172, -573], [173, -562], [161, -542], [165, -533], [192, -543], [202, -560], [219, -578], [213, -559], [224, -550], [267, -578], [270, -588], [247, -612], [238, -629], [245, -646], [246, -627], [252, -623], [266, -602], [281, -611], [278, -588], [283, -580], [308, -585], [309, -575], [292, -564], [279, -561], [261, -544], [218, -525], [205, -516], [160, -499], [131, -479], [123, -446], [115, -432], [108, -401], [110, -387], [116, -380], [153, -358], [171, -359], [197, -350], [246, -317], [253, -297], [247, -270], [193, -272], [146, -284], [134, -294], [118, -294], [104, -304], [95, -302], [79, -283], [63, -275], [57, -260], [58, -235], [63, -220], [62, -202], [65, -175], [80, -162], [78, -173], [81, -189], [95, -189], [113, -174], [121, -146], [117, -139], [98, -133], [84, -147], [92, -134], [87, -114], [78, -109], [64, -109], [64, -103], [88, -97], [113, -95], [135, -126], [151, -127], [166, -109], [181, -106], [205, -69], [199, -46], [206, -31], [235, -14], [242, -20], [268, -67], [283, -80], [304, -70], [321, -68], [339, -54], [345, -42], [351, -44], [371, -68], [366, -679]]], [[[-465, 870], [-452, 867], [-460, 856], [-518, 851], [-517, 832], [-533, 812], [-530, 799], [-538, 787], [-523, 760], [-526, 742], [-511, 723], [-514, 709], [-523, 698], [-528, 673], [-514, 661], [-510, 649], [-518, 631], [-517, 615], [-477, 581], [-467, 554], [-461, 500], [-447, 477], [-443, 446], [-428, 441], [-413, 452], [-395, 473], [-373, 487], [-352, 474], [-344, 475], [-324, 495], [-302, 490], [-287, 483], [-293, 464], [-292, 451], [-278, 430], [-277, 395], [-281, 375], [-289, 373], [-298, 357], [-295, 337], [-272, 319], [-281, 299], [-282, 273], [-285, 248], [-302, 226], [-298, 219], [-284, 218], [-276, 193], [-270, 185], [-253, 180], [-232, 165], [-211, 140], [-211, 111], [-203, 106], [141, 110], [222, 76], [270, 74], [306, 63], [338, 45], [373, 39], [402, 28], [403, 15], [382, 17], [357, 27], [325, 28], [306, 38], [284, 55], [266, 56], [247, 44], [230, 42], [223, 31], [221, 4], [213, 0], [210, -15], [191, -37], [172, -35], [161, -25], [144, -38], [108, -54], [99, -72], [79, -69], [46, -94], [40, -88], [49, -81], [35, -80], [37, -88], [12, -99], [11, -89], [-12, -98], [-3, -102], [7, -116], [-4, -123], [-8, -146], [-3, -153], [-3, -171], [-11, -188], [-14, -206], [-26, -237], [-26, -267], [-30, -283], [-28, -307], [-20, -342], [-26, -367], [-24, -388], [-34, -404], [-66, -423], [-84, -425], [-108, -412], [-136, -411], [-174, -387], [-176, -377], [-187, -385], [-196, -372], [-206, -381], [-216, -375], [-229, -359], [-246, -347], [-249, -338], [-267, -319], [-277, -299], [-270, -286], [-273, -277], [-250, -251], [-219, -257], [-214, -280], [-219, -292], [-224, -286], [-236, -307], [-216, -301], [-203, -292], [-202, -279], [-206, -250], [-200, -243], [-178, -243], [-174, -250], [-167, -308], [-200, -330], [-200, -339], [-168, -326], [-159, -312], [-163, -269], [-159, -242], [-139, -233], [-133, -225], [-113, -214], [-99, -212], [-88, -201], [-78, -180], [-69, -154], [-56, -152], [-56, -141], [-65, -122], [-79, -116], [-97, -122], [-105, -99], [-104, -129], [-119, -127], [-127, -139], [-189, -183], [-206, -214], [-218, -219], [-231, -212], [-240, -197], [-258, -192], [-250, -202], [-270, -197], [-289, -200], [-275, -208], [-267, -202], [-259, -220], [-242, -224], [-238, -239], [-259, -237], [-265, -246], [-280, -237], [-281, -249], [-272, -256], [-281, -264], [-293, -261], [-287, -273], [-289, -288], [-312, -282], [-295, -303], [-282, -300], [-260, -335], [-287, -338], [-303, -329], [-311, -319], [-322, -317], [-330, -296], [-334, -311], [-338, -296], [-361, -288], [-385, -270], [-406, -259], [-425, -235], [-404, -243], [-370, -234], [-359, -242], [-330, -255], [-334, -267], [-323, -268], [-320, -254], [-339, -236], [-327, -239], [-334, -228], [-315, -229], [-324, -221], [-341, -220], [-347, -230], [-363, -218], [-354, -198], [-361, -198], [-368, -167], [-365, -156], [-375, -143], [-339, -121], [-330, -103], [-313, -112], [-313, -121], [-327, -137], [-329, -149], [-309, -127], [-309, -138], [-289, -129], [-281, -143], [-276, -122], [-262, -112], [-255, -98], [-243, -95], [-237, -83], [-212, -88], [-210, -80], [-183, -93], [-145, -88], [-138, -81], [-120, -77], [-122, -67], [-137, -53], [-147, -57], [-169, -56], [-195, -50], [-219, -55], [-226, -45], [-260, -45], [-268, -48], [-275, -67], [-282, -72], [-281, -84], [-305, -69], [-298, -57], [-280, -51], [-278, -44], [-298, -47], [-309, -59], [-324, -50], [-345, -57], [-358, -46], [-366, -83], [-379, -105], [-376, -108], [-359, -78], [-351, -70], [-348, -78], [-329, -82], [-326, -90], [-344, -117], [-356, -128], [-369, -131], [-381, -144], [-370, -158], [-371, -177], [-368, -195], [-394, -217], [-411, -210], [-408, -220], [-431, -212], [-452, -188], [-436, -191], [-439, -180], [-431, -173], [-418, -180], [-414, -159], [-427, -164], [-438, -160], [-444, -149], [-463, -144], [-476, -137], [-472, -132], [-452, -131], [-445, -137], [-430, -130], [-433, -111], [-424, -107], [-421, -92], [-410, -89], [-400, -46], [-386, -46], [-366, -55], [-368, -45], [-394, -40], [-405, -46], [-418, -40], [-425, -48], [-415, -53], [-411, -67], [-423, -83], [-428, -99], [-440, -87], [-436, -74], [-445, -76], [-451, -64], [-450, -90], [-460, -98], [-469, -96], [-486, -124], [-501, -115], [-495, -103], [-480, -90], [-497, -95], [-501, -82], [-493, -72], [-479, -67], [-491, -59], [-476, -40], [-478, -29], [-485, -38], [-500, -33], [-500, -21], [-491, -21], [-484, -3], [-494, 10], [-489, 29], [-521, 68], [-513, 80], [-497, 47], [-491, 50], [-481, 34], [-471, 42], [-481, 50], [-465, 47], [-446, 29], [-436, 31], [-428, 41], [-424, 63], [-415, 57], [-406, 70], [-389, 75], [-388, 51], [-397, 52], [-403, 30], [-388, 42], [-375, 35], [-375, 28], [-360, 28], [-355, 60], [-348, 75], [-351, 82], [-364, 60], [-366, 40], [-380, 48], [-379, 84], [-374, 77], [-367, 110], [-358, 114], [-339, 102], [-342, 86], [-326, 66], [-321, 66], [-308, 43], [-313, 11], [-317, 2], [-331, 6], [-341, -9], [-352, -13], [-366, -32], [-332, -18], [-314, -23], [-307, -30], [-300, -24], [-322, -17], [-300, 10], [-298, 46], [-332, 87], [-335, 108], [-324, 105], [-318, 115], [-310, 111], [-297, 124], [-308, 145], [-302, 159], [-312, 180], [-333, 195], [-348, 215], [-368, 218], [-386, 224], [-394, 237], [-412, 241], [-417, 257], [-425, 249], [-444, 243], [-440, 231], [-425, 238], [-409, 235], [-406, 225], [-417, 211], [-415, 205], [-399, 228], [-373, 210], [-362, 211], [-344, 183], [-319, 176], [-317, 167], [-330, 147], [-346, 143], [-366, 162], [-391, 160], [-394, 173], [-409, 180], [-423, 177], [-430, 195], [-443, 204], [-450, 199], [-439, 192], [-431, 171], [-408, 172], [-394, 154], [-404, 157], [-403, 142], [-409, 135], [-430, 127], [-407, 130], [-396, 142], [-384, 144], [-380, 135], [-418, 110], [-421, 70], [-429, 66], [-439, 80], [-439, 100], [-446, 120], [-447, 139], [-453, 168], [-465, 189], [-456, 161], [-440, 62], [-440, 45], [-458, 52], [-463, 62], [-485, 73], [-493, 82], [-492, 93], [-500, 107], [-494, 109], [-508, 136], [-500, 138], [-486, 119], [-481, 103], [-468, 86], [-461, 86], [-477, 114], [-487, 145], [-499, 159], [-494, 179], [-504, 167], [-512, 176], [-518, 148], [-539, 174], [-523, 189], [-535, 187], [-557, 195], [-543, 217], [-560, 218], [-566, 254], [-558, 264], [-545, 258], [-539, 246], [-521, 230], [-507, 193], [-506, 207], [-512, 219], [-506, 226], [-516, 230], [-513, 244], [-523, 253], [-529, 269], [-530, 288], [-543, 306], [-539, 314], [-529, 302], [-524, 317], [-534, 319], [-529, 331], [-543, 336], [-540, 323], [-560, 301], [-576, 312], [-585, 333], [-585, 312], [-591, 306], [-605, 312], [-611, 350], [-600, 369], [-605, 375], [-618, 355], [-627, 353], [-633, 380], [-623, 396], [-607, 402], [-579, 397], [-575, 406], [-565, 399], [-562, 387], [-551, 380], [-550, 392], [-567, 413], [-564, 425], [-549, 418], [-553, 458], [-545, 467], [-527, 472], [-511, 454], [-500, 468], [-514, 477], [-528, 479], [-545, 494], [-541, 507], [-525, 517], [-518, 540], [-523, 540], [-532, 519], [-552, 506], [-561, 459], [-557, 442], [-580, 415], [-597, 416], [-613, 432], [-601, 443], [-613, 454], [-617, 441], [-636, 475], [-630, 489], [-641, 495], [-649, 510], [-634, 516], [-619, 483], [-623, 511], [-605, 501], [-576, 495], [-578, 500], [-598, 507], [-598, 520], [-611, 516], [-620, 528], [-601, 546], [-613, 547], [-614, 539], [-630, 530], [-639, 532], [-642, 521], [-656, 536], [-646, 556], [-655, 572], [-662, 543], [-684, 546], [-696, 559], [-688, 573], [-700, 570], [-696, 580], [-713, 575], [-710, 587], [-725, 581], [-727, 593], [-717, 609], [-701, 614], [-693, 608], [-694, 621], [-687, 616], [-667, 620], [-655, 608], [-661, 596], [-651, 598], [-639, 587], [-635, 573], [-618, 584], [-592, 568], [-577, 573], [-596, 577], [-602, 592], [-630, 590], [-637, 594], [-649, 623], [-658, 629], [-672, 647], [-672, 658], [-664, 665], [-642, 658], [-632, 644], [-625, 647], [-613, 638], [-596, 642], [-583, 629], [-589, 648], [-606, 652], [-597, 671], [-589, 674], [-590, 691], [-597, 677], [-605, 676], [-612, 659], [-626, 661], [-633, 672], [-660, 676], [-667, 682], [-673, 704], [-663, 703], [-646, 718], [-627, 719], [-622, 729], [-656, 723], [-668, 725], [-671, 749], [-656, 769], [-624, 779], [-616, 757], [-610, 776], [-600, 772], [-592, 747], [-576, 740], [-562, 724], [-559, 714], [-553, 732], [-569, 746], [-584, 749], [-592, 784], [-599, 789], [-629, 789], [-639, 811], [-633, 857], [-620, 854], [-611, 839], [-604, 842], [-616, 855], [-621, 868], [-616, 884], [-630, 908], [-613, 926], [-596, 918], [-590, 922], [-603, 931], [-629, 925], [-634, 928], [-628, 891], [-635, 875], [-644, 866], [-651, 873], [-646, 854], [-653, 781], [-658, 780], [-678, 805], [-689, 810], [-695, 822], [-690, 833], [-696, 865], [-689, 875], [-704, 901], [-709, 921], [-699, 934], [-704, 945], [-693, 947], [-705, 954], [-712, 974], [-699, 1007], [-692, 998], [-666, 1009], [-641, 1004], [-641, 1004], [-465, 870]]], [[[240, -780], [260, -784], [264, -801], [246, -805], [229, -803], [233, -823], [239, -817], [251, -825], [262, -826], [257, -838], [268, -841], [259, -853], [270, -863], [285, -861], [279, -867], [281, -882], [271, -869], [259, -875], [251, -868], [251, -852], [245, -851], [243, -869], [237, -875], [234, -852], [225, -852], [226, -844], [243, -838], [205, -838], [207, -830], [180, -823], [199, -814], [201, -804], [219, -795], [225, -785], [200, -795], [191, -806], [170, -810], [165, -800], [172, -794], [161, -790], [170, -784], [140, -781], [153, -762], [163, -755], [201, -751], [211, -748], [219, -777], [218, -747], [236, -741], [237, -751], [254, -751], [245, -741], [255, -734], [334, -731], [350, -722], [376, -720], [386, -716], [398, -719], [414, -736], [415, -754], [409, -758], [400, -749], [399, -759], [386, -768], [372, -755], [377, -775], [369, -779], [335, -769], [325, -758], [308, -751], [299, -741], [298, -753], [306, -754], [315, -767], [342, -783], [370, -786], [378, -791], [402, -787], [416, -804], [435, -811], [430, -819], [399, -821], [388, -824], [369, -823], [364, -811], [359, -820], [367, -829], [378, -829], [374, -838], [396, -829], [413, -834], [416, -850], [425, -844], [445, -851], [443, -870], [459, -870], [451, -886], [461, -901], [471, -904], [462, -924], [469, -928], [456, -940], [444, -927], [443, -906], [433, -911], [421, -909], [427, -885], [415, -879], [411, -871], [399, -879], [381, -870], [379, -856], [371, -867], [350, -868], [339, -855], [344, -876], [329, -879], [312, -858], [322, -846], [344, -843], [342, -830], [315, -844], [324, -829], [328, -805], [335, -789], [314, -803], [312, -810], [301, -810], [288, -794], [287, -778], [282, -784], [289, -805], [273, -797], [267, -777], [255, -776], [240, -780]]], [[[-469, -379], [-465, -359], [-448, -360], [-442, -353], [-423, -359], [-417, -371], [-400, -379], [-361, -378], [-359, -367], [-368, -374], [-379, -373], [-373, -359], [-380, -360], [-392, -374], [-410, -371], [-416, -363], [-411, -349], [-425, -347], [-438, -332], [-434, -323], [-419, -328], [-412, -336], [-397, -331], [-381, -340], [-391, -328], [-397, -307], [-375, -299], [-376, -312], [-370, -327], [-368, -311], [-372, -304], [-355, -309], [-358, -335], [-346, -339], [-351, -367], [-346, -380], [-336, -339], [-345, -320], [-331, -323], [-323, -332], [-325, -340], [-315, -351], [-311, -376], [-304, -372], [-310, -337], [-288, -352], [-280, -346], [-265, -363], [-279, -375], [-271, -383], [-270, -396], [-260, -378], [-255, -384], [-236, -388], [-242, -397], [-221, -407], [-234, -418], [-237, -431], [-250, -420], [-253, -446], [-263, -436], [-256, -452], [-264, -458], [-248, -463], [-248, -476], [-265, -479], [-269, -464], [-280, -462], [-283, -496], [-298, -485], [-297, -476], [-310, -492], [-310, -498], [-323, -496], [-332, -508], [-346, -505], [-340, -494], [-356, -499], [-363, -493], [-348, -487], [-325, -486], [-340, -480], [-337, -475], [-320, -478], [-331, -469], [-323, -463], [-334, -459], [-319, -439], [-303, -429], [-324, -433], [-317, -423], [-331, -430], [-332, -419], [-345, -433], [-357, -429], [-363, -436], [-370, -418], [-379, -447], [-376, -459], [-368, -465], [-374, -481], [-394, -471], [-406, -470], [-417, -462], [-406, -455], [-401, -445], [-415, -431], [-406, -414], [-416, -402], [-406, -394], [-411, -390], [-454, -407], [-463, -409], [-446, -392], [-449, -382], [-469, -379]]], [[[-834, 718], [-843, 722], [-849, 744], [-841, 758], [-853, 756], [-846, 775], [-858, 769], [-859, 784], [-875, 792], [-889, 817], [-883, 833], [-874, 833], [-854, 797], [-829, 774], [-834, 792], [-849, 801], [-862, 825], [-864, 839], [-852, 840], [-833, 812], [-838, 834], [-823, 827], [-824, 840], [-811, 844], [-817, 858], [-790, 831], [-782, 817], [-783, 863], [-777, 870], [-781, 889], [-771, 892], [-801, 917], [-784, 927], [-776, 919], [-778, 935], [-786, 936], [-799, 924], [-808, 928], [-803, 942], [-815, 943], [-813, 951], [-790, 965], [-815, 954], [-825, 964], [-819, 976], [-804, 969], [-817, 982], [-785, 1009], [-777, 993], [-776, 1010], [-757, 1012], [-753, 1000], [-744, 999], [-744, 976], [-751, 964], [-741, 969], [-743, 991], [-736, 1006], [-727, 1004], [-725, 963], [-729, 948], [-720, 952], [-713, 941], [-717, 914], [-707, 892], [-709, 884], [-702, 866], [-701, 835], [-705, 796], [-713, 781], [-705, 764], [-714, 749], [-714, 739], [-721, 728], [-719, 720], [-710, 736], [-709, 748], [-693, 745], [-687, 716], [-708, 714], [-692, 708], [-688, 697], [-690, 676], [-693, 685], [-703, 683], [-693, 669], [-692, 659], [-703, 654], [-718, 666], [-703, 644], [-724, 626], [-732, 632], [-728, 642], [-744, 624], [-763, 633], [-756, 648], [-735, 666], [-747, 666], [-766, 649], [-773, 661], [-772, 672], [-754, 673], [-741, 684], [-761, 684], [-750, 704], [-759, 704], [-770, 694], [-768, 711], [-779, 724], [-777, 730], [-766, 725], [-776, 737], [-776, 746], [-753, 740], [-744, 746], [-775, 757], [-777, 769], [-759, 786], [-751, 776], [-754, 793], [-739, 817], [-741, 823], [-754, 810], [-754, 802], [-771, 780], [-778, 786], [-784, 769], [-791, 769], [-797, 783], [-800, 769], [-797, 727], [-787, 738], [-797, 708], [-785, 697], [-799, 684], [-793, 682], [-791, 664], [-815, 674], [-830, 700], [-834, 718]]]]}}, {"type": "Feature", "id": "CL.CO", "properties": {"hc-group": "admin1", "hc-middle-x": 0.51, "hc-middle-y": 0.39, "hc-key": "cl-co", "hc-a2": "CO", "labelrank": "3", "hasc": "CL.CO", "alt-name": null, "woe-id": "2345024", "subregion": null, "fips": "CI07", "postal-code": "CO", "name": "Coquimbo", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-70.7749", "woe-name": "Coquimbo", "latitude": "-30.5838", "woe-label": "Coquimbo Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-253, 6542], [-237, 6513], [-218, 6514], [-205, 6499], [-193, 6506], [-182, 6550], [-137, 6563], [-124, 6574], [-121, 6585], [-107, 6589], [-83, 6577], [-77, 6560], [-56, 6476], [-44, 6454], [-28, 6443], [-7, 6416], [14, 6412], [39, 6400], [60, 6400], [78, 6409], [82, 6384], [80, 6346], [68, 6304], [82, 6305], [99, 6294], [99, 6273], [90, 6267], [86, 6241], [71, 6224], [55, 6217], [26, 6229], [20, 6226], [26, 6199], [10, 6183], [-3, 6143], [-4, 6124], [-14, 6103], [-19, 6064], [-1, 6040], [-18, 6034], [-28, 6012], [-41, 6006], [-52, 6019], [-66, 5997], [-68, 5971], [-73, 5960], [-69, 5943], [-74, 5929], [-77, 5890], [-61, 5853], [-51, 5840], [-51, 5821], [-37, 5800], [-9, 5797], [8, 5775], [0, 5754], [-23, 5755], [-26, 5746], [-14, 5725], [-12, 5705], [-24, 5702], [-44, 5706], [-56, 5723], [-88, 5737], [-107, 5754], [-134, 5758], [-164, 5769], [-175, 5755], [-185, 5753], [-207, 5764], [-223, 5759], [-255, 5738], [-276, 5749], [-303, 5704], [-296, 5765], [-301, 5783], [-295, 5790], [-305, 5805], [-299, 5822], [-314, 5874], [-314, 5894], [-324, 5922], [-335, 5984], [-340, 5998], [-337, 6021], [-338, 6047], [-344, 6063], [-352, 6102], [-351, 6122], [-355, 6149], [-351, 6169], [-348, 6226], [-338, 6256], [-332, 6245], [-309, 6245], [-293, 6278], [-285, 6271], [-277, 6283], [-278, 6311], [-285, 6326], [-255, 6350], [-254, 6367], [-265, 6385], [-268, 6406], [-257, 6437], [-269, 6458], [-263, 6488], [-273, 6517], [-281, 6527], [-308, 6546], [-285, 6554], [-271, 6552], [-253, 6542]]]}}, {"type": "Feature", "id": "CL.AT", "properties": {"hc-group": "admin1", "hc-middle-x": 0.58, "hc-middle-y": 0.4, "hc-key": "cl-at", "hc-a2": "AT", "labelrank": "3", "hasc": "CL.AT", "alt-name": null, "woe-id": "2345022", "subregion": null, "fips": "CI05", "postal-code": "AT", "name": "Atacama", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-69.9123", "woe-name": null, "latitude": "-27.4469", "woe-label": null, "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-253, 6542], [-285, 6554], [-308, 6546], [-310, 6596], [-319, 6623], [-311, 6650], [-287, 6663], [-279, 6684], [-266, 6699], [-263, 6718], [-268, 6730], [-258, 6737], [-259, 6752], [-244, 6763], [-235, 6788], [-241, 6806], [-235, 6814], [-232, 6849], [-236, 6863], [-230, 6876], [-228, 6905], [-219, 6925], [-221, 6935], [-207, 6968], [-205, 6984], [-183, 6986], [-173, 6997], [-175, 7019], [-169, 7034], [-183, 7068], [-180, 7089], [-186, 7104], [-187, 7127], [-183, 7141], [-165, 7140], [-160, 7159], [-145, 7174], [-154, 7205], [-145, 7233], [-132, 7244], [-138, 7254], [-131, 7263], [-122, 7302], [-126, 7316], [-121, 7327], [-124, 7345], [-107, 7359], [-120, 7370], [-112, 7389], [-118, 7410], [-116, 7435], [-109, 7448], [-88, 7478], [-50, 7506], [-17, 7506], [14, 7531], [38, 7536], [48, 7525], [82, 7529], [125, 7553], [162, 7557], [194, 7548], [287, 7557], [291, 7573], [307, 7600], [303, 7627], [307, 7632], [349, 7635], [380, 7633], [412, 7655], [421, 7658], [408, 7617], [409, 7596], [421, 7579], [420, 7553], [435, 7527], [438, 7504], [455, 7431], [455, 7407], [422, 7382], [415, 7372], [410, 7329], [416, 7303], [479, 7212], [483, 7197], [476, 7163], [464, 7163], [429, 7154], [411, 7130], [395, 7134], [378, 7146], [357, 7144], [337, 7114], [336, 7093], [324, 7065], [304, 7050], [300, 7021], [284, 6991], [288, 6980], [276, 6969], [264, 6916], [253, 6903], [231, 6894], [215, 6857], [202, 6834], [188, 6841], [180, 6819], [144, 6782], [139, 6765], [139, 6735], [123, 6713], [119, 6694], [124, 6665], [107, 6623], [108, 6584], [81, 6571], [63, 6553], [66, 6543], [52, 6533], [49, 6509], [55, 6497], [68, 6447], [67, 6423], [78, 6409], [60, 6400], [39, 6400], [14, 6412], [-7, 6416], [-28, 6443], [-44, 6454], [-56, 6476], [-77, 6560], [-83, 6577], [-107, 6589], [-121, 6585], [-124, 6574], [-137, 6563], [-182, 6550], [-193, 6506], [-205, 6499], [-218, 6514], [-237, 6513], [-253, 6542]]]}}, {"type": "Feature", "id": "CL.VS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.83, "hc-middle-y": 0.21, "hc-key": "cl-vs", "hc-a2": "VS", "labelrank": "3", "hasc": "CL.VS", "alt-name": "Aconcagua", "woe-id": "2345018", "subregion": null, "fips": "CI01", "postal-code": "VS", "name": "Valparaíso", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-70.93600000000001", "woe-name": null, "latitude": "-32.4884", "woe-label": null, "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-332, 5201], [-347, 5210], [-369, 5211], [-355, 5256], [-327, 5272], [-320, 5291], [-313, 5323], [-317, 5335], [-333, 5353], [-334, 5366], [-326, 5375], [-327, 5391], [-335, 5406], [-339, 5428], [-349, 5447], [-332, 5452], [-322, 5471], [-304, 5476], [-301, 5496], [-293, 5513], [-300, 5534], [-289, 5543], [-291, 5558], [-281, 5562], [-277, 5578], [-285, 5608], [-277, 5623], [-271, 5655], [-283, 5665], [-287, 5681], [-303, 5704], [-276, 5749], [-255, 5738], [-223, 5759], [-207, 5764], [-185, 5753], [-175, 5755], [-164, 5769], [-134, 5758], [-107, 5754], [-88, 5737], [-56, 5723], [-44, 5706], [-24, 5702], [-12, 5705], [-10, 5685], [9, 5671], [9, 5650], [27, 5633], [30, 5612], [25, 5587], [35, 5548], [56, 5525], [69, 5518], [61, 5489], [48, 5480], [44, 5468], [34, 5466], [-2, 5434], [-27, 5457], [-47, 5462], [-77, 5488], [-92, 5477], [-108, 5492], [-149, 5484], [-184, 5463], [-272, 5382], [-273, 5355], [-253, 5333], [-252, 5287], [-264, 5271], [-259, 5252], [-286, 5241], [-304, 5218], [-332, 5201]]]}}, {"type": "Feature", "id": "CL.RM", "properties": {"hc-group": "admin1", "hc-middle-x": 0.75, "hc-middle-y": 0.51, "hc-key": "cl-rm", "hc-a2": "RM", "labelrank": "7", "hasc": "CL.RM", "alt-name": "Región Metropolitana|Région Metropolitaine de Santiago|Región Metropolitana|RM", "woe-id": "2345029", "subregion": null, "fips": "CI12", "postal-code": "RM", "name": "Región Metropolitana de Santiago", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-70.7527", "woe-name": "Región Metropolitana de Santiago", "latitude": "-33.4568", "woe-label": "Santiago Metropolitan Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-332, 5201], [-304, 5218], [-286, 5241], [-259, 5252], [-264, 5271], [-252, 5287], [-253, 5333], [-273, 5355], [-272, 5382], [-184, 5463], [-149, 5484], [-108, 5492], [-92, 5477], [-77, 5488], [-47, 5462], [-27, 5457], [-2, 5434], [34, 5466], [44, 5468], [48, 5457], [46, 5430], [58, 5419], [68, 5398], [84, 5414], [92, 5416], [114, 5401], [121, 5376], [110, 5354], [108, 5333], [99, 5326], [96, 5296], [104, 5278], [93, 5268], [96, 5237], [93, 5213], [106, 5205], [102, 5161], [112, 5132], [93, 5120], [75, 5125], [61, 5120], [51, 5141], [38, 5151], [31, 5173], [24, 5180], [4, 5180], [-5, 5187], [-15, 5209], [-36, 5228], [-66, 5217], [-88, 5217], [-119, 5210], [-120, 5190], [-159, 5148], [-178, 5144], [-205, 5150], [-233, 5171], [-246, 5166], [-272, 5181], [-294, 5188], [-310, 5183], [-332, 5201]]]}}, {"type": "Feature", "id": "CL.AR", "properties": {"hc-group": "admin1", "hc-middle-x": 0.72, "hc-middle-y": 0.52, "hc-key": "cl-ar", "hc-a2": "AR", "labelrank": "3", "hasc": "CL.AR", "alt-name": "XIV", "woe-id": "56043703", "subregion": null, "fips": "CI09", "postal-code": "AR", "name": "Los Ríos", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-72.6829", "woe-name": "Los Ríos", "latitude": "-40.1186", "woe-label": "Los Rios Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-270, 3612], [-284, 3616], [-286, 3599], [-281, 3591], [-285, 3576], [-274, 3539], [-263, 3529], [-264, 3519], [-276, 3494], [-275, 3471], [-301, 3475], [-305, 3438], [-288, 3416], [-278, 3419], [-272, 3403], [-282, 3379], [-298, 3377], [-309, 3339], [-316, 3330], [-341, 3312], [-354, 3309], [-424, 3302], [-452, 3302], [-475, 3307], [-494, 3316], [-531, 3341], [-546, 3376], [-556, 3388], [-571, 3391], [-601, 3386], [-656, 3400], [-703, 3397], [-721, 3403], [-716, 3423], [-719, 3431], [-704, 3450], [-708, 3472], [-718, 3486], [-710, 3499], [-701, 3500], [-676, 3514], [-664, 3530], [-655, 3528], [-652, 3513], [-646, 3524], [-652, 3539], [-648, 3557], [-655, 3570], [-633, 3603], [-622, 3630], [-619, 3650], [-624, 3659], [-601, 3659], [-578, 3667], [-561, 3664], [-557, 3656], [-563, 3612], [-560, 3604], [-521, 3607], [-468, 3599], [-443, 3586], [-429, 3569], [-411, 3575], [-395, 3567], [-362, 3568], [-346, 3573], [-334, 3585], [-306, 3649], [-295, 3652], [-280, 3641], [-270, 3612]]]}}, {"type": "Feature", "id": "CL.ML", "properties": {"hc-group": "admin1", "hc-middle-x": 0.56, "hc-middle-y": 0.47, "hc-key": "cl-ml", "hc-a2": "ML", "labelrank": "3", "hasc": "CL.ML", "alt-name": "VII", "woe-id": "2345028", "subregion": null, "fips": "CI06", "postal-code": "ML", "name": "<PERSON><PERSON>", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-71.58110000000001", "woe-name": "<PERSON><PERSON>", "latitude": "-35.6067", "woe-label": "Maule Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-12, 4910], [-10, 4890], [-14, 4870], [-50, 4858], [-59, 4843], [-54, 4832], [-34, 4828], [-23, 4816], [-33, 4810], [-27, 4787], [-18, 4774], [-20, 4731], [-5, 4687], [-19, 4672], [-12, 4661], [-17, 4644], [-9, 4622], [-25, 4592], [-36, 4588], [-52, 4595], [-61, 4578], [-82, 4560], [-88, 4536], [-85, 4517], [-105, 4511], [-124, 4520], [-134, 4499], [-155, 4496], [-165, 4514], [-174, 4518], [-207, 4513], [-239, 4525], [-321, 4567], [-330, 4568], [-392, 4591], [-489, 4602], [-495, 4606], [-541, 4617], [-564, 4605], [-566, 4623], [-561, 4634], [-540, 4654], [-536, 4665], [-521, 4676], [-518, 4687], [-522, 4709], [-534, 4732], [-534, 4744], [-508, 4766], [-499, 4787], [-498, 4803], [-487, 4811], [-480, 4840], [-455, 4859], [-442, 4875], [-437, 4896], [-435, 4940], [-416, 4976], [-389, 4962], [-378, 4952], [-349, 4945], [-329, 4925], [-307, 4934], [-261, 4932], [-251, 4937], [-234, 4957], [-222, 4955], [-202, 4965], [-148, 4939], [-114, 4940], [-103, 4935], [-83, 4916], [-68, 4912], [-45, 4917], [-12, 4910]]]}}, {"type": "Feature", "id": "CL.TA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.52, "hc-middle-y": 0.5, "hc-key": "cl-ta", "hc-a2": "TA", "labelrank": "3", "hasc": "CL.TA", "alt-name": "Tarapaca", "woe-id": "2345030", "subregion": null, "fips": "CI13", "postal-code": "TA", "name": "Tarapacá", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-69.36830000000001", "woe-name": "Tarapacá", "latitude": "-20.2427", "woe-label": "Tarapaca Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[27, 8740], [18, 8769], [25, 8780], [22, 8801], [8, 8831], [9, 8841], [-2, 8867], [9, 8884], [1, 8907], [-9, 8918], [-12, 8939], [-10, 8966], [-13, 8999], [-3, 9022], [-10, 9044], [3, 9055], [6, 9073], [-2, 9086], [1, 9109], [8, 9128], [8, 9148], [-2, 9199], [0, 9227], [-9, 9252], [-17, 9261], [-13, 9279], [-21, 9314], [-37, 9345], [-33, 9381], [-17, 9386], [36, 9417], [96, 9433], [130, 9434], [157, 9423], [183, 9421], [227, 9405], [255, 9402], [271, 9405], [286, 9417], [310, 9446], [329, 9415], [356, 9399], [388, 9370], [397, 9353], [443, 9324], [455, 9308], [442, 9301], [406, 9250], [390, 9235], [387, 9220], [426, 9189], [429, 9171], [417, 9133], [399, 9134], [367, 9124], [363, 9113], [380, 9106], [379, 9084], [392, 9056], [377, 9051], [368, 9039], [379, 9014], [390, 9003], [418, 8990], [445, 8973], [420, 8940], [420, 8903], [431, 8887], [427, 8860], [421, 8846], [399, 8839], [386, 8828], [372, 8808], [361, 8799], [275, 8772], [216, 8750], [188, 8741], [174, 8742], [145, 8756], [92, 8757], [73, 8750], [66, 8739], [27, 8740]]]}}, {"type": "Feature", "id": "CL.2740", "properties": {"hc-group": "admin1", "hc-middle-x": 0.46, "hc-middle-y": 0.51, "hc-key": "cl-2740", "hc-a2": "AY", "labelrank": "7", "hasc": "CL.", "alt-name": null, "woe-id": "56043702", "subregion": null, "fips": null, "postal-code": null, "name": "Arica y Parinacota", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-69.68040000000001", "woe-name": "Arica y Parinacota", "latitude": "-18.3207", "woe-label": "Arica and Parinacota Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[-33, 9381], [-35, 9399], [-45, 9423], [-44, 9437], [-57, 9493], [-52, 9521], [-56, 9532], [-43, 9586], [-68, 9616], [-47, 9620], [-19, 9618], [2, 9621], [46, 9641], [74, 9671], [96, 9717], [83, 9767], [76, 9787], [86, 9808], [122, 9809], [135, 9817], [169, 9851], [170, 9829], [179, 9810], [210, 9780], [217, 9767], [219, 9736], [226, 9719], [236, 9722], [244, 9714], [269, 9704], [282, 9706], [283, 9689], [265, 9673], [280, 9646], [286, 9600], [298, 9578], [296, 9543], [306, 9514], [304, 9502], [319, 9471], [310, 9446], [286, 9417], [271, 9405], [255, 9402], [227, 9405], [183, 9421], [157, 9423], [130, 9434], [96, 9433], [36, 9417], [-17, 9386], [-33, 9381]]]}}, {"type": "Feature", "id": "CL.AN", "properties": {"hc-group": "admin1", "hc-middle-x": 0.4, "hc-middle-y": 0.5, "hc-key": "cl-an", "hc-a2": "AN", "labelrank": "6", "hasc": "CL.AN", "alt-name": null, "woe-id": "2345020", "subregion": null, "fips": "CI03", "postal-code": "AN", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Chile", "type-en": "Region", "region": null, "longitude": "-68.87390000000001", "woe-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "latitude": "-23.3178", "woe-label": "Antofagasta Region, CL, Chile", "type": "Región"}, "geometry": {"type": "Polygon", "coordinates": [[[421, 7658], [412, 7655], [380, 7633], [349, 7635], [307, 7632], [303, 7627], [307, 7600], [291, 7573], [287, 7557], [194, 7548], [162, 7557], [125, 7553], [82, 7529], [48, 7525], [38, 7536], [14, 7531], [-17, 7506], [-50, 7506], [-88, 7478], [-109, 7448], [-113, 7471], [-137, 7503], [-127, 7519], [-125, 7542], [-111, 7561], [-115, 7585], [-108, 7595], [-85, 7609], [-81, 7629], [-68, 7630], [-63, 7641], [-62, 7684], [-77, 7709], [-71, 7745], [-84, 7761], [-99, 7813], [-99, 7835], [-94, 7849], [-101, 7872], [-91, 7903], [-94, 7917], [-82, 7973], [-81, 7992], [-88, 8021], [-82, 8044], [-85, 8060], [-82, 8081], [-58, 8119], [-54, 8139], [-60, 8158], [-82, 8169], [-93, 8152], [-117, 8156], [-105, 8182], [-110, 8201], [-110, 8233], [-101, 8245], [-105, 8271], [-100, 8285], [-86, 8299], [-80, 8278], [-71, 8274], [-51, 8288], [-32, 8325], [-29, 8339], [-36, 8361], [-25, 8410], [-28, 8427], [-18, 8444], [-21, 8455], [-12, 8561], [-1, 8603], [-6, 8617], [5, 8623], [4, 8679], [16, 8695], [27, 8740], [66, 8739], [73, 8750], [92, 8757], [145, 8756], [174, 8742], [188, 8741], [216, 8750], [275, 8772], [361, 8799], [372, 8808], [386, 8828], [399, 8839], [421, 8846], [427, 8860], [431, 8887], [461, 8878], [516, 8787], [520, 8774], [518, 8706], [537, 8666], [542, 8644], [545, 8598], [572, 8568], [581, 8549], [576, 8540], [586, 8514], [583, 8485], [596, 8460], [606, 8425], [597, 8383], [601, 8350], [612, 8348], [628, 8334], [686, 8332], [779, 8351], [825, 8300], [824, 8291], [737, 8019], [731, 8011], [563, 7938], [502, 7913], [484, 7884], [462, 7881], [448, 7844], [435, 7849], [417, 7805], [421, 7779], [441, 7768], [451, 7749], [450, 7734], [469, 7711], [466, 7701], [442, 7699], [431, 7688], [421, 7658]]]}}]}