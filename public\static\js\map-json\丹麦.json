{"title": "Denmark", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:32632"}}, "hc-transform": {"default": {"crs": "+proj=utm +zone=32 +datum=WGS84 +units=m +no_defs", "scale": 0.00155831624948, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 442861.310439, "yoffset": 6402126.29055}}, "features": [{"type": "Feature", "id": "DK.6326", "properties": {"hc-group": "admin1", "hc-middle-x": 0.4, "hc-middle-y": 0.44, "hc-key": "dk-6326", "hc-a2": "MI", "labelrank": "7", "hasc": "DK.", "alt-name": "Central", "woe-id": "28362580", "subregion": null, "fips": null, "postal-code": null, "name": "Midtjylland", "country": "Denmark", "type-en": "Region", "region": null, "longitude": "9.26862", "woe-name": "Midtjylland", "latitude": "56.2367", "woe-label": "Midtjylland, DK, Denmark", "type": "Region"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[2383, 4450], [2372, 4442], [2269, 4457], [2264, 4482], [2343, 4498], [2360, 4561], [2383, 4539], [2383, 4450]]], [[[2831, 4821], [2858, 4785], [2898, 4841], [2903, 4824], [2844, 4730], [2841, 4592], [2827, 4544], [2779, 4497], [2726, 4511], [2684, 4573], [2668, 4671], [2676, 4726], [2738, 4783], [2769, 4859], [2767, 4905], [2746, 4950], [2687, 4982], [2667, 5010], [2670, 5077], [2707, 5136], [2726, 5131], [2740, 5018], [2765, 4972], [2839, 4932], [2821, 4876], [2831, 4821]]], [[[4200, 7019], [4145, 7010], [4097, 7053], [4108, 7108], [4233, 7126], [4296, 7153], [4297, 7133], [4252, 7102], [4200, 7019]]], [[[-867, 4589], [-922, 5032], [-933, 5091], [-899, 5062], [-864, 4855], [-838, 4784], [-853, 4692], [-840, 4617], [-771, 4654], [-698, 4711], [-605, 4769], [-544, 4832], [-544, 4922], [-589, 5009], [-644, 5083], [-644, 5167], [-660, 5263], [-698, 5325], [-749, 5363], [-865, 5418], [-910, 5400], [-910, 5285], [-923, 5170], [-922, 5108], [-944, 5115], [-962, 5190], [-966, 5385], [-961, 5446], [-922, 5613], [-917, 5676], [-922, 6607], [-904, 6698], [-835, 6930], [-781, 7036], [-755, 6973], [-801, 6844], [-755, 6826], [-725, 6751], [-664, 6739], [-669, 6647], [-647, 6611], [-631, 6636], [-640, 6677], [-593, 6692], [-521, 6693], [-455, 6679], [-340, 6583], [-300, 6615], [-263, 6674], [-219, 6547], [-212, 6465], [-159, 6407], [-117, 6396], [-26, 6416], [-2, 6455], [21, 6654], [-51, 6699], [-66, 6760], [-91, 6774], [-71, 6819], [-3, 6823], [93, 6986], [176, 7018], [258, 7021], [226, 7041], [161, 7043], [146, 7058], [160, 7106], [199, 7171], [316, 7274], [343, 7281], [420, 7265], [448, 7299], [508, 7289], [537, 7267], [559, 7171], [622, 7078], [641, 7021], [600, 7011], [557, 6941], [519, 6930], [525, 6886], [469, 6820], [455, 6782], [460, 6663], [471, 6637], [520, 6670], [539, 6758], [612, 6764], [590, 6799], [561, 6784], [611, 6894], [602, 6833], [694, 6834], [747, 6816], [790, 6818], [800, 6758], [753, 6672], [798, 6603], [866, 6543], [896, 6601], [917, 6582], [934, 6637], [901, 6655], [852, 6619], [817, 6625], [827, 6754], [864, 6917], [1026, 6834], [1062, 6896], [1060, 6960], [1103, 6957], [1125, 6932], [1116, 6868], [1158, 6822], [1326, 6822], [1355, 6794], [1317, 6749], [1307, 6690], [1336, 6681], [1384, 6708], [1458, 6624], [1523, 6643], [1588, 6616], [1650, 6626], [1699, 6685], [1793, 6732], [1881, 6744], [1982, 6809], [2030, 6880], [2098, 6939], [2146, 6996], [2219, 6983], [2280, 6988], [2354, 7027], [2383, 6968], [2392, 6880], [2368, 6803], [2269, 6724], [2184, 6633], [2170, 6520], [2176, 6451], [2142, 6393], [2205, 6411], [2204, 6587], [2219, 6623], [2280, 6671], [2328, 6738], [2354, 6757], [2385, 6737], [2401, 6646], [2453, 6601], [2565, 6539], [2687, 6528], [3039, 6592], [3088, 6588], [3160, 6537], [3210, 6454], [3305, 6363], [3272, 6311], [3255, 6249], [3250, 6114], [3236, 6053], [3104, 5853], [3035, 5803], [3003, 5730], [3009, 5646], [2990, 5564], [2942, 5553], [2889, 5590], [2853, 5652], [2907, 5674], [2909, 5744], [2819, 5783], [2751, 5678], [2704, 5621], [2745, 5445], [2681, 5410], [2647, 5439], [2619, 5516], [2670, 5554], [2645, 5598], [2583, 5615], [2494, 5580], [2468, 5601], [2411, 5679], [2444, 5683], [2522, 5736], [2596, 5683], [2615, 5721], [2589, 5764], [2590, 5797], [2632, 5887], [2590, 5895], [2537, 5941], [2499, 5922], [2478, 5939], [2466, 5880], [2421, 5850], [2383, 5761], [2347, 5713], [2268, 5644], [2232, 5593], [2212, 5529], [2223, 5490], [2273, 5399], [2284, 5331], [2279, 5279], [2235, 5178], [2297, 5200], [2305, 5181], [2268, 5096], [2269, 4947], [2261, 4903], [2170, 4792], [2195, 4730], [2170, 4684], [2122, 4695], [2080, 4789], [2046, 4813], [1899, 4804], [1848, 4748], [1809, 4732], [1730, 4736], [1693, 4726], [1731, 4684], [1949, 4639], [1964, 4628], [1923, 4497], [1927, 4476], [1988, 4473], [1988, 4455], [1926, 4418], [1909, 4388], [1927, 4344], [1907, 4323], [1871, 4345], [1705, 4294], [1629, 4252], [1585, 4247], [1491, 4283], [1437, 4330], [1406, 4337], [1414, 4391], [1388, 4430], [1304, 4446], [1285, 4477], [1298, 4525], [1262, 4546], [1185, 4548], [1090, 4680], [1062, 4757], [985, 4853], [899, 4917], [757, 4963], [700, 4950], [660, 4866], [590, 4777], [537, 4748], [488, 4674], [401, 4655], [274, 4793], [200, 4800], [145, 4773], [105, 4669], [113, 4606], [40, 4575], [14, 4584], [-92, 4660], [-141, 4673], [-208, 4655], [-256, 4675], [-302, 4657], [-295, 4611], [-391, 4504], [-438, 4503], [-575, 4589], [-867, 4589]]]]}}, {"type": "Feature", "id": "DK.3564", "properties": {"hc-group": "admin1", "hc-middle-x": 0.35, "hc-middle-y": 0.4, "hc-key": "dk-3564", "hc-a2": "SY", "labelrank": "7", "hasc": "DK.", "alt-name": "Southern", "woe-id": "28362581", "subregion": null, "fips": null, "postal-code": null, "name": "Syddanmark", "country": "Denmark", "type-en": "Region", "region": null, "longitude": "9.129200000000001", "woe-name": "Syddanmark", "latitude": "55.349", "woe-label": "Syddanmark, DK, Denmark", "type": "Region"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[2472, 2170], [2536, 2168], [2531, 2235], [2554, 2203], [2574, 2123], [2591, 2107], [2645, 2114], [2627, 2182], [2705, 2115], [2705, 2073], [2736, 2055], [2615, 2025], [2563, 1978], [2528, 1994], [2256, 2281], [2220, 2383], [2243, 2358], [2375, 2287], [2415, 2252], [2472, 2170]]], [[[1605, 2652], [1698, 2543], [1747, 2518], [1816, 2508], [1865, 2478], [1906, 2432], [1968, 2331], [2031, 2147], [2031, 2120], [1965, 2115], [1882, 2060], [1834, 2090], [1762, 2112], [1736, 2149], [1776, 2170], [1918, 2096], [1918, 2114], [1843, 2178], [1772, 2203], [1741, 2198], [1704, 2166], [1643, 2169], [1590, 2211], [1559, 2281], [1563, 2368], [1588, 2361], [1661, 2289], [1692, 2296], [1672, 2369], [1608, 2414], [1597, 2435], [1615, 2479], [1494, 2466], [1422, 2489], [1414, 2514], [1456, 2569], [1377, 2553], [1351, 2588], [1486, 2654], [1540, 2663], [1605, 2652]]], [[[3225, 2296], [3088, 1796], [3073, 1762], [3015, 1748], [2996, 1773], [2953, 1930], [2914, 1983], [2865, 2021], [2926, 2036], [2969, 2099], [2991, 2158], [3014, 2140], [3053, 2155], [3041, 2211], [3015, 2195], [2987, 2210], [3008, 2275], [3039, 2324], [3123, 2426], [3160, 2454], [3244, 2584], [3253, 2660], [3301, 2794], [3358, 2898], [3393, 2883], [3328, 2549], [3301, 2460], [3225, 2296]]], [[[-518, 3611], [-461, 3602], [-439, 3580], [-460, 3488], [-434, 3382], [-421, 3359], [-459, 3344], [-499, 3387], [-534, 3453], [-580, 3581], [-590, 3639], [-574, 3677], [-523, 3691], [-518, 3611]]], [[[2417, 4087], [2524, 4016], [2582, 3937], [2650, 3918], [2696, 3887], [2648, 3896], [2624, 3850], [2653, 3820], [2649, 3782], [2605, 3767], [2549, 3683], [2575, 3639], [2630, 3642], [2658, 3703], [2676, 3698], [2773, 3752], [2830, 3772], [2818, 3825], [2777, 3849], [2774, 3886], [2823, 3845], [2842, 3875], [2809, 4010], [2841, 3974], [2828, 4104], [2857, 4106], [2903, 4057], [2978, 3939], [2987, 3876], [3032, 3830], [3043, 3793], [3027, 3756], [2913, 3693], [2807, 3701], [2763, 3678], [2770, 3627], [2817, 3641], [2819, 3691], [2952, 3663], [2976, 3643], [3132, 3427], [3184, 3289], [3195, 3245], [3136, 3290], [3111, 3282], [3101, 3234], [3153, 3137], [3171, 3039], [3159, 2940], [3137, 2885], [3133, 2811], [3091, 2745], [3055, 2654], [3012, 2628], [2882, 2629], [2828, 2610], [2756, 2534], [2723, 2537], [2661, 2570], [2558, 2576], [2483, 2617], [2406, 2618], [2284, 2692], [2239, 2696], [2228, 2616], [2167, 2675], [2110, 2690], [2052, 2674], [2024, 2688], [2141, 2780], [2161, 2819], [2093, 2935], [2063, 2947], [1980, 2926], [1924, 2969], [1906, 2945], [1911, 2903], [1966, 2822], [1961, 2777], [1899, 2782], [1903, 2847], [1876, 2935], [1886, 3010], [1801, 3039], [1764, 3066], [1738, 3118], [1755, 3190], [1733, 3281], [1736, 3374], [1720, 3386], [1652, 3382], [1610, 3409], [1572, 3456], [1651, 3481], [1666, 3511], [1582, 3547], [1634, 3547], [1590, 3595], [1508, 3611], [1455, 3675], [1496, 3674], [1583, 3610], [1622, 3620], [1470, 3753], [1413, 3765], [1425, 3793], [1476, 3822], [1532, 3897], [1614, 3914], [1642, 3909], [1662, 3843], [1736, 3804], [1815, 3833], [1965, 3939], [2160, 4008], [2227, 4058], [2317, 4073], [2329, 4018], [2355, 4047], [2349, 4102], [2417, 4087]]], [[[1406, 4337], [1266, 4337], [1230, 4354], [1220, 4316], [1252, 4301], [1361, 4300], [1460, 4208], [1504, 4126], [1534, 4118], [1680, 4120], [1655, 4089], [1577, 4026], [1535, 3970], [1466, 3939], [1459, 3877], [1363, 3852], [1307, 3821], [1255, 3840], [1223, 3789], [1167, 3770], [1141, 3746], [1197, 3731], [1300, 3764], [1389, 3717], [1395, 3687], [1373, 3618], [1278, 3580], [1311, 3489], [1301, 3415], [1370, 3353], [1354, 3306], [1427, 3171], [1465, 3146], [1472, 3102], [1433, 2964], [1369, 2939], [1278, 2955], [1221, 2899], [1142, 2862], [1130, 2826], [1169, 2789], [1124, 2790], [1084, 2768], [1164, 2752], [1207, 2718], [1244, 2661], [1204, 2608], [1150, 2567], [1079, 2552], [1052, 2531], [1084, 2492], [1171, 2528], [1224, 2529], [1276, 2568], [1310, 2554], [1405, 2459], [1490, 2436], [1523, 2382], [1561, 2158], [1523, 2145], [1483, 2161], [1471, 2127], [1509, 2110], [1535, 2074], [1538, 1993], [1497, 1992], [1461, 2054], [1397, 2048], [1347, 2070], [1365, 2108], [1326, 2172], [1379, 2199], [1370, 2230], [1333, 2251], [1289, 2237], [1227, 2107], [1190, 2106], [1089, 2010], [1058, 1920], [1010, 1914], [979, 1944], [897, 1900], [760, 1896], [733, 1907], [709, 2003], [683, 2026], [354, 2103], [233, 2153], [109, 2175], [-33, 2130], [-144, 2150], [-143, 2215], [-122, 2289], [-135, 2357], [-173, 2492], [-167, 2579], [-109, 2787], [-272, 2817], [-297, 2793], [-301, 2689], [-367, 2621], [-415, 2612], [-436, 2642], [-453, 2709], [-434, 2908], [-405, 2961], [-341, 2970], [-276, 2950], [-244, 2916], [-308, 2861], [-304, 2831], [-128, 2797], [-97, 2809], [-100, 2860], [-128, 2952], [-147, 3067], [-158, 3182], [-141, 3251], [-140, 3308], [-157, 3376], [-181, 3555], [-203, 3606], [-241, 3637], [-296, 3651], [-398, 3654], [-471, 3678], [-533, 3737], [-669, 3945], [-636, 3987], [-700, 4002], [-760, 3971], [-774, 3908], [-729, 3859], [-772, 3823], [-696, 3789], [-662, 3748], [-648, 3702], [-667, 3694], [-848, 3849], [-885, 3871], [-975, 3888], [-998, 3912], [-999, 3952], [-911, 4181], [-881, 4284], [-860, 4395], [-856, 4504], [-867, 4589], [-575, 4589], [-438, 4503], [-391, 4504], [-295, 4611], [-302, 4657], [-256, 4675], [-208, 4655], [-141, 4673], [-92, 4660], [14, 4584], [40, 4575], [113, 4606], [105, 4669], [145, 4773], [200, 4800], [274, 4793], [401, 4655], [488, 4674], [537, 4748], [590, 4777], [660, 4866], [700, 4950], [757, 4963], [899, 4917], [985, 4853], [1062, 4757], [1090, 4680], [1185, 4548], [1262, 4546], [1298, 4525], [1285, 4477], [1304, 4446], [1388, 4430], [1414, 4391], [1406, 4337]]]]}}, {"type": "Feature", "id": "DK.3568", "properties": {"hc-group": "admin1", "hc-middle-x": 0.62, "hc-middle-y": 0.38, "hc-key": "dk-3568", "hc-a2": "NO", "labelrank": "7", "hasc": "DK.", "alt-name": "North", "woe-id": "28362579", "subregion": null, "fips": null, "postal-code": null, "name": "Nordjylland", "country": "Denmark", "type-en": "Region", "region": null, "longitude": "9.732989999999999", "woe-name": "Nordjylland", "latitude": "56.8261", "woe-label": "Nordjylland, DK, Denmark", "type": "Region"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[251, 7686], [264, 7589], [246, 7501], [216, 7446], [208, 7499], [186, 7520], [151, 7491], [140, 7453], [156, 7409], [168, 7320], [206, 7281], [166, 7213], [144, 7152], [104, 7091], [43, 7041], [35, 6995], [-139, 6948], [-178, 7037], [-230, 7098], [-275, 7118], [-336, 7103], [-324, 7148], [-278, 7183], [-290, 7245], [-249, 7275], [-144, 7289], [-107, 7337], [-177, 7354], [-132, 7445], [-136, 7512], [-55, 7536], [32, 7551], [75, 7575], [107, 7564], [137, 7602], [186, 7705], [220, 7683], [258, 7748], [277, 7741], [251, 7686]]], [[[3566, 8731], [3578, 8698], [3567, 8647], [3500, 8664], [3389, 8633], [3431, 8574], [3428, 8531], [3395, 8491], [3342, 8391], [3297, 8379], [3267, 8402], [3227, 8473], [3134, 8502], [3083, 8553], [3083, 8569], [3131, 8581], [3214, 8664], [3306, 8681], [3331, 8710], [3438, 8713], [3496, 8747], [3536, 8749], [3566, 8731]]], [[[2098, 6939], [2030, 6880], [1982, 6809], [1881, 6744], [1793, 6732], [1699, 6685], [1650, 6626], [1588, 6616], [1523, 6643], [1458, 6624], [1384, 6708], [1336, 6681], [1307, 6690], [1317, 6749], [1355, 6794], [1326, 6822], [1158, 6822], [1116, 6868], [1125, 6932], [1103, 6957], [1060, 6960], [1048, 6933], [1062, 6896], [1026, 6834], [864, 6917], [833, 6986], [811, 7009], [722, 6977], [676, 6935], [644, 6933], [631, 6967], [648, 7000], [716, 7042], [739, 7125], [699, 7156], [653, 7224], [641, 7316], [693, 7405], [684, 7467], [621, 7520], [675, 7640], [750, 7731], [792, 7751], [805, 7797], [1022, 7872], [1070, 7871], [1242, 7800], [1222, 7751], [1254, 7725], [1288, 7741], [1391, 7914], [1496, 7968], [1567, 7950], [1723, 7971], [1635, 8051], [1561, 8091], [1534, 8090], [1455, 7990], [1424, 7974], [1370, 8022], [1342, 8014], [1291, 7966], [1232, 7940], [1105, 7937], [770, 7814], [713, 7824], [549, 7962], [546, 7908], [444, 7852], [402, 7885], [376, 7880], [327, 7832], [303, 7834], [237, 7767], [224, 7803], [195, 7817], [137, 7797], [158, 7773], [105, 7724], [32, 7685], [-21, 7701], [-90, 7678], [-114, 7606], [-176, 7521], [-190, 7415], [-253, 7318], [-303, 7308], [-369, 7254], [-381, 7219], [-370, 7085], [-393, 7043], [-334, 7049], [-309, 7010], [-357, 6970], [-409, 7001], [-446, 7002], [-494, 6971], [-495, 6951], [-424, 6970], [-357, 6933], [-253, 6978], [-231, 6969], [-232, 6875], [-200, 6814], [-237, 6756], [-278, 6744], [-302, 6712], [-379, 6800], [-408, 6872], [-441, 6904], [-546, 6949], [-577, 6983], [-625, 7062], [-640, 7159], [-711, 7210], [-735, 7212], [-715, 7113], [-712, 7018], [-734, 7015], [-745, 7071], [-746, 7195], [-728, 7306], [-694, 7388], [-426, 7818], [-347, 7900], [-224, 8092], [-178, 8136], [-125, 8135], [-3, 8091], [65, 8086], [197, 8117], [256, 8147], [326, 8225], [353, 8237], [718, 8184], [851, 8201], [983, 8251], [1108, 8337], [1217, 8461], [1524, 9046], [1578, 9129], [1676, 9242], [1725, 9339], [1777, 9404], [1847, 9419], [1980, 9412], [2110, 9436], [2227, 9491], [2328, 9569], [2500, 9755], [2594, 9829], [2675, 9851], [2717, 9842], [2752, 9815], [2675, 9783], [2602, 9709], [2488, 9522], [2448, 9360], [2491, 9240], [2566, 9130], [2624, 9000], [2584, 8881], [2588, 8741], [2625, 8466], [2601, 8425], [2528, 8368], [2493, 8324], [2444, 8214], [2422, 8140], [2408, 8024], [2357, 7851], [2318, 7816], [2248, 7816], [2201, 7801], [2076, 7883], [1984, 7980], [1923, 8004], [1873, 8053], [1837, 8064], [1808, 8047], [1767, 7978], [1848, 8041], [1878, 8028], [2025, 7877], [2074, 7854], [2131, 7803], [2163, 7792], [2282, 7792], [2310, 7777], [2269, 7724], [2243, 7573], [2273, 7393], [2293, 7228], [2332, 7114], [2363, 7064], [2331, 7039], [2277, 7058], [2211, 7057], [2162, 7020], [2108, 7066], [2076, 7072], [2006, 7027], [1947, 7005], [1902, 6967], [1848, 6946], [1781, 6895], [1575, 6862], [1575, 6844], [1642, 6833], [1864, 6904], [1939, 6948], [1943, 6973], [1995, 6960], [2044, 6991], [2059, 7045], [2131, 7036], [2120, 7000], [2146, 6996], [2098, 6939]]]]}}, {"type": "Feature", "id": "DK.6325", "properties": {"hc-group": "admin1", "hc-middle-x": 0.13, "hc-middle-y": 0.24, "hc-key": "dk-6325", "hc-a2": "HO", "labelrank": "7", "hasc": "DK.", "alt-name": "Capital Region", "woe-id": "28362583", "subregion": null, "fips": null, "postal-code": null, "name": "Hovedstaden", "country": "Denmark", "type-en": "Region", "region": null, "longitude": "12.3198", "woe-name": "Hovedstaden", "latitude": "55.8486", "woe-label": "Hovedstaden, DK, Denmark", "type": "Region"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[9422, 3437], [9484, 3395], [9555, 3402], [9603, 3331], [9740, 3252], [9814, 3233], [9848, 3203], [9830, 3165], [9851, 3102], [9800, 2960], [9816, 2890], [9760, 2813], [9682, 2827], [9619, 2826], [9531, 2853], [9419, 2910], [9296, 2934], [9163, 3026], [9137, 3057], [9152, 3116], [9142, 3393], [9156, 3451], [9181, 3507], [9208, 3624], [9231, 3612], [9300, 3527], [9361, 3472], [9422, 3437]]], [[[6121, 4223], [6111, 4216], [6058, 4289], [6068, 4362], [6095, 4387], [6123, 4361], [6132, 4271], [6121, 4223]]], [[[5832, 4057], [5799, 4046], [5764, 4095], [5738, 4168], [5735, 4228], [5750, 4263], [5802, 4325], [5839, 4428], [5857, 4455], [5970, 4203], [5922, 4127], [5872, 4100], [5832, 4057]]], [[[4710, 4485], [4688, 4549], [4702, 4619], [4724, 4681], [4746, 4706], [4825, 4740], [4847, 4817], [4773, 4981], [4782, 5032], [4809, 5031], [4865, 4977], [4908, 4953], [4902, 4874], [4968, 4723], [5000, 4613], [5009, 4510], [4972, 4451], [4917, 4468], [4891, 4464], [4792, 4511], [4769, 4491], [4710, 4485]]], [[[5041, 4625], [4999, 4783], [4988, 4916], [4946, 5058], [4921, 5094], [4860, 5104], [4747, 5052], [4704, 5042], [4677, 5065], [4700, 5132], [4775, 5202], [4915, 5297], [5136, 5504], [5252, 5576], [5378, 5577], [5511, 5509], [5618, 5514], [5703, 5455], [5748, 5438], [5830, 5376], [5812, 5333], [5754, 5245], [5735, 5199], [5682, 5038], [5751, 4939], [5791, 4796], [5835, 4713], [5822, 4528], [5847, 4474], [5817, 4431], [5762, 4309], [5707, 4266], [5686, 4200], [5653, 4187], [5588, 4220], [5558, 4219], [5492, 4176], [5426, 4205], [5327, 4200], [5308, 4213], [5250, 4191], [5202, 4210], [5188, 4268], [5193, 4312], [5178, 4339], [5229, 4384], [5320, 4446], [5274, 4514], [5294, 4535], [5183, 4550], [5096, 4627], [5041, 4625]]]]}}, {"type": "Feature", "id": "DK.3563", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.38, "hc-key": "dk-3563", "hc-a2": "SJ", "labelrank": "7", "hasc": "DK.", "alt-name": "Sjaelland|Zealand", "woe-id": "28362582", "subregion": null, "fips": null, "postal-code": null, "name": "Sjaælland", "country": "Denmark", "type-en": "Region", "region": null, "longitude": "11.7483", "woe-name": "Sjaælland", "latitude": "55.4417", "woe-label": "Sjaelland, DK, Denmark", "type": "Region"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[3871, 2362], [4002, 2271], [4052, 2170], [4127, 2141], [4409, 1995], [4371, 2092], [4404, 2123], [4446, 2124], [4498, 2146], [4478, 2208], [4483, 2273], [4605, 2169], [4616, 2131], [4655, 2085], [4705, 2061], [4684, 2042], [4708, 1995], [4789, 1933], [4807, 1908], [4821, 1829], [4749, 1730], [4812, 1705], [4808, 1647], [4761, 1588], [4692, 1563], [4448, 1607], [4370, 1585], [4296, 1548], [4239, 1491], [4204, 1496], [4056, 1577], [3958, 1657], [3784, 1761], [3706, 1776], [3622, 1812], [3537, 1830], [3503, 1859], [3486, 1899], [3495, 1944], [3533, 1968], [3625, 1967], [3652, 2024], [3605, 2048], [3509, 2167], [3508, 2213], [3527, 2256], [3590, 2336], [3612, 2326], [3832, 2379], [3871, 2362]]], [[[4335, 2405], [4309, 2358], [4242, 2441], [4320, 2459], [4345, 2428], [4335, 2405]]], [[[5846, 2572], [5875, 2481], [5860, 2435], [5799, 2424], [5614, 2455], [5559, 2444], [5507, 2417], [5456, 2371], [5379, 2254], [5351, 2236], [5305, 2241], [5204, 2286], [5226, 2304], [5223, 2352], [5253, 2378], [5221, 2413], [5273, 2465], [5303, 2474], [5290, 2510], [5377, 2477], [5460, 2518], [5449, 2566], [5467, 2610], [5415, 2644], [5391, 2689], [5412, 2718], [5440, 2719], [5476, 2661], [5529, 2640], [5656, 2638], [5792, 2616], [5846, 2572]]], [[[4891, 4464], [4872, 4387], [4821, 4333], [4803, 4295], [4834, 4289], [4877, 4311], [4910, 4350], [4913, 4393], [4936, 4404], [4985, 4375], [4999, 4342], [4951, 4356], [4956, 4325], [5032, 4288], [5042, 4345], [5073, 4432], [5075, 4492], [5041, 4625], [5096, 4627], [5183, 4550], [5294, 4535], [5274, 4514], [5320, 4446], [5229, 4384], [5178, 4339], [5193, 4312], [5188, 4268], [5202, 4210], [5250, 4191], [5308, 4213], [5327, 4200], [5426, 4205], [5492, 4176], [5451, 4150], [5331, 4011], [5261, 3850], [5309, 3711], [5386, 3648], [5507, 3638], [5602, 3575], [5656, 3496], [5670, 3411], [5695, 3340], [5666, 3302], [5588, 3241], [5270, 3150], [5209, 3095], [5180, 3049], [5164, 2993], [5175, 2948], [5152, 2914], [5123, 2927], [5132, 2985], [5113, 3021], [5081, 3030], [5019, 2978], [5017, 2950], [5071, 2907], [5201, 2901], [5260, 2884], [5276, 2822], [5259, 2784], [5205, 2748], [5201, 2727], [5265, 2601], [5266, 2550], [5246, 2521], [5156, 2495], [5105, 2452], [5070, 2444], [5015, 2464], [4928, 2540], [4876, 2547], [4857, 2508], [4866, 2457], [4843, 2399], [4854, 2331], [4899, 2312], [4917, 2328], [4904, 2363], [4956, 2385], [4997, 2312], [5091, 2242], [5177, 2260], [5297, 2121], [5298, 2104], [5166, 1986], [5144, 1959], [5089, 1840], [4990, 1703], [4993, 1617], [5017, 1466], [5017, 1369], [4986, 1365], [4952, 1405], [4851, 1604], [4851, 1660], [4908, 1773], [4854, 1793], [4862, 1865], [4742, 1991], [4713, 2096], [4642, 2170], [4625, 2310], [4576, 2350], [4575, 2370], [4633, 2419], [4670, 2419], [4718, 2375], [4769, 2426], [4786, 2407], [4837, 2454], [4787, 2572], [4768, 2598], [4662, 2643], [4633, 2648], [4590, 2683], [4492, 2685], [4413, 2751], [4497, 2736], [4662, 2667], [4744, 2652], [4707, 2713], [4609, 2781], [4566, 2847], [4698, 2877], [4702, 2909], [4667, 2936], [4580, 2926], [4552, 2960], [4586, 3017], [4558, 3069], [4523, 3067], [4456, 2991], [4378, 3016], [4212, 3045], [4104, 3089], [4022, 3049], [3916, 3046], [3949, 3010], [3857, 3023], [3832, 3061], [3819, 3134], [3864, 3118], [3891, 3174], [3823, 3151], [3820, 3232], [3695, 3337], [3680, 3361], [3734, 3382], [3736, 3409], [3701, 3428], [3662, 3425], [3635, 3369], [3601, 3380], [3577, 3459], [3640, 3459], [3704, 3515], [3753, 3534], [3765, 3599], [3703, 3795], [3662, 3850], [3607, 3866], [3563, 3840], [3565, 3910], [3639, 3937], [3654, 3976], [3648, 4027], [3626, 4078], [3556, 4161], [3481, 4203], [3314, 4243], [3313, 4263], [3553, 4250], [3527, 4294], [3394, 4355], [3355, 4417], [3329, 4429], [3250, 4432], [3225, 4463], [3465, 4433], [3514, 4443], [3608, 4483], [3660, 4493], [3599, 4457], [3624, 4410], [3665, 4375], [3709, 4363], [3747, 4386], [3682, 4439], [3702, 4460], [3743, 4440], [3797, 4443], [3872, 4492], [3923, 4506], [3964, 4545], [3977, 4661], [3933, 4741], [4010, 4712], [4045, 4728], [4076, 4766], [4128, 4757], [4167, 4831], [4172, 4938], [4118, 5025], [4074, 5042], [3977, 5046], [3855, 5087], [3820, 5116], [3805, 5163], [4001, 5076], [4040, 5087], [4274, 5059], [4302, 5034], [4365, 5040], [4470, 5077], [4554, 5120], [4567, 5070], [4515, 4985], [4534, 4957], [4500, 4934], [4475, 4946], [4483, 4984], [4435, 4979], [4402, 4944], [4482, 4815], [4513, 4742], [4494, 4688], [4424, 4694], [4402, 4685], [4358, 4628], [4330, 4627], [4356, 4600], [4431, 4611], [4500, 4655], [4528, 4653], [4556, 4588], [4569, 4526], [4448, 4499], [4411, 4469], [4431, 4449], [4521, 4454], [4546, 4475], [4612, 4397], [4624, 4362], [4605, 4287], [4649, 4340], [4710, 4485], [4769, 4491], [4792, 4511], [4891, 4464]]]]}}]}