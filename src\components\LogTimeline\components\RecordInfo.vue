<script>
import { SchemaNameMap, $m_a, MissionLabelMap } from '@/assets/libs/enum';
import querystring from 'querystring';

export default {
  name: 'RecordInfo',
  props: {
    msg: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showMore: false,
      accountRender: null,
      personRender: null,
      proxyRender: null,
      urlRender: null,
      machineId: null,
      paramRender: null
    };
  },
  computed: {
    label() {
      return SchemaNameMap[$m_a(this.msg.taskExecutionType)];
    },
    status() {
      return MissionLabelMap[this.msg.status];
    }
  },
  methods: {

    /**
     * 生成【复制代理】按钮
     */
    generateProxyBtn(proxyInfo) {
      const handleCopy = async() => {
        await this.$copyText(proxyInfo);
        this.$message.success(this.$t('components["【代理信息】复制成功"]'));
      };
      if (proxyInfo) {
        return (
          <titan-link type='primary' onClick={handleCopy}>
            {this.$t('components["复制代理"]')}
          </titan-link>
        );
      }
    },

    /**
     * 生成【新标签页】链接
     */
    generateLinkBtn(href, label) {
      const handleView = () => {
        window.open(href);
      };
      if (href) {
        return (
          <titan-link type='primary' onClick={handleView}>
            {label}
          </titan-link>
        );
      }
    },

    /**
     * 生成【展开/收起】按钮
     */
    generateToggleBtn() {
      const handleToggle = () => {
        // 当前点击项
        const item = this.$parent.$el;
        // 滚动盒子元素
        const wrapper = item.closest(this.element);
        const extraHeight = wrapper.offsetTop;
        wrapper.scrollTop = item.offsetTop - extraHeight;
        this.showMore = !this.showMore;
      };
      return (
        <titan-link
          type='primary'
          style='verticalAlign: top;'
          onClick={handleToggle}
        >
          {this.showMore ? this.$t('components["收起"]') : this.$t('components["点击查看全部"]')}
        </titan-link>
      );
    },

    getAccountInfo(item) {
      const handleView = () => {
        const url =
          '/#/account-center/virtual/sys-account?' +
          querystring.stringify({
            name: item.account,
            platform: item.platform
          });
        window.open(url);
      };
      const ele = (
        <titan-link type='primary' onClick={handleView} style='margin-top: -3px'>
          {item.account}
        </titan-link>
      );
      const demo = [];
      if (item.account) {
        demo.push(
          <span>{this.$t('components["账号"]')}：{ele}</span>
        );
      }

      if (item.account_url) {
        const url = (
          <titan-link type='primary' onClick={() => window.open(item.account_url)} style='margin-top: -3px'>
            {this.$t('components["查看"]')}
          </titan-link>
        );
        demo.push(
          <span>{this.$t('components["个人主页"]')}：{url}</span>
        );
      }
      return (
        <div class='tip-box'>{demo}</div>
      );
    },

    getPersonInfo() {
    },

    getProxy(item) {
      let link = '';
      const url = item?.share_url;
      if (url) {
        const handleCopy = async() => {
          await this.$copyText(url);
          this.$message.success(this.$t('components["【代理信息】复制成功"]'));
        };
        link = (
          <titan-link type='primary' onClick={handleCopy}>
            {this.$t('components["复制代理url"]')}
          </titan-link>
        );
      }
      const copyDetail = async() => {
        await this.$copyText(JSON.stringify(item));
        this.$message.success(this.$t('components["【代理信息】复制成功"]'));
      };
      return (
        <div class='proxy'>
          <label>{this.$t('components["代理相关"]')}</label>
          <titan-link type='primary' onClick={copyDetail}>
            {this.$t('components["复制代理详情"]')}
          </titan-link>
          {link}
        </div>
      );
    },
    getUrlInfo(url, label) {
      return (
        <div class='url'>
          <label>{label}</label>
          <titan-link type='primary' href={url} target='_blank'>{url}</titan-link>
        </div>
      );
    },
    getParamInfo(item) {
      if (item?.nurture_exe_proxy_info) return this.getProxy(item.nurture_exe_proxy_info);
      return '';
    },
    formatMissionLogItem(h) {
      const missionLog = this.msg;
      const line1 = this.label && this.status ? `${this.label}${this.$t('components["任务"]')}【${this.status}】` : '';
      const line2 = missionLog.message ? `${missionLog.message}` : '';

      return (
        <div class='record-info'>
          <p>{line1}</p>
          <p>{line2}</p>
          <div>
            {missionLog?.info?.account_info ? this.getAccountInfo(missionLog.info.account_info) : ''}
            {missionLog?.info?.account ? this.getAccountInfo(missionLog.info.account) : ''}
            {missionLog?.info?.param ? this.getParamInfo(missionLog.info.param) : ''}
            {missionLog?.info?.proxy ? this.getProxy(missionLog.info.proxy) : ''}
            {missionLog?.info?.url ? this.getUrlInfo(missionLog.info.url, this.$t('components["执行结果"]')) : ''}
          </div>
        </div>
      );
    }
  },
  render: function(h) {
    return this.formatMissionLogItem(h);
  }
};
</script>

<style lang="scss" scoped>
.record-info {
  p {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .link {
      overflow: hidden;
      display: inline-block;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .action {
    color: #3569e7;
  }
  .tip-box {
    span:first-child {
      margin-right: 10px;
    }
  }
  .proxy{
    label{
      vertical-align: middle;
      margin-right: 10px;
    }
    .titan-link:last-child{
      margin-left: 20px;
    }
  }
  .url{
    label{
      font-weight: 500;
      vertical-align: middle;
      margin-right: 10px;
      color: #FFFFFF;
      background: #53cbbf;
      padding: 0 2px;
      border-radius: 4px;
    }
  }
}
</style>
