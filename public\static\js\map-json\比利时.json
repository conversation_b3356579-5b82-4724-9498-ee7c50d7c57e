{"title": "Belgium", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:32631"}}, "hc-transform": {"default": {"crs": "+proj=utm +zone=31 +datum=WGS84 +units=m +no_defs", "scale": 0.0025573356301, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 466508.156242, "yoffset": 5706486.37858}}, "features": [{"type": "Feature", "id": "BE.3530", "properties": {"hc-group": "admin1", "hc-middle-x": 0.48, "hc-middle-y": 0.55, "hc-key": "be-3530", "hc-a2": "BR", "labelrank": "9", "hasc": "BE.BU", "alt-name": "Bruselas|Brussel Hoofstadt|Brusselse Hoofdstedelijke Gewest|Brüssel|Bruxelas|Région de Bruxelles-Capitale", "woe-id": "55965974", "subregion": null, "fips": "BE11", "postal-code": null, "name": "Brussels", "country": "Belgium", "type-en": "Capital Region", "region": "Capital Region", "longitude": "4.36266", "woe-name": "Brussels", "latitude": "50.8332", "woe-label": "Capital Region of Brussels, BE, Belgium", "type": "Hoofdstedelijk Gewest|Région Capitale"}, "geometry": {"type": "Polygon", "coordinates": [[[4287, 7230], [4333, 7156], [4332, 7114], [4289, 7068], [4403, 6998], [4450, 6860], [4391, 6851], [4368, 6811], [4462, 6745], [4238, 6618], [4179, 6603], [4117, 6613], [4039, 6664], [3962, 6819], [3912, 6792], [3806, 6848], [3838, 6916], [3909, 6929], [3925, 7003], [3896, 7053], [3947, 7158], [4048, 7212], [4168, 7194], [4239, 7267], [4287, 7230]]]}}, {"type": "Feature", "id": "BE.3527", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.43, "hc-key": "be-3527", "hc-a2": "LU", "labelrank": "7", "hasc": "BE.", "alt-name": "Luxemburg", "woe-id": "7153302", "subregion": null, "fips": null, "postal-code": null, "name": "Luxembourg", "country": "Belgium", "type-en": "Province", "region": "Walloon", "longitude": "5.50117", "woe-name": "Luxembourg", "latitude": "49.9637", "woe-label": "Luxemburg, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[8889, 4036], [8863, 4047], [8815, 4139], [8772, 4103], [8712, 4094], [8690, 4052], [8661, 3927], [8620, 3882], [8517, 3826], [8472, 3781], [8454, 3651], [8381, 3559], [8388, 3467], [8363, 3409], [8289, 3358], [8288, 3297], [8322, 3249], [8293, 3187], [8188, 3108], [8162, 3034], [8069, 2859], [8060, 2817], [8158, 2756], [8186, 2717], [8111, 2722], [8100, 2657], [8128, 2632], [8084, 2566], [8109, 2458], [8141, 2409], [8201, 2392], [8258, 2344], [8330, 2214], [8343, 2121], [8436, 2085], [8473, 2092], [8495, 2033], [8457, 1914], [8488, 1868], [8567, 1847], [8585, 1785], [8522, 1691], [8468, 1543], [8437, 1511], [8486, 1486], [8462, 1417], [8399, 1344], [8332, 1309], [8234, 1352], [8172, 1338], [8102, 1271], [7912, 1320], [7842, 1281], [7795, 1183], [7728, 1182], [7637, 1221], [7584, 1195], [7440, 1090], [7378, 1104], [7359, 1143], [7367, 1270], [7354, 1310], [7208, 1555], [7153, 1601], [7013, 1652], [6977, 1593], [6908, 1594], [6886, 1651], [6893, 1713], [6928, 1745], [6906, 1811], [6787, 1934], [6726, 1931], [6598, 1891], [6531, 1908], [6434, 1986], [6308, 2158], [6273, 2190], [6170, 2217], [6112, 2247], [6003, 2350], [5944, 2376], [5963, 2483], [6000, 2494], [5973, 2578], [5985, 2617], [6194, 2776], [6145, 2822], [6216, 2893], [6357, 2954], [6294, 3013], [6301, 3112], [6217, 3152], [6190, 3211], [6069, 3184], [6039, 3198], [5998, 3341], [5927, 3406], [5981, 3406], [5999, 3475], [6053, 3459], [6143, 3558], [6171, 3633], [6146, 3658], [6183, 3828], [6203, 3705], [6264, 3686], [6529, 3712], [6589, 3727], [6664, 3783], [6740, 3778], [6753, 3825], [6679, 3890], [6759, 3919], [6783, 3999], [6679, 4113], [6677, 4202], [6566, 4259], [6618, 4297], [6699, 4294], [6690, 4346], [6783, 4395], [6814, 4357], [7010, 4492], [7085, 4576], [7085, 4642], [7054, 4712], [6989, 4705], [6955, 4653], [6933, 4676], [7083, 4795], [7041, 4846], [7097, 4927], [7071, 4983], [7162, 5074], [7102, 5173], [7156, 5207], [7281, 5116], [7391, 5194], [7420, 5108], [7468, 5100], [7461, 5060], [7524, 5041], [7618, 5090], [7776, 4953], [7869, 4962], [7857, 4909], [8000, 4914], [7955, 4802], [7960, 4754], [8008, 4678], [8085, 4662], [8015, 4497], [8184, 4530], [8231, 4517], [8251, 4475], [8268, 4519], [8446, 4551], [8462, 4595], [8364, 4605], [8421, 4761], [8367, 4804], [8388, 4885], [8733, 4824], [8740, 4786], [8832, 4744], [8833, 4333], [8847, 4285], [8910, 4275], [8872, 4229], [8889, 4171], [8889, 4036]]]}}, {"type": "Feature", "id": "BE.3532", "properties": {"hc-group": "admin1", "hc-middle-x": 0.68, "hc-middle-y": 0.38, "hc-key": "be-3532", "hc-a2": "FB", "labelrank": "7", "hasc": "BE.VB", "alt-name": "Vlaams Brabant|Flamish-Brabant|Brabant flamand", "woe-id": "22525998", "subregion": null, "fips": null, "postal-code": null, "name": "Flemish Brabant", "country": "Belgium", "type-en": "Province", "region": "Flemish", "longitude": "4.53709", "woe-name": "Flemish Brabant", "latitude": "50.8709", "woe-label": "<PERSON><PERSON><PERSON>, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[2898, 6426], [2988, 6450], [2991, 6474], [2932, 6553], [2967, 6632], [3001, 6652], [3074, 6596], [3121, 6656], [3161, 6620], [3208, 6640], [3291, 6712], [3330, 6837], [3304, 6901], [3232, 6913], [3360, 7105], [3362, 7151], [3321, 7186], [3394, 7242], [3368, 7327], [3497, 7275], [3560, 7321], [3561, 7378], [3519, 7500], [3560, 7589], [3651, 7559], [3714, 7589], [3776, 7802], [3870, 7828], [3938, 7818], [4076, 7758], [4076, 7718], [4162, 7778], [4136, 7711], [4189, 7696], [4204, 7630], [4372, 7675], [4435, 7613], [4484, 7690], [4579, 7620], [4629, 7668], [4718, 7618], [4761, 7618], [4796, 7709], [4997, 7768], [5073, 7729], [5043, 7658], [5148, 7782], [5210, 7771], [5299, 7842], [5349, 7739], [5385, 7716], [5529, 7780], [5563, 7764], [5636, 7832], [5742, 7878], [5794, 7883], [5834, 7838], [5904, 7859], [5951, 7773], [6006, 7796], [6065, 7762], [6113, 7795], [6110, 7905], [6188, 7918], [6250, 7891], [6328, 7796], [6259, 7766], [6216, 7793], [6237, 7689], [6122, 7633], [6123, 7535], [6014, 7433], [6053, 7338], [6117, 7345], [6165, 7295], [6271, 7349], [6282, 7315], [6380, 7316], [6421, 7282], [6401, 7160], [6333, 7162], [6290, 7031], [6275, 6882], [6337, 6862], [6214, 6728], [6240, 6647], [6205, 6557], [6260, 6526], [6209, 6409], [6209, 6409], [6135, 6395], [6092, 6414], [6044, 6472], [6044, 6550], [5974, 6589], [5974, 6589], [5867, 6670], [5732, 6557], [5690, 6550], [5654, 6578], [5662, 6658], [5451, 6631], [5462, 6679], [5395, 6684], [5333, 6782], [5215, 6819], [5141, 6791], [5118, 6734], [5106, 6767], [5058, 6738], [4911, 6772], [4902, 6659], [4955, 6592], [4902, 6527], [4808, 6520], [4792, 6616], [4727, 6543], [4605, 6511], [4600, 6457], [4510, 6506], [4507, 6579], [4405, 6563], [4305, 6482], [4171, 6459], [4156, 6392], [4103, 6395], [4049, 6467], [4029, 6444], [4059, 6400], [3998, 6398], [3992, 6331], [3932, 6293], [3854, 6317], [3800, 6277], [3708, 6350], [3632, 6347], [3570, 6431], [3515, 6430], [3496, 6382], [3428, 6382], [3407, 6337], [3407, 6337], [3331, 6361], [3292, 6286], [3272, 6316], [3207, 6281], [3153, 6297], [3097, 6251], [2928, 6254], [2868, 6275], [2813, 6375], [2832, 6448], [2832, 6448], [2898, 6426]], [[4287, 7230], [4239, 7267], [4168, 7194], [4048, 7212], [3947, 7158], [3896, 7053], [3925, 7003], [3909, 6929], [3838, 6916], [3806, 6848], [3912, 6792], [3962, 6819], [4039, 6664], [4117, 6613], [4179, 6603], [4238, 6618], [4462, 6745], [4368, 6811], [4391, 6851], [4450, 6860], [4403, 6998], [4289, 7068], [4332, 7114], [4333, 7156], [4287, 7230]]]}}, {"type": "Feature", "id": "BE.3533", "properties": {"hc-group": "admin1", "hc-middle-x": 0.44, "hc-middle-y": 0.55, "hc-key": "be-3533", "hc-a2": "EF", "labelrank": "7", "hasc": "BE.OV", "alt-name": "Oost-Vlaanderen|Ostflandern|Falndre orientale", "woe-id": "7153304", "subregion": null, "fips": null, "postal-code": null, "name": "East Flanders", "country": "Belgium", "type-en": "Province", "region": "Flemish", "longitude": "3.83186", "woe-name": "East Flanders", "latitude": "50.9749", "woe-label": "Oost<PERSON>, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[2898, 6426], [2832, 6448], [2832, 6448], [2802, 6524], [2644, 6487], [2607, 6524], [2495, 6510], [2446, 6648], [2326, 6595], [2296, 6654], [2216, 6608], [2104, 6387], [2026, 6440], [1841, 6442], [1821, 6581], [1710, 6547], [1608, 6589], [1781, 6719], [1788, 6751], [1723, 6839], [1759, 6885], [1585, 7033], [1650, 7101], [1628, 7183], [1584, 7201], [1520, 7161], [1497, 7221], [1534, 7258], [1512, 7309], [1578, 7300], [1597, 7320], [1554, 7362], [1610, 7361], [1476, 7427], [1562, 7467], [1514, 7531], [1586, 7640], [1502, 7782], [1562, 7826], [1246, 8048], [1367, 8128], [1466, 8318], [1401, 8433], [1325, 8458], [1392, 8505], [1344, 8620], [1432, 8687], [1496, 8657], [1585, 8648], [1715, 8669], [1720, 8817], [1751, 8849], [1945, 8888], [2084, 8881], [2423, 8772], [2476, 8738], [2492, 8586], [2568, 8522], [2896, 8530], [2957, 8551], [3082, 8652], [3423, 8829], [3558, 8939], [3664, 9090], [3699, 9255], [3754, 9206], [3798, 9082], [3930, 8942], [3945, 8888], [3920, 8825], [3893, 8773], [3955, 8585], [3931, 8569], [4000, 8473], [4023, 8387], [4012, 8290], [3978, 8220], [3869, 8182], [3701, 8169], [3652, 8133], [3609, 8072], [3652, 7872], [3633, 7825], [3698, 7783], [3752, 7829], [3776, 7802], [3714, 7589], [3651, 7559], [3560, 7589], [3519, 7500], [3561, 7378], [3560, 7321], [3497, 7275], [3368, 7327], [3394, 7242], [3321, 7186], [3362, 7151], [3360, 7105], [3232, 6913], [3304, 6901], [3330, 6837], [3291, 6712], [3208, 6640], [3161, 6620], [3121, 6656], [3074, 6596], [3001, 6652], [2967, 6632], [2932, 6553], [2991, 6474], [2988, 6450], [2898, 6426]]]}}, {"type": "Feature", "id": "BE.3534", "properties": {"hc-group": "admin1", "hc-middle-x": 0.5, "hc-middle-y": 0.51, "hc-key": "be-3534", "hc-a2": "WF", "labelrank": "7", "hasc": "BE.", "alt-name": "West-Vlaanderen", "woe-id": "7153305", "subregion": null, "fips": null, "postal-code": null, "name": "West Flanders", "country": "Belgium", "type-en": "Province", "region": "Flemish", "longitude": "3.02202", "woe-name": "West Flanders", "latitude": "51.0312", "woe-label": "West-Vlaanderen, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[784, 6503], [737, 6587], [690, 6632], [617, 6653], [395, 6584], [369, 6746], [325, 6775], [163, 6709], [217, 6621], [112, 6578], [100, 6537], [20, 6522], [-20, 6558], [-113, 6507], [-45, 6283], [-268, 6387], [-320, 6430], [-387, 6576], [-491, 6676], [-532, 6764], [-569, 6784], [-647, 6780], [-731, 6799], [-770, 6880], [-816, 6912], [-796, 7025], [-850, 7218], [-834, 7259], [-753, 7349], [-761, 7437], [-805, 7503], [-904, 7614], [-931, 7699], [-957, 7894], [-999, 7995], [-943, 8036], [-837, 8070], [-459, 8354], [117, 8692], [673, 9058], [1293, 9261], [1317, 9092], [1305, 8880], [1345, 8769], [1432, 8687], [1344, 8620], [1392, 8505], [1325, 8458], [1401, 8433], [1466, 8318], [1367, 8128], [1246, 8048], [1562, 7826], [1502, 7782], [1586, 7640], [1514, 7531], [1562, 7467], [1476, 7427], [1610, 7361], [1554, 7362], [1597, 7320], [1578, 7300], [1512, 7309], [1534, 7258], [1497, 7221], [1520, 7161], [1584, 7201], [1628, 7183], [1650, 7101], [1585, 7033], [1759, 6885], [1723, 6839], [1788, 6751], [1781, 6719], [1608, 6589], [1483, 6495], [1357, 6361], [1346, 6328], [1302, 6338], [1233, 6388], [1236, 6448], [1177, 6533], [1058, 6515], [943, 6580], [784, 6503]]]}}, {"type": "Feature", "id": "BE.3535", "properties": {"hc-group": "admin1", "hc-middle-x": 0.48, "hc-middle-y": 0.5, "hc-key": "be-3535", "hc-a2": "AN", "labelrank": "7", "hasc": "BE.", "alt-name": "Antwerpen", "woe-id": "7153308", "subregion": null, "fips": null, "postal-code": null, "name": "Antwerp", "country": "Belgium", "type-en": "Province", "region": "Flemish", "longitude": "4.72122", "woe-name": "Antwerp", "latitude": "51.2485", "woe-label": "Antwerp, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[3776, 7802], [3752, 7829], [3698, 7783], [3633, 7825], [3652, 7872], [3609, 8072], [3652, 8133], [3701, 8169], [3869, 8182], [3978, 8220], [4012, 8290], [4023, 8387], [4000, 8473], [3931, 8569], [3955, 8585], [3893, 8773], [3920, 8825], [3968, 8858], [4006, 8944], [3913, 8982], [3879, 9038], [3870, 9179], [3808, 9263], [3888, 9262], [4043, 9192], [4172, 9186], [4224, 9215], [4236, 9291], [4218, 9334], [4133, 9446], [4156, 9557], [4122, 9593], [4265, 9680], [4411, 9737], [4527, 9747], [4550, 9710], [4548, 9637], [4521, 9538], [4575, 9501], [4691, 9515], [4822, 9498], [4886, 9535], [5093, 9802], [5183, 9851], [5273, 9830], [5349, 9764], [5357, 9698], [5331, 9567], [5353, 9491], [5231, 9544], [5200, 9539], [5186, 9486], [5245, 9462], [5438, 9460], [5597, 9401], [5656, 9419], [5724, 9489], [5826, 9649], [5868, 9772], [5910, 9784], [6002, 9721], [6059, 9620], [6051, 9546], [6022, 9475], [6015, 9379], [6034, 9336], [6132, 9234], [6193, 9071], [6242, 9046], [6399, 9055], [6450, 8995], [6456, 8838], [6523, 8832], [6490, 8803], [6465, 8634], [6581, 8532], [6595, 8354], [6528, 8290], [6437, 8314], [6377, 8220], [6238, 8209], [6130, 8063], [5909, 8012], [5911, 7952], [5834, 7838], [5794, 7883], [5742, 7878], [5636, 7832], [5563, 7764], [5529, 7780], [5385, 7716], [5349, 7739], [5299, 7842], [5210, 7771], [5148, 7782], [5043, 7658], [5073, 7729], [4997, 7768], [4796, 7709], [4761, 7618], [4718, 7618], [4629, 7668], [4579, 7620], [4484, 7690], [4435, 7613], [4372, 7675], [4204, 7630], [4189, 7696], [4136, 7711], [4162, 7778], [4076, 7718], [4076, 7758], [3938, 7818], [3870, 7828], [3776, 7802]]]}}, {"type": "Feature", "id": "BE.3528", "properties": {"hc-group": "admin1", "hc-middle-x": 0.59, "hc-middle-y": 0.46, "hc-key": "be-3528", "hc-a2": "LI", "labelrank": "7", "hasc": "BE.", "alt-name": "<PERSON><PERSON>", "woe-id": "7153300", "subregion": null, "fips": null, "postal-code": null, "name": "Liege", "country": "Belgium", "type-en": "Province", "region": "Walloon", "longitude": "5.67651", "woe-name": "Liege", "latitude": "50.5794", "woe-label": "Liege, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[8413, 6667], [8444, 6658], [8647, 6660], [8703, 6672], [8702, 6640], [8749, 6617], [8757, 6492], [8794, 6489], [8903, 6524], [8950, 6522], [9006, 6470], [9188, 6217], [9151, 6191], [9187, 6129], [9246, 6102], [9439, 6105], [9447, 6079], [9399, 5983], [9253, 5866], [9205, 5782], [9254, 5725], [9238, 5671], [9294, 5661], [9273, 5622], [9323, 5553], [9374, 5535], [9481, 5578], [9538, 5552], [9712, 5528], [9677, 5461], [9704, 5377], [9762, 5321], [9763, 5247], [9731, 5083], [9737, 5031], [9842, 4865], [9851, 4804], [9817, 4765], [9743, 4748], [9641, 4765], [9603, 4741], [9560, 4663], [9570, 4579], [9521, 4530], [9343, 4460], [9317, 4416], [9228, 4329], [9267, 4288], [9285, 4189], [9250, 4132], [9170, 4109], [9152, 4023], [9193, 3974], [9162, 3913], [9119, 3930], [9098, 4046], [9060, 4080], [8997, 4040], [8889, 4036], [8889, 4171], [8872, 4229], [8910, 4275], [8847, 4285], [8833, 4333], [8832, 4744], [8740, 4786], [8733, 4824], [8388, 4885], [8367, 4804], [8421, 4761], [8364, 4605], [8462, 4595], [8446, 4551], [8268, 4519], [8251, 4475], [8231, 4517], [8184, 4530], [8015, 4497], [8085, 4662], [8008, 4678], [7960, 4754], [7955, 4802], [8000, 4914], [7857, 4909], [7869, 4962], [7776, 4953], [7618, 5090], [7524, 5041], [7461, 5060], [7468, 5100], [7420, 5108], [7391, 5194], [7281, 5116], [7156, 5207], [7102, 5173], [7162, 5074], [7071, 4983], [6950, 5020], [6799, 4911], [6758, 4964], [6759, 5046], [6618, 5079], [6581, 5135], [6623, 5223], [6567, 5339], [6504, 5398], [6410, 5357], [6378, 5409], [6391, 5448], [6335, 5438], [6314, 5507], [6246, 5517], [6201, 5620], [6044, 5624], [6096, 5666], [6018, 5875], [5878, 6001], [5885, 6107], [5936, 6209], [5930, 6340], [5973, 6373], [5902, 6469], [5967, 6507], [5974, 6589], [6044, 6550], [6044, 6472], [6092, 6414], [6135, 6395], [6209, 6409], [6209, 6409], [6397, 6359], [6431, 6404], [6398, 6475], [6565, 6459], [6584, 6502], [6606, 6452], [6694, 6510], [6785, 6464], [6797, 6515], [6851, 6566], [6978, 6614], [7017, 6598], [7054, 6513], [7130, 6486], [7192, 6495], [7212, 6573], [7269, 6515], [7313, 6528], [7387, 6596], [7379, 6668], [7618, 6748], [7750, 6891], [7872, 6842], [7844, 6687], [7896, 6660], [7968, 6683], [8019, 6632], [8090, 6654], [8215, 6502], [8314, 6520], [8383, 6488], [8407, 6499], [8468, 6601], [8413, 6667]]]}}, {"type": "Feature", "id": "BE.3529", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.51, "hc-key": "be-3529", "hc-a2": "WB", "labelrank": "7", "hasc": "BE.", "alt-name": "Waals-Brabant|Brabant Wallon", "woe-id": "22525997", "subregion": null, "fips": null, "postal-code": null, "name": "<PERSON><PERSON>", "country": "Belgium", "type-en": "Province", "region": "Walloon", "longitude": "4.55466", "woe-name": "<PERSON><PERSON>", "latitude": "50.6416", "woe-label": "<PERSON><PERSON>, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[5967, 6507], [5902, 6469], [5973, 6373], [5930, 6340], [5936, 6209], [5885, 6107], [5795, 6125], [5734, 6037], [5659, 6042], [5657, 6015], [5358, 5928], [5356, 5863], [5324, 5909], [5212, 5862], [5192, 5950], [5120, 5928], [5108, 5879], [5029, 5933], [5001, 5932], [4972, 5879], [5007, 5742], [4945, 5727], [4842, 5761], [4792, 5753], [4745, 5714], [4757, 5640], [4589, 5564], [4548, 5582], [4550, 5623], [4459, 5573], [4464, 5682], [4450, 5704], [4324, 5701], [4294, 5731], [4135, 5711], [4066, 5631], [4072, 5743], [3989, 5734], [3917, 5842], [3825, 5859], [3839, 5952], [3689, 5987], [3702, 6080], [3646, 6150], [3616, 6137], [3529, 6015], [3465, 6004], [3415, 6024], [3387, 6101], [3381, 6264], [3407, 6337], [3407, 6337], [3428, 6382], [3496, 6382], [3515, 6430], [3570, 6431], [3632, 6347], [3708, 6350], [3800, 6277], [3854, 6317], [3932, 6293], [3992, 6331], [3998, 6398], [4059, 6400], [4029, 6444], [4049, 6467], [4103, 6395], [4156, 6392], [4171, 6459], [4305, 6482], [4405, 6563], [4507, 6579], [4510, 6506], [4600, 6457], [4605, 6511], [4727, 6543], [4792, 6616], [4808, 6520], [4902, 6527], [4955, 6592], [4902, 6659], [4911, 6772], [5058, 6738], [5106, 6767], [5118, 6734], [5141, 6791], [5215, 6819], [5333, 6782], [5395, 6684], [5462, 6679], [5451, 6631], [5662, 6658], [5654, 6578], [5690, 6550], [5732, 6557], [5867, 6670], [5974, 6589], [5974, 6589], [5967, 6507]]]}}, {"type": "Feature", "id": "BE.489", "properties": {"hc-group": "admin1", "hc-middle-x": 0.57, "hc-middle-y": 0.31, "hc-key": "be-489", "hc-a2": "HA", "labelrank": "7", "hasc": "BE.", "alt-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "woe-id": "7153299", "subregion": null, "fips": null, "postal-code": null, "name": "Hainaut", "country": "Belgium", "type-en": "Province", "region": "Walloon", "longitude": "3.88925", "woe-name": "Hainaut", "latitude": "50.521", "woe-label": "Hainault, BE, Belgium", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[3381, 6264], [3387, 6101], [3415, 6024], [3465, 6004], [3529, 6015], [3616, 6137], [3646, 6150], [3702, 6080], [3689, 5987], [3839, 5952], [3825, 5859], [3917, 5842], [3989, 5734], [4072, 5743], [4066, 5631], [4135, 5711], [4294, 5731], [4324, 5701], [4450, 5704], [4464, 5682], [4459, 5573], [4550, 5623], [4548, 5582], [4589, 5564], [4757, 5640], [4711, 5421], [4812, 5449], [4791, 5407], [4826, 5361], [4781, 5219], [4755, 5191], [4878, 5121], [4839, 5028], [4794, 5013], [4854, 4946], [4788, 4832], [4809, 4667], [4619, 4631], [4566, 4697], [4488, 4688], [4432, 4587], [4275, 4606], [4254, 4523], [4203, 4525], [4168, 4458], [4073, 4450], [3962, 4366], [4010, 4303], [4114, 4244], [4187, 4307], [4288, 4265], [4256, 4123], [4280, 4060], [4179, 3979], [4291, 3792], [4255, 3716], [4261, 3539], [4274, 3468], [4321, 3452], [4348, 3224], [4434, 3056], [4390, 2943], [3961, 3053], [3744, 3018], [3706, 3028], [3545, 3112], [3568, 3110], [3544, 3299], [3578, 3358], [3694, 3455], [3762, 3490], [3724, 3595], [3673, 3785], [3645, 3798], [3559, 3767], [3518, 3786], [3524, 3866], [3602, 4160], [3631, 4204], [3724, 4289], [3730, 4335], [3677, 4394], [3613, 4426], [3578, 4410], [3560, 4331], [3508, 4355], [3428, 4520], [3309, 4605], [3213, 4707], [3158, 4733], [3099, 4726], [2920, 4648], [2856, 4646], [2698, 4732], [2581, 4745], [2420, 4720], [2296, 4521], [2196, 4613], [2170, 4793], [2167, 4999], [2142, 5169], [2096, 5252], [2039, 5305], [1914, 5348], [1732, 5348], [1697, 5373], [1724, 5422], [1710, 5481], [1629, 5490], [1535, 5413], [1412, 5367], [1295, 5372], [1171, 5436], [1090, 5521], [1075, 5571], [1071, 5712], [995, 5960], [972, 6087], [1011, 6161], [957, 6306], [880, 6324], [857, 6350], [784, 6503], [943, 6580], [1058, 6515], [1177, 6533], [1236, 6448], [1233, 6388], [1302, 6338], [1346, 6328], [1357, 6361], [1483, 6495], [1608, 6589], [1710, 6547], [1821, 6581], [1841, 6442], [2026, 6440], [2104, 6387], [2216, 6608], [2296, 6654], [2326, 6595], [2446, 6648], [2495, 6510], [2607, 6524], [2644, 6487], [2802, 6524], [2832, 6448], [2832, 6448], [2813, 6375], [2868, 6275], [2928, 6254], [3097, 6251], [3153, 6297], [3207, 6281], [3272, 6316], [3292, 6286], [3331, 6361], [3407, 6337], [3407, 6337], [3381, 6264]]], [[[395, 6584], [249, 6538], [184, 6497], [133, 6442], [49, 6297], [12, 6268], [-45, 6283], [-113, 6507], [-20, 6558], [20, 6522], [100, 6537], [112, 6578], [217, 6621], [163, 6709], [325, 6775], [369, 6746], [395, 6584]]]]}}, {"type": "Feature", "id": "BE.490", "properties": {"hc-group": "admin1", "hc-middle-x": 0.48, "hc-middle-y": 0.54, "hc-key": "be-490", "hc-a2": "LI", "labelrank": "7", "hasc": "BE.", "alt-name": null, "woe-id": "7153301", "subregion": null, "fips": null, "postal-code": null, "name": "Limburg", "country": "Belgium", "type-en": "Province", "region": "Flemish", "longitude": "5.41314", "woe-name": "Limburg", "latitude": "50.9954", "woe-label": "Limburg, BE, Belgium", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[6209, 6409], [6260, 6526], [6205, 6557], [6240, 6647], [6214, 6728], [6337, 6862], [6275, 6882], [6290, 7031], [6333, 7162], [6401, 7160], [6421, 7282], [6380, 7316], [6282, 7315], [6271, 7349], [6165, 7295], [6117, 7345], [6053, 7338], [6014, 7433], [6123, 7535], [6122, 7633], [6237, 7689], [6216, 7793], [6259, 7766], [6328, 7796], [6250, 7891], [6188, 7918], [6110, 7905], [6113, 7795], [6065, 7762], [6006, 7796], [5951, 7773], [5904, 7859], [5834, 7838], [5911, 7952], [5909, 8012], [6130, 8063], [6238, 8209], [6377, 8220], [6437, 8314], [6528, 8290], [6595, 8354], [6581, 8532], [6465, 8634], [6490, 8803], [6523, 8832], [6608, 8856], [6936, 8853], [6993, 8876], [7106, 8962], [7160, 8990], [7220, 8985], [7286, 8946], [7340, 8881], [7375, 8697], [7440, 8645], [7598, 8603], [7695, 8528], [7858, 8558], [7940, 8530], [8039, 8423], [8170, 8446], [8205, 8369], [8167, 8329], [8160, 8279], [8226, 8213], [8174, 8183], [8113, 8179], [8135, 8117], [8083, 7994], [8027, 8006], [8010, 7904], [8050, 7854], [8010, 7745], [7907, 7566], [7968, 7585], [8023, 7570], [7948, 7411], [7902, 7344], [7838, 7317], [7645, 7085], [7655, 6987], [7702, 6921], [7750, 6891], [7618, 6748], [7379, 6668], [7387, 6596], [7313, 6528], [7269, 6515], [7212, 6573], [7192, 6495], [7130, 6486], [7054, 6513], [7017, 6598], [6978, 6614], [6851, 6566], [6797, 6515], [6785, 6464], [6694, 6510], [6606, 6452], [6584, 6502], [6565, 6459], [6398, 6475], [6431, 6404], [6397, 6359], [6209, 6409], [6209, 6409], [6209, 6409]]], [[[7968, 6683], [8021, 6750], [8049, 6740], [8138, 6660], [8380, 6675], [8413, 6667], [8468, 6601], [8407, 6499], [8383, 6488], [8314, 6520], [8215, 6502], [8090, 6654], [8019, 6632], [7968, 6683]]]]}}, {"type": "Feature", "id": "BE.3526", "properties": {"hc-group": "admin1", "hc-middle-x": 0.55, "hc-middle-y": 0.43, "hc-key": "be-3526", "hc-a2": "NA", "labelrank": "7", "hasc": "BE.", "alt-name": "Namen", "woe-id": "7153303", "subregion": null, "fips": null, "postal-code": null, "name": "<PERSON><PERSON>", "country": "Belgium", "type-en": "Province", "region": "Walloon", "longitude": "4.88712", "woe-name": "<PERSON><PERSON>", "latitude": "50.3244", "woe-label": "Namur, BE, Belgium", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[4757, 5640], [4745, 5714], [4792, 5753], [4842, 5761], [4945, 5727], [5007, 5742], [4972, 5879], [5001, 5932], [5029, 5933], [5108, 5879], [5120, 5928], [5192, 5950], [5212, 5862], [5324, 5909], [5356, 5863], [5358, 5928], [5657, 6015], [5659, 6042], [5734, 6037], [5795, 6125], [5885, 6107], [5878, 6001], [6018, 5875], [6096, 5666], [6044, 5624], [6201, 5620], [6246, 5517], [6314, 5507], [6335, 5438], [6391, 5448], [6378, 5409], [6410, 5357], [6504, 5398], [6567, 5339], [6623, 5223], [6581, 5135], [6618, 5079], [6759, 5046], [6758, 4964], [6799, 4911], [6950, 5020], [7071, 4983], [7097, 4927], [7041, 4846], [7083, 4795], [6933, 4676], [6955, 4653], [6989, 4705], [7054, 4712], [7085, 4642], [7085, 4576], [7010, 4492], [6814, 4357], [6783, 4395], [6690, 4346], [6699, 4294], [6618, 4297], [6566, 4259], [6677, 4202], [6679, 4113], [6783, 3999], [6759, 3919], [6679, 3890], [6753, 3825], [6740, 3778], [6664, 3783], [6589, 3727], [6529, 3712], [6264, 3686], [6203, 3705], [6183, 3828], [6146, 3658], [6171, 3633], [6143, 3558], [6053, 3459], [5999, 3475], [5981, 3406], [5927, 3406], [5998, 3341], [6039, 3198], [6069, 3184], [6190, 3211], [6217, 3152], [6301, 3112], [6294, 3013], [6357, 2954], [6216, 2893], [6145, 2822], [6194, 2776], [5985, 2617], [5973, 2578], [6000, 2494], [5963, 2483], [5944, 2376], [5891, 2372], [5769, 2326], [5630, 2322], [5604, 2355], [5600, 2520], [5563, 2592], [5622, 2786], [5622, 2881], [5576, 2961], [5440, 3013], [5401, 3074], [5411, 3145], [5514, 3420], [5512, 3543], [5563, 3662], [5611, 3634], [5631, 3880], [5604, 3914], [5515, 3893], [5483, 3906], [5469, 3971], [5393, 3934], [5098, 3621], [5076, 3542], [5098, 3464], [5079, 3323], [5036, 3201], [5005, 3181], [4888, 3159], [4495, 2954], [4390, 2943], [4434, 3056], [4348, 3224], [4321, 3452], [4274, 3468], [4261, 3539], [4255, 3716], [4291, 3792], [4179, 3979], [4280, 4060], [4256, 4123], [4288, 4265], [4187, 4307], [4114, 4244], [4010, 4303], [3962, 4366], [4073, 4450], [4168, 4458], [4203, 4525], [4254, 4523], [4275, 4606], [4432, 4587], [4488, 4688], [4566, 4697], [4619, 4631], [4809, 4667], [4788, 4832], [4854, 4946], [4794, 5013], [4839, 5028], [4878, 5121], [4755, 5191], [4781, 5219], [4826, 5361], [4791, 5407], [4812, 5449], [4711, 5421], [4757, 5640]]]}}]}