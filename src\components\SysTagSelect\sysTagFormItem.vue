<template>
  <div class="editable-select">
    <section
      class="editable-select__main"
    >
      <sys-tag-span
        :primary="primary"
        :is-el-tag="isTag"
        :tags="tags"
        :el-tag-type="tagType"
        :translate-name="translateName"
        :popper-display="popperDisplay"
      />
    </section>
    <section
      v-if="showEditBtn"
      class="editable-select__btn"
    >
      <i
        v-if="!disabled"
        class="fa fa-pencil"
        @click="handleEdit"
      />

      <i
        v-else
        class="fa fa-pencil"
        style="color: #888282; cursor: not-allowed"
      />
    </section>
    <sys-tag-dialog
      :title="$t('unit.编辑') + labelConfig.tagTxt"
      :visible.sync="dialogVisible"
      :primary="primary"
      :multiple="multiple"
      :form-value="tag_value"
      :default-tag-type="defaultTagType"
      :default-tag-type-editable="defaultTagTypeEditable"
      :confirm-action="confirmAction"
      :translate-name="translateName"
      :show-msg="showMsg"
    />
  </div>
</template>

<script>
import SysTagDialog from '@/components/SysTagSelect/sysTagDialog';
import SysTagSpan from '@/components/SysTagSelect/sysTagSpan';
import i18n from '@/lang';

export default {
  name: 'SysTagFormItem',
  components: {
    SysTagSpan,
    SysTagDialog
  },
  provide() {
    return {
      tagTypeLabel: this.labelConfig.tagTypeLabel,
      tagNameLabel: this.labelConfig.tagNameLabel,
      tagTxt: this.labelConfig.tagTxt
    };
  },
  props: {
    // 标签id
    tags: {
      type: Array,
      default: () => []
    },
    primary: {
      type: String,
      default: '角色培育通用'
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    isTag: {
      type: Boolean,
      default: true
    },
    // 标签样式
    tagType: {
      type: String,
      default: 'info'
    },
    // 是否可以编辑
    showEditBtn: {
      type: Boolean,
      default: true
    },
    confirmAction: {
      type: [String, Function],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    popperDisplay: {
      type: String,
      default: 'every'
    },
    labelConfig: {
      type: Object,
      default: () => ({
        tagTypeLabel: i18n.t('components["标签类型"]'),
        tagNameLabel: i18n.t('components["标签名称"]'),
        tagTxt: i18n.t('components["标签"]')
      })
    },
    defaultTagType: {
      type: String,
      default: '',
      info: '默认选中的标签类型'
    },
    defaultTagTypeEditable: {
      type: Boolean,
      default: true,
      info: '默认选中的标签类型是否可以被修改'
    },
    translateName: {
      type: String,
      default: ''
    },
    // 是否显示添加标签成功的提示
    showMsg: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tag_value: [],
      dialogVisible: false
    };
  },
  methods: {
    handleEdit() {
      this.tag_value = this.tags ? [...this.tags] : [];
      this.dialogVisible = true;
    },
    handleCancel() {
      this.dialogVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>

.editable-select {
  display: flex;
  ::v-deep &__main {
    display: inline-block;
    max-width: calc(100% - 20px);
    .el-select__tags-text {
      display: inline-block;
      max-width: 50px;
      @include utils-ellipsis(1);
      vertical-align: bottom;
    }
    ::v-deep
    .operateDropOption
    .el-scrollbar
    .el-select-dropdown__wrap
    .el-select-dropdown__item::after {
      right: 6px;
    }
  }
  &__btn {
    display: inline-block;
    transform: translateY(-1px);
    .fa-pencil{
      color: #3381D0;
      cursor: pointer;
      font-size: 16px;
      vertical-align: middle;
      margin-left: 5px;
    }
  }
}
</style>
