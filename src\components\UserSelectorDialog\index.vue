<template>
  <titan-dialog
    v-if="showDialog"
    :title="title"
    width="90%"
    top="20px"
    class="user-selector-dialog"
    :visible.sync="showDialog"
    :config="{
      showConfirm: true
    }"
    append-to-body
    @close="handleClose"
    @cancel="handleCancel"
    @confirm="handleConfirm"
  >
    <user-selector
      ref="user"
      :selected="selectedUser"
      :search-params="searchParams"
    />
  </titan-dialog>
</template>

<script>
import UserSelector from '@/components/UserSelector';
export default {
  name: 'UserSelectorDialog',
  components: { UserSelector },
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    selectedUser: {
      type: Array,
      default: () => []
    },
    searchParams: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    showDialog: {
      set(val) {
        this.$emit('update:visible', val);
      },
      get() {
        return this.visible;
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    handleClose() {
      this.$emit('close');
    },
    handleConfirm() {
      this.$emit('confirm', this.$refs['user'].selectedList);
    }
  }
};
</script>

<style lang="scss" scoped>
@at-root body .user-selector-dialog {
  ::v-deep .el-dialog__header{
    border-bottom: none;
  }
}
</style>
