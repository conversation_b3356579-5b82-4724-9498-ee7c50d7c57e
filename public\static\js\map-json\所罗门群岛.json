{"title": "Solomon Islands", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:32757"}}, "hc-transform": {"default": {"crs": "+proj=utm +zone=57 +south +datum=WGS84 +units=m +no_defs", "scale": 0.000479797478364, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 114387.732678, "yoffset": 9400293.04984}}, "features": [{"type": "Feature", "id": "SB.4191", "properties": {"hc-group": "admin1", "hc-middle-x": 0.0, "hc-middle-y": 0.25, "hc-key": "sb-4191", "hc-a2": "NU", "labelrank": "20", "hasc": "-99", "alt-name": null, "woe-id": "-99", "subregion": null, "fips": null, "postal-code": null, "name": null, "country": "Solomon Islands", "type-en": null, "region": null, "longitude": "159.413", "woe-name": null, "latitude": "-5.10703", "woe-label": null, "type": null}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4670, 5348], [4675, 5334], [4660, 5335], [4656, 5344], [4670, 5348]]], [[[4695, 5374], [4686, 5372], [4676, 5375], [4674, 5385], [4679, 5391], [4678, 5405], [4690, 5411], [4701, 5401], [4707, 5387], [4695, 5374]]], [[[7406, 5455], [7395, 5452], [7408, 5473], [7411, 5491], [7418, 5494], [7431, 5484], [7434, 5474], [7425, 5459], [7406, 5455]]], [[[4288, 5890], [4299, 5875], [4296, 5869], [4290, 5880], [4281, 5884], [4277, 5894], [4282, 5900], [4288, 5890]]], [[[2710, 7015], [2716, 7008], [2721, 6989], [2711, 6986], [2698, 7001], [2690, 7006], [2691, 6994], [2685, 7000], [2679, 6981], [2672, 6990], [2677, 7003], [2690, 7015], [2696, 7012], [2710, 7015]]], [[[-579, 8734], [-565, 8731], [-564, 8725], [-579, 8707], [-601, 8699], [-605, 8713], [-601, 8727], [-579, 8734]]], [[[2455, 9794], [2452, 9791], [2451, 9810], [2457, 9806], [2455, 9794]]], [[[2177, 9838], [2164, 9840], [2158, 9849], [2162, 9851], [2172, 9844], [2177, 9838]]]]}}, {"type": "Feature", "id": "SB.ML", "properties": {"hc-group": "admin1", "hc-middle-x": 0.2, "hc-middle-y": 0.59, "hc-key": "sb-ml", "hc-a2": "ML", "labelrank": "6", "hasc": "SB.ML", "alt-name": null, "woe-id": "2344836", "subregion": null, "fips": "BP03", "postal-code": "ML", "name": "Malaita", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "161.956", "woe-name": "Malaita", "latitude": "-9.75831", "woe-label": "Malaita, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4299, 6337], [4298, 6326], [4287, 6309], [4292, 6297], [4302, 6229], [4302, 6216], [4298, 6202], [4286, 6212], [4276, 6230], [4268, 6252], [4259, 6310], [4260, 6321], [4268, 6333], [4289, 6333], [4299, 6337]]], [[[4937, 7402], [4932, 7401], [4925, 7409], [4929, 7413], [4947, 7409], [4937, 7402]]], [[[3220, 7792], [3207, 7792], [3191, 7813], [3187, 7823], [3190, 7833], [3200, 7835], [3210, 7821], [3220, 7792]]], [[[3882, 6338], [3875, 6315], [3867, 6301], [3854, 6315], [3839, 6334], [3828, 6355], [3823, 6372], [3824, 6470], [3823, 6499], [3836, 6522], [3834, 6536], [3827, 6549], [3825, 6566], [3808, 6561], [3808, 6578], [3803, 6594], [3786, 6623], [3796, 6627], [3813, 6606], [3841, 6588], [3847, 6580], [3850, 6567], [3875, 6532], [3895, 6490], [3909, 6471], [3915, 6453], [3936, 6427], [3946, 6407], [3952, 6392], [3953, 6378], [3952, 6348], [3955, 6335], [3966, 6307], [3968, 6289], [3964, 6270], [3956, 6256], [3948, 6253], [3945, 6270], [3912, 6307], [3899, 6336], [3890, 6340], [3882, 6338]]], [[[3461, 7061], [3476, 7047], [3485, 7031], [3496, 7033], [3516, 7042], [3523, 7039], [3546, 7022], [3553, 7014], [3548, 6998], [3559, 6987], [3569, 6991], [3589, 6982], [3609, 6967], [3628, 6949], [3642, 6928], [3632, 6932], [3623, 6926], [3621, 6917], [3638, 6908], [3647, 6897], [3652, 6884], [3647, 6872], [3658, 6862], [3660, 6838], [3669, 6836], [3690, 6821], [3689, 6809], [3669, 6793], [3684, 6786], [3692, 6776], [3703, 6780], [3712, 6778], [3717, 6770], [3714, 6759], [3736, 6753], [3736, 6748], [3706, 6742], [3703, 6738], [3707, 6719], [3716, 6708], [3721, 6678], [3730, 6669], [3719, 6663], [3710, 6676], [3703, 6680], [3695, 6675], [3703, 6657], [3735, 6623], [3745, 6610], [3770, 6545], [3786, 6531], [3797, 6515], [3813, 6487], [3822, 6422], [3822, 6405], [3817, 6394], [3808, 6392], [3796, 6398], [3753, 6428], [3746, 6429], [3743, 6443], [3738, 6455], [3706, 6498], [3700, 6513], [3696, 6539], [3687, 6553], [3649, 6593], [3593, 6622], [3573, 6645], [3535, 6650], [3540, 6664], [3517, 6690], [3506, 6698], [3487, 6698], [3478, 6707], [3478, 6727], [3459, 6743], [3413, 6764], [3395, 6783], [3336, 6893], [3322, 6933], [3311, 6953], [3317, 6964], [3311, 6975], [3311, 6987], [3299, 7028], [3287, 7046], [3254, 7114], [3251, 7125], [3251, 7169], [3249, 7177], [3223, 7235], [3237, 7245], [3261, 7265], [3276, 7269], [3277, 7276], [3257, 7320], [3225, 7362], [3221, 7370], [3210, 7375], [3182, 7403], [3174, 7415], [3164, 7452], [3180, 7464], [3211, 7461], [3241, 7449], [3257, 7434], [3263, 7432], [3281, 7438], [3286, 7444], [3283, 7452], [3258, 7468], [3266, 7480], [3280, 7482], [3280, 7477], [3299, 7477], [3309, 7471], [3325, 7449], [3343, 7436], [3351, 7429], [3362, 7405], [3366, 7385], [3381, 7375], [3409, 7333], [3451, 7287], [3484, 7259], [3503, 7234], [3511, 7218], [3514, 7203], [3508, 7186], [3492, 7176], [3474, 7169], [3458, 7161], [3467, 7140], [3467, 7129], [3453, 7107], [3453, 7096], [3461, 7061]]]]}}, {"type": "Feature", "id": "SB.RB", "properties": {"hc-group": "admin1", "hc-middle-x": 0.83, "hc-middle-y": 0.84, "hc-key": "sb-rb", "hc-a2": "RB", "labelrank": "6", "hasc": "SB.RB", "alt-name": null, "woe-id": "-2344841", "subregion": null, "fips": "BP10", "postal-code": "RB", "name": "Rennell and Bellona", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "160.273", "woe-name": null, "latitude": "-11.6204", "woe-label": null, "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[2559, 5014], [2561, 5000], [2542, 5008], [2527, 5012], [2513, 5018], [2500, 5034], [2512, 5034], [2522, 5028], [2550, 5022], [2559, 5014]]], [[[3136, 4629], [3151, 4611], [3132, 4597], [3108, 4601], [3099, 4597], [3088, 4575], [3082, 4569], [3070, 4570], [3052, 4595], [3043, 4603], [3031, 4598], [3021, 4600], [2991, 4621], [2987, 4626], [2978, 4661], [2971, 4676], [2955, 4689], [2921, 4694], [2911, 4701], [2910, 4731], [2899, 4745], [2884, 4751], [2866, 4751], [2850, 4728], [2833, 4727], [2816, 4729], [2802, 4734], [2771, 4751], [2755, 4757], [2761, 4769], [2746, 4779], [2733, 4778], [2713, 4769], [2699, 4772], [2688, 4782], [2665, 4811], [2662, 4822], [2650, 4838], [2660, 4862], [2677, 4877], [2683, 4887], [2694, 4881], [2706, 4869], [2722, 4859], [2751, 4859], [2758, 4856], [2774, 4841], [2791, 4830], [2836, 4808], [2860, 4790], [2889, 4776], [2913, 4758], [2929, 4750], [2965, 4739], [2999, 4724], [3087, 4659], [3136, 4629]]]]}}, {"type": "Feature", "id": "SB.1014", "properties": {"hc-group": "admin1", "hc-middle-x": 0.84, "hc-middle-y": 0.44, "hc-key": "sb-1014", "hc-a2": "CE", "labelrank": "6", "hasc": "SB.GC", "alt-name": null, "woe-id": "2344838", "subregion": null, "fips": "BP10", "postal-code": null, "name": "Central", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "159.825", "woe-name": "Central", "latitude": "-9.140029999999999", "woe-label": "Central, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[2549, 6826], [2555, 6811], [2559, 6792], [2557, 6776], [2546, 6770], [2534, 6775], [2527, 6788], [2526, 6805], [2532, 6820], [2545, 6822], [2549, 6826]]], [[[3014, 6792], [3002, 6770], [2985, 6757], [2968, 6763], [2951, 6773], [2935, 6779], [2919, 6774], [2922, 6764], [2910, 6757], [2891, 6753], [2874, 6752], [2878, 6764], [2862, 6769], [2874, 6780], [2893, 6780], [2912, 6789], [2925, 6802], [2935, 6843], [2944, 6860], [2958, 6858], [2980, 6819], [2988, 6813], [3011, 6811], [3024, 6802], [3014, 6792]]], [[[2046, 6851], [2051, 6847], [2062, 6850], [2059, 6829], [2048, 6816], [2011, 6798], [2012, 6821], [2015, 6836], [2028, 6866], [2037, 6867], [2053, 6886], [2062, 6893], [2056, 6883], [2056, 6866], [2048, 6858], [2046, 6851]]], [[[1978, 6922], [1984, 6911], [1987, 6895], [1999, 6888], [2005, 6898], [2012, 6899], [2028, 6893], [2001, 6838], [1989, 6827], [1950, 6815], [1931, 6833], [1922, 6827], [1901, 6856], [1902, 6865], [1928, 6866], [1923, 6877], [1925, 6885], [1933, 6887], [1944, 6883], [1944, 6893], [1961, 6888], [1956, 6905], [1944, 6917], [1956, 6917], [1970, 6912], [1978, 6922]]], [[[2779, 6932], [2779, 6916], [2767, 6921], [2760, 6915], [2751, 6892], [2741, 6900], [2742, 6908], [2751, 6929], [2747, 6951], [2750, 6958], [2762, 6960], [2775, 6943], [2779, 6932]]], [[[2772, 6882], [2787, 6887], [2796, 6898], [2802, 6892], [2807, 6904], [2802, 6916], [2807, 6919], [2813, 6915], [2827, 6909], [2842, 6910], [2857, 6909], [2868, 6892], [2875, 6892], [2883, 6905], [2897, 6903], [2913, 6892], [2927, 6878], [2932, 6860], [2927, 6837], [2915, 6815], [2902, 6802], [2896, 6811], [2885, 6808], [2884, 6818], [2872, 6830], [2863, 6830], [2868, 6814], [2863, 6814], [2849, 6835], [2839, 6844], [2814, 6834], [2799, 6833], [2784, 6836], [2773, 6842], [2770, 6861], [2755, 6868], [2751, 6879], [2772, 6882]]]]}}, {"type": "Feature", "id": "SB.IS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.44, "hc-middle-y": 0.44, "hc-key": "sb-is", "hc-a2": "IS", "labelrank": "6", "hasc": "SB.IS", "alt-name": null, "woe-id": "2344840", "subregion": null, "fips": "BP07", "postal-code": "IS", "name": "<PERSON>", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "159.619", "woe-name": "<PERSON>", "latitude": "-8.446949999999999", "woe-label": "Isabel, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[2408, 7337], [2442, 7308], [2441, 7302], [2425, 7290], [2409, 7290], [2389, 7299], [2371, 7317], [2361, 7332], [2354, 7335], [2343, 7327], [2331, 7340], [2319, 7347], [2310, 7357], [2309, 7378], [2315, 7400], [2326, 7414], [2342, 7422], [2365, 7424], [2389, 7418], [2396, 7405], [2393, 7367], [2398, 7349], [2408, 7337]]], [[[1299, 8160], [1304, 8150], [1314, 8154], [1324, 8150], [1343, 8134], [1353, 8142], [1371, 8141], [1390, 8133], [1405, 8122], [1392, 8113], [1394, 8095], [1357, 8102], [1348, 8106], [1325, 8130], [1301, 8136], [1283, 8148], [1266, 8155], [1269, 8172], [1275, 8173], [1299, 8160]]], [[[1269, 8235], [1269, 8213], [1280, 8218], [1275, 8202], [1275, 8185], [1258, 8196], [1253, 8167], [1241, 8175], [1225, 8195], [1208, 8196], [1208, 8201], [1219, 8213], [1232, 8211], [1253, 8231], [1269, 8235]]], [[[1375, 8084], [1395, 8080], [1405, 8067], [1418, 8061], [1447, 8055], [1459, 8050], [1472, 8038], [1480, 8024], [1478, 8015], [1465, 8014], [1453, 8021], [1438, 8043], [1430, 8032], [1414, 8036], [1382, 8055], [1384, 8047], [1394, 8027], [1366, 8032], [1346, 8032], [1343, 8049], [1334, 8060], [1317, 8066], [1308, 8080], [1320, 8082], [1346, 8074], [1350, 8076], [1345, 8090], [1359, 8094], [1375, 8084]]], [[[1871, 7859], [1883, 7863], [1915, 7850], [1925, 7843], [1934, 7821], [1939, 7814], [1950, 7818], [1973, 7801], [1991, 7809], [2006, 7813], [2021, 7812], [2039, 7807], [2029, 7802], [2032, 7795], [2048, 7784], [2070, 7779], [2078, 7762], [2092, 7772], [2101, 7765], [2107, 7752], [2116, 7745], [2142, 7743], [2153, 7746], [2158, 7756], [2173, 7749], [2186, 7739], [2199, 7749], [2207, 7740], [2222, 7708], [2236, 7698], [2281, 7672], [2287, 7661], [2325, 7630], [2400, 7585], [2558, 7466], [2562, 7458], [2544, 7440], [2527, 7416], [2521, 7412], [2505, 7418], [2495, 7417], [2489, 7407], [2502, 7398], [2509, 7384], [2524, 7388], [2531, 7395], [2538, 7388], [2563, 7354], [2569, 7342], [2572, 7313], [2596, 7295], [2606, 7281], [2595, 7271], [2588, 7273], [2561, 7288], [2544, 7288], [2536, 7303], [2521, 7309], [2512, 7324], [2495, 7329], [2478, 7350], [2452, 7370], [2444, 7378], [2442, 7399], [2430, 7401], [2420, 7406], [2382, 7440], [2314, 7455], [2285, 7472], [2265, 7481], [2246, 7496], [2233, 7503], [2219, 7506], [2208, 7503], [2180, 7519], [2165, 7524], [2152, 7519], [2143, 7538], [2136, 7549], [2127, 7553], [2104, 7558], [2097, 7565], [2096, 7576], [2078, 7570], [2060, 7573], [2044, 7582], [1967, 7638], [1944, 7638], [1941, 7643], [1939, 7661], [1933, 7662], [1922, 7655], [1899, 7666], [1877, 7666], [1871, 7683], [1851, 7694], [1849, 7706], [1826, 7694], [1825, 7711], [1817, 7723], [1806, 7731], [1790, 7734], [1742, 7756], [1738, 7788], [1731, 7802], [1717, 7798], [1692, 7804], [1680, 7812], [1686, 7824], [1669, 7841], [1663, 7841], [1655, 7829], [1646, 7840], [1635, 7869], [1613, 7890], [1599, 7895], [1585, 7891], [1577, 7906], [1541, 7925], [1528, 7936], [1524, 7947], [1528, 7973], [1528, 8007], [1464, 8060], [1422, 8083], [1415, 8095], [1413, 8110], [1419, 8119], [1433, 8117], [1450, 8107], [1531, 8079], [1537, 8075], [1545, 8075], [1561, 8093], [1573, 8088], [1585, 8095], [1591, 8079], [1603, 8068], [1635, 8055], [1650, 8070], [1665, 8069], [1677, 8041], [1693, 8026], [1717, 8010], [1730, 7986], [1744, 7978], [1764, 7953], [1774, 7947], [1804, 7937], [1818, 7928], [1843, 7907], [1860, 7897], [1852, 7881], [1858, 7866], [1871, 7859]]]]}}, {"type": "Feature", "id": "SB.TE", "properties": {"hc-group": "admin1", "hc-middle-x": 0.07, "hc-middle-y": 0.34, "hc-key": "sb-te", "hc-a2": "TE", "labelrank": "6", "hasc": "SB.TE", "alt-name": "Eastern Islands", "woe-id": "20069919", "subregion": null, "fips": "BP09", "postal-code": "TE", "name": "Temotu", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "168.812", "woe-name": "Temotu", "latitude": "-12.277", "woe-label": "Temotu, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[9845, 4065], [9832, 4060], [9828, 4068], [9840, 4080], [9849, 4079], [9851, 4067], [9845, 4065]]], [[[8343, 4642], [8329, 4640], [8323, 4649], [8328, 4663], [8339, 4673], [8354, 4674], [8362, 4671], [8357, 4651], [8343, 4642]]], [[[8026, 4974], [8019, 4955], [8000, 4935], [7985, 4933], [7972, 4939], [7973, 4952], [7981, 4963], [7980, 4975], [7959, 4961], [7954, 4968], [7957, 4987], [7969, 5001], [7983, 5014], [7994, 5017], [8007, 5013], [8019, 5002], [8026, 4990], [8026, 4974]]], [[[7371, 5685], [7357, 5684], [7353, 5692], [7356, 5707], [7361, 5711], [7373, 5708], [7379, 5697], [7378, 5689], [7371, 5685]]], [[[7783, 5842], [7782, 5802], [7802, 5806], [7812, 5798], [7804, 5783], [7788, 5769], [7777, 5767], [7778, 5790], [7770, 5796], [7776, 5814], [7780, 5831], [7779, 5847], [7783, 5842]]], [[[8571, 6095], [8564, 6094], [8554, 6101], [8548, 6116], [8552, 6124], [8561, 6123], [8570, 6112], [8573, 6101], [8571, 6095]]], [[[8249, 4692], [8268, 4695], [8287, 4690], [8321, 4677], [8311, 4650], [8310, 4623], [8355, 4625], [8359, 4592], [8297, 4589], [8280, 4588], [8243, 4592], [8232, 4609], [8222, 4619], [8211, 4623], [8183, 4660], [8183, 4674], [8190, 4682], [8215, 4694], [8249, 4692]]], [[[7432, 5360], [7409, 5339], [7384, 5341], [7373, 5361], [7372, 5411], [7387, 5422], [7405, 5446], [7412, 5446], [7408, 5418], [7420, 5404], [7428, 5401], [7432, 5412], [7436, 5446], [7450, 5465], [7476, 5488], [7513, 5489], [7571, 5482], [7598, 5493], [7626, 5487], [7639, 5479], [7645, 5471], [7669, 5474], [7692, 5472], [7696, 5452], [7687, 5443], [7683, 5427], [7652, 5415], [7642, 5407], [7618, 5393], [7597, 5393], [7576, 5402], [7558, 5389], [7549, 5392], [7519, 5388], [7484, 5349], [7459, 5313], [7448, 5323], [7451, 5346], [7442, 5358], [7432, 5360]]]]}}, {"type": "Feature", "id": "SB.3343", "properties": {"hc-group": "admin1", "hc-middle-x": 0.72, "hc-middle-y": 0.65, "hc-key": "sb-3343", "hc-a2": "WE", "labelrank": "6", "hasc": "SB.WE", "alt-name": "<PERSON><PERSON>", "woe-id": "2344837", "subregion": null, "fips": "BP11", "postal-code": null, "name": "Western", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "157.551", "woe-name": "Western", "latitude": "-8.729990000000001", "woe-label": "Western, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[716, 7148], [730, 7132], [753, 7121], [764, 7103], [764, 7086], [742, 7078], [705, 7095], [666, 7106], [623, 7124], [603, 7138], [605, 7151], [636, 7147], [649, 7146], [661, 7151], [666, 7145], [673, 7156], [682, 7163], [690, 7162], [698, 7153], [716, 7148]]], [[[1217, 7051], [1204, 7046], [1188, 7070], [1177, 7079], [1177, 7089], [1186, 7118], [1210, 7136], [1218, 7153], [1221, 7175], [1227, 7176], [1227, 7158], [1233, 7146], [1239, 7127], [1241, 7108], [1238, 7097], [1261, 7088], [1257, 7074], [1253, 7070], [1234, 7065], [1217, 7051]]], [[[356, 7460], [356, 7454], [332, 7467], [320, 7498], [305, 7526], [271, 7533], [283, 7544], [269, 7550], [255, 7561], [249, 7572], [260, 7578], [271, 7569], [292, 7568], [313, 7564], [322, 7547], [323, 7536], [333, 7505], [338, 7477], [344, 7466], [356, 7460]]], [[[310, 7600], [330, 7593], [349, 7595], [367, 7593], [384, 7578], [391, 7561], [398, 7540], [401, 7516], [401, 7494], [395, 7494], [394, 7505], [372, 7510], [361, 7539], [344, 7566], [332, 7576], [316, 7584], [322, 7589], [310, 7592], [310, 7600]]], [[[74, 7684], [77, 7671], [86, 7673], [100, 7637], [78, 7638], [54, 7659], [57, 7684], [74, 7684]]], [[[-94, 7572], [-108, 7565], [-116, 7575], [-136, 7635], [-149, 7653], [-151, 7671], [-145, 7711], [-155, 7753], [-154, 7771], [-140, 7779], [-118, 7756], [-109, 7728], [-98, 7710], [-93, 7695], [-85, 7648], [-85, 7636], [-89, 7626], [-82, 7609], [-85, 7588], [-94, 7572]]], [[[-136, 7879], [-153, 7871], [-168, 7878], [-167, 7885], [-152, 7900], [-144, 7900], [-133, 7891], [-136, 7879]]], [[[-948, 8280], [-935, 8274], [-908, 8247], [-925, 8231], [-943, 8227], [-985, 8230], [-999, 8237], [-994, 8252], [-971, 8280], [-948, 8280]]], [[[-747, 8531], [-716, 8522], [-705, 8508], [-705, 8497], [-713, 8472], [-719, 8466], [-748, 8461], [-758, 8463], [-771, 8451], [-799, 8452], [-814, 8445], [-837, 8462], [-860, 8469], [-865, 8473], [-864, 8484], [-843, 8530], [-836, 8526], [-829, 8553], [-815, 8558], [-818, 8565], [-815, 8575], [-805, 8571], [-775, 8547], [-747, 8531]]], [[[1008, 7116], [1006, 7121], [992, 7129], [980, 7146], [952, 7172], [952, 7214], [957, 7243], [969, 7259], [968, 7271], [990, 7257], [999, 7257], [1002, 7271], [1011, 7268], [1014, 7259], [1020, 7270], [1014, 7286], [1013, 7299], [1018, 7305], [1058, 7322], [1064, 7310], [1055, 7299], [1061, 7285], [1072, 7281], [1081, 7285], [1097, 7299], [1114, 7303], [1134, 7303], [1152, 7298], [1159, 7285], [1161, 7268], [1153, 7254], [1129, 7252], [1122, 7243], [1106, 7236], [1109, 7226], [1125, 7224], [1131, 7220], [1148, 7226], [1137, 7212], [1125, 7203], [1144, 7185], [1137, 7165], [1104, 7136], [1090, 7116], [1062, 7104], [1050, 7103], [1032, 7106], [1008, 7116]]], [[[418, 7302], [440, 7314], [453, 7323], [468, 7354], [491, 7377], [516, 7393], [531, 7392], [546, 7382], [558, 7367], [567, 7346], [570, 7322], [556, 7303], [547, 7276], [541, 7264], [535, 7257], [514, 7261], [507, 7253], [509, 7235], [503, 7211], [502, 7198], [506, 7187], [536, 7155], [552, 7144], [571, 7139], [571, 7134], [558, 7131], [543, 7132], [528, 7135], [508, 7151], [488, 7159], [487, 7184], [459, 7223], [419, 7243], [403, 7256], [396, 7266], [404, 7288], [418, 7302]]], [[[951, 7274], [957, 7254], [907, 7274], [884, 7276], [884, 7271], [896, 7264], [903, 7254], [906, 7240], [901, 7225], [896, 7225], [894, 7239], [878, 7256], [876, 7268], [868, 7280], [850, 7299], [829, 7310], [784, 7314], [763, 7323], [727, 7348], [704, 7374], [703, 7386], [693, 7416], [693, 7466], [703, 7447], [704, 7425], [708, 7415], [728, 7409], [735, 7393], [746, 7403], [749, 7413], [744, 7422], [725, 7440], [717, 7467], [708, 7486], [695, 7505], [681, 7517], [671, 7520], [642, 7511], [625, 7522], [620, 7511], [605, 7516], [581, 7506], [553, 7505], [523, 7508], [513, 7506], [501, 7478], [492, 7464], [474, 7454], [460, 7457], [432, 7473], [418, 7471], [412, 7547], [426, 7569], [431, 7573], [452, 7574], [468, 7584], [472, 7574], [478, 7571], [490, 7579], [492, 7604], [496, 7613], [493, 7622], [497, 7634], [523, 7668], [536, 7695], [543, 7706], [578, 7740], [591, 7749], [641, 7765], [647, 7760], [704, 7728], [726, 7726], [720, 7712], [731, 7678], [730, 7647], [732, 7632], [738, 7619], [730, 7604], [733, 7594], [751, 7577], [758, 7564], [752, 7544], [754, 7535], [760, 7541], [777, 7535], [792, 7543], [815, 7549], [829, 7549], [841, 7545], [865, 7528], [873, 7518], [878, 7496], [888, 7482], [909, 7468], [917, 7456], [916, 7435], [922, 7420], [940, 7405], [947, 7392], [952, 7363], [957, 7349], [971, 7344], [974, 7338], [971, 7323], [953, 7291], [951, 7274]]], [[[303, 7854], [342, 7834], [369, 7805], [386, 7770], [394, 7731], [395, 7692], [393, 7678], [381, 7643], [367, 7620], [350, 7609], [332, 7628], [322, 7625], [316, 7628], [274, 7625], [232, 7659], [198, 7708], [186, 7747], [190, 7767], [197, 7786], [217, 7816], [230, 7833], [256, 7843], [284, 7851], [303, 7854]]], [[[-26, 7783], [-35, 7785], [-50, 7779], [-52, 7796], [-48, 7812], [-51, 7825], [-90, 7835], [-92, 7848], [-84, 7883], [-88, 7889], [-107, 7903], [-118, 7925], [-124, 7931], [-145, 7937], [-145, 7943], [-154, 7962], [-158, 7964], [-169, 7959], [-185, 7996], [-179, 8033], [-157, 8064], [-125, 8083], [-99, 8076], [-92, 8083], [-85, 8070], [-75, 8061], [-73, 8050], [-60, 8049], [-57, 8036], [-38, 8024], [-31, 8004], [-23, 7999], [-6, 7994], [-12, 7988], [-1, 7976], [18, 7983], [36, 7976], [54, 7966], [70, 7960], [76, 7948], [64, 7922], [34, 7881], [19, 7868], [-1, 7847], [-14, 7824], [-8, 7804], [0, 7794], [0, 7785], [-6, 7778], [-16, 7774], [-26, 7783]]], [[[-538, 8658], [-513, 8650], [-517, 8639], [-504, 8621], [-493, 8613], [-471, 8606], [-488, 8600], [-505, 8606], [-511, 8581], [-517, 8569], [-528, 8560], [-534, 8577], [-545, 8566], [-562, 8565], [-536, 8586], [-534, 8592], [-543, 8604], [-545, 8611], [-538, 8626], [-525, 8630], [-540, 8639], [-550, 8654], [-559, 8671], [-557, 8688], [-534, 8701], [-517, 8701], [-517, 8696], [-528, 8693], [-537, 8687], [-543, 8677], [-545, 8665], [-538, 8658]]]]}}, {"type": "Feature", "id": "SB.CH", "properties": {"hc-group": "admin1", "hc-middle-x": 0.39, "hc-middle-y": 0.5, "hc-key": "sb-ch", "hc-a2": "CH", "labelrank": "6", "hasc": "SB.CH", "alt-name": "<PERSON><PERSON>", "woe-id": "-2344837", "subregion": null, "fips": "BP11", "postal-code": "CH", "name": "Choiseul", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "157.547", "woe-name": null, "latitude": "-7.40991", "woe-label": null, "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[645, 8234], [667, 8229], [690, 8232], [711, 8231], [724, 8216], [729, 8223], [735, 8216], [728, 8202], [735, 8183], [713, 8199], [712, 8187], [704, 8183], [678, 8183], [678, 8188], [695, 8199], [647, 8215], [626, 8214], [618, 8216], [645, 8234]]], [[[876, 8177], [882, 8166], [848, 8166], [834, 8171], [817, 8166], [805, 8176], [784, 8187], [793, 8207], [820, 8240], [859, 8226], [864, 8220], [866, 8208], [876, 8177]]], [[[268, 8631], [286, 8607], [318, 8581], [325, 8569], [327, 8555], [329, 8520], [338, 8483], [351, 8451], [376, 8422], [380, 8407], [397, 8418], [403, 8404], [414, 8393], [428, 8387], [445, 8384], [456, 8379], [464, 8368], [471, 8342], [475, 8335], [510, 8322], [529, 8309], [544, 8289], [549, 8289], [551, 8302], [557, 8303], [575, 8295], [601, 8296], [631, 8307], [647, 8308], [658, 8300], [662, 8281], [662, 8261], [659, 8250], [649, 8245], [626, 8244], [616, 8238], [595, 8199], [590, 8214], [578, 8211], [567, 8219], [547, 8224], [510, 8227], [494, 8232], [483, 8243], [480, 8258], [487, 8277], [480, 8275], [465, 8260], [455, 8261], [454, 8249], [438, 8258], [409, 8268], [392, 8277], [348, 8280], [341, 8280], [336, 8272], [324, 8272], [312, 8280], [303, 8305], [283, 8328], [273, 8333], [250, 8322], [239, 8321], [245, 8333], [238, 8346], [225, 8357], [209, 8364], [192, 8367], [180, 8371], [167, 8381], [90, 8470], [81, 8497], [62, 8504], [56, 8511], [47, 8529], [15, 8574], [-3, 8588], [-32, 8637], [-46, 8646], [-85, 8666], [-107, 8674], [-219, 8770], [-224, 8782], [-231, 8827], [-231, 8843], [-224, 8855], [-197, 8878], [-183, 8880], [-164, 8878], [-148, 8872], [-138, 8861], [-126, 8854], [-120, 8840], [-100, 8845], [-69, 8836], [-47, 8819], [-5, 8772], [7, 8762], [44, 8737], [79, 8728], [131, 8693], [165, 8677], [195, 8671], [228, 8656], [235, 8640], [260, 8638], [268, 8631]]]]}}, {"type": "Feature", "id": "SB.MK", "properties": {"hc-group": "admin1", "hc-middle-x": 0.54, "hc-middle-y": 0.59, "hc-key": "sb-mk", "hc-a2": "MK", "labelrank": "6", "hasc": "SB.MK", "alt-name": "Makira and Ulawa", "woe-id": "2344841", "subregion": null, "fips": "BP08", "postal-code": "MK", "name": "<PERSON><PERSON><PERSON>", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "161.744", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "-10.2782", "woe-label": "Makira, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4126, 5851], [4107, 5842], [4098, 5851], [4106, 5864], [4093, 5871], [4083, 5891], [4083, 5901], [4094, 5911], [4111, 5896], [4125, 5873], [4126, 5851]]], [[[4178, 5712], [4195, 5711], [4214, 5715], [4226, 5723], [4256, 5712], [4270, 5711], [4282, 5717], [4301, 5694], [4319, 5685], [4345, 5688], [4363, 5687], [4367, 5704], [4373, 5708], [4388, 5710], [4404, 5702], [4426, 5671], [4444, 5659], [4451, 5646], [4467, 5602], [4470, 5596], [4486, 5586], [4490, 5559], [4496, 5546], [4507, 5537], [4536, 5517], [4544, 5507], [4547, 5494], [4547, 5447], [4528, 5432], [4525, 5427], [4530, 5406], [4556, 5402], [4588, 5403], [4614, 5398], [4622, 5402], [4628, 5395], [4627, 5385], [4616, 5380], [4586, 5386], [4575, 5381], [4569, 5384], [4535, 5389], [4477, 5412], [4458, 5410], [4440, 5393], [4421, 5400], [4391, 5402], [4374, 5417], [4338, 5431], [4318, 5445], [4284, 5436], [4269, 5442], [4235, 5452], [4221, 5446], [4191, 5466], [4185, 5481], [4179, 5463], [4162, 5477], [4152, 5479], [4140, 5475], [4129, 5492], [4121, 5489], [4115, 5494], [4113, 5509], [4105, 5519], [4084, 5518], [4074, 5521], [4068, 5529], [4061, 5546], [4049, 5549], [4034, 5565], [4023, 5572], [4014, 5562], [4005, 5568], [3990, 5583], [3987, 5595], [3974, 5601], [3974, 5613], [3963, 5624], [3956, 5616], [3946, 5613], [3923, 5618], [3941, 5669], [3932, 5675], [3931, 5691], [3922, 5701], [3896, 5697], [3910, 5720], [3910, 5732], [3896, 5737], [3911, 5767], [3921, 5773], [3914, 5782], [3891, 5782], [3877, 5797], [3844, 5799], [3835, 5802], [3826, 5811], [3797, 5811], [3784, 5801], [3772, 5801], [3766, 5805], [3745, 5808], [3730, 5817], [3726, 5830], [3734, 5847], [3730, 5856], [3742, 5867], [3736, 5880], [3736, 5898], [3743, 5913], [3758, 5919], [3772, 5912], [3776, 5907], [3821, 5915], [3907, 5876], [3994, 5821], [4042, 5781], [4057, 5787], [4068, 5780], [4084, 5755], [4096, 5747], [4131, 5746], [4141, 5742], [4165, 5727], [4178, 5712]]]]}}, {"type": "Feature", "id": "SB.6633", "properties": {"hc-group": "admin1", "hc-middle-x": 0.52, "hc-middle-y": 0.58, "hc-key": "sb-6633", "hc-a2": "CT", "labelrank": "6", "hasc": "SB.CT", "alt-name": null, "woe-id": "-2344839", "subregion": null, "fips": "BP06", "postal-code": null, "name": "Capital Territory (Honiara)", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "160.019", "woe-name": null, "latitude": "-9.446580000000001", "woe-label": null, "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[2636, 6555], [2653, 6554], [2694, 6556], [2708, 6564], [2720, 6557], [2715, 6541], [2715, 6532], [2703, 6527], [2696, 6535], [2671, 6532], [2647, 6541], [2636, 6555]]]}}, {"type": "Feature", "id": "SB.GC", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.54, "hc-key": "sb-gc", "hc-a2": "GC", "labelrank": "6", "hasc": "SB.GC", "alt-name": null, "woe-id": "2344839", "subregion": null, "fips": "BP06", "postal-code": "GC", "name": "Guadalcanal", "country": "Solomon Islands", "type-en": "Province", "region": null, "longitude": "160.209", "woe-name": "Guadalcanal", "latitude": "-9.616949999999999", "woe-label": "Guadalcanal, SB, Solomon Islands", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[2636, 6555], [2647, 6541], [2671, 6532], [2696, 6535], [2703, 6527], [2715, 6532], [2715, 6541], [2720, 6557], [2708, 6564], [2721, 6571], [2766, 6556], [2784, 6555], [2806, 6569], [2814, 6571], [2831, 6570], [2858, 6562], [2873, 6560], [2886, 6569], [2895, 6571], [2906, 6565], [2968, 6565], [2984, 6571], [2994, 6561], [3011, 6554], [3027, 6531], [3060, 6519], [3073, 6504], [3096, 6469], [3126, 6473], [3164, 6456], [3198, 6430], [3219, 6407], [3228, 6376], [3230, 6342], [3234, 6335], [3256, 6339], [3266, 6337], [3291, 6322], [3319, 6314], [3325, 6310], [3329, 6280], [3342, 6272], [3342, 6245], [3344, 6237], [3358, 6220], [3357, 6195], [3335, 6182], [3274, 6169], [3242, 6159], [3225, 6150], [3210, 6139], [3151, 6154], [3099, 6178], [3081, 6182], [3048, 6176], [3037, 6179], [3017, 6204], [3000, 6204], [2989, 6222], [2946, 6240], [2854, 6244], [2785, 6234], [2749, 6234], [2726, 6236], [2710, 6234], [2704, 6237], [2669, 6244], [2633, 6249], [2605, 6264], [2592, 6268], [2577, 6265], [2563, 6260], [2550, 6259], [2533, 6271], [2496, 6310], [2459, 6330], [2459, 6347], [2445, 6356], [2439, 6378], [2420, 6383], [2417, 6388], [2420, 6404], [2412, 6422], [2402, 6439], [2378, 6468], [2360, 6483], [2358, 6491], [2376, 6502], [2381, 6511], [2379, 6519], [2364, 6544], [2364, 6554], [2370, 6575], [2364, 6604], [2352, 6615], [2364, 6629], [2367, 6657], [2372, 6669], [2397, 6678], [2448, 6708], [2476, 6698], [2505, 6680], [2549, 6640], [2582, 6616], [2599, 6583], [2621, 6561], [2636, 6555]]]}}]}