{"title": "Lithuania", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:3346"}}, "hc-transform": {"default": {"crs": "+proj=tmerc +lat_0=0 +lon_0=24 +k=0.9998 +x_0=500000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs", "scale": 0.0018751655983, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 304648.437215, "yoffset": 6256518.95231}}, "features": [{"type": "Feature", "id": "LT.KP", "properties": {"hc-group": "admin1", "hc-middle-x": 0.43, "hc-middle-y": 0.56, "hc-key": "lt-kp", "hc-a2": "KP", "labelrank": "7", "hasc": "LT.KP", "alt-name": "Klaip?da|<PERSON><PERSON>", "woe-id": "55848085", "subregion": null, "fips": "LH58", "postal-code": "KP", "name": "<PERSON><PERSON><PERSON><PERSON>", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "21.3879", "woe-name": "<PERSON><PERSON><PERSON><PERSON>", "latitude": "55.6254", "woe-label": "Klaipeda County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[-881, 6189], [-999, 6226], [-850, 6460], [-765, 6648], [-679, 6908], [-632, 7280], [-645, 7346], [-631, 7547], [-643, 7636], [-587, 7559], [-567, 7415], [-575, 7133], [-606, 7000], [-611, 6879], [-677, 6725], [-645, 6659], [-709, 6567], [-784, 6365], [-879, 6248], [-881, 6189]]], [[[792, 7192], [769, 7114], [672, 7076], [706, 7003], [715, 6896], [750, 6841], [791, 6657], [828, 6635], [878, 6665], [933, 6623], [1066, 6568], [1082, 6521], [1053, 6506], [1036, 6417], [821, 6183], [806, 6114], [885, 6092], [1059, 6009], [1086, 5971], [1133, 5834], [1123, 5746], [1147, 5727], [1217, 5778], [1265, 5785], [1335, 5737], [1493, 5717], [1526, 5660], [1473, 5542], [1467, 5451], [1433, 5456], [1230, 5408], [1148, 5331], [1056, 5345], [1016, 5381], [997, 5524], [927, 5538], [861, 5495], [734, 5553], [637, 5635], [510, 5667], [448, 5739], [321, 5845], [247, 5885], [60, 5897], [-5, 5954], [-112, 6156], [-167, 6215], [-213, 6210], [-370, 6089], [-372, 6204], [-333, 6212], [-323, 6271], [-374, 6373], [-374, 6480], [-393, 6484], [-504, 6386], [-513, 6411], [-431, 6580], [-384, 6638], [-381, 6717], [-418, 6971], [-465, 7060], [-460, 7183], [-521, 7369], [-546, 7520], [-616, 7655], [-660, 7709], [-679, 7853], [-696, 8127], [-690, 8270], [-660, 8379], [-648, 8656], [-653, 8770], [-583, 8784], [-477, 8780], [-403, 8798], [-374, 8858], [-322, 9050], [-274, 9119], [-138, 9240], [-81, 9267], [29, 9276], [288, 9461], [355, 9493], [515, 9494], [1027, 9717], [1020, 9684], [930, 9633], [911, 9595], [963, 9495], [970, 9369], [1048, 9200], [1049, 9155], [998, 9138], [883, 8908], [849, 8884], [736, 8883], [667, 8829], [597, 8828], [488, 8737], [474, 8671], [407, 8549], [297, 8466], [304, 8407], [292, 8276], [305, 8197], [347, 8103], [297, 8038], [198, 7962], [153, 7901], [154, 7863], [231, 7853], [317, 7893], [318, 7836], [359, 7770], [340, 7713], [361, 7688], [565, 7711], [582, 7689], [563, 7582], [596, 7519], [645, 7361], [697, 7280], [792, 7192]]]]}}, {"type": "Feature", "id": "LT.AS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.41, "hc-middle-y": 0.5, "hc-key": "lt-as", "hc-a2": "AS", "labelrank": "7", "hasc": "LT.AS", "alt-name": "Alytus|Olita|Alitus", "woe-id": "55848082", "subregion": null, "fips": "LH56", "postal-code": "AS", "name": "Alytaus", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "24.167", "woe-name": "Alytaus", "latitude": "54.2544", "woe-label": "Alytus County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[6295, 2430], [6239, 2395], [6234, 2316], [6167, 2260], [6243, 2025], [6241, 1936], [6183, 1861], [6060, 1840], [5996, 1870], [5971, 1932], [5905, 1902], [5783, 1877], [5687, 1820], [5470, 1623], [5401, 1587], [5170, 1609], [5116, 1733], [5063, 1798], [5002, 1819], [4940, 1807], [4822, 1743], [4766, 1728], [4643, 1726], [4526, 1782], [4351, 1693], [4273, 1719], [4210, 1683], [4091, 1666], [3996, 1626], [3934, 1636], [3763, 1741], [3697, 1758], [3707, 1795], [3655, 1866], [3648, 1930], [3720, 2026], [3720, 2099], [3679, 2319], [3633, 2456], [3602, 2503], [3507, 2564], [3455, 2659], [3420, 2689], [3446, 2853], [3444, 2940], [3487, 3057], [3548, 3121], [3671, 3159], [3637, 3214], [3669, 3252], [3813, 3306], [3816, 3428], [3844, 3469], [4003, 3595], [4041, 3594], [4122, 3498], [4220, 3472], [4240, 3452], [4415, 3458], [4450, 3444], [4564, 3527], [4675, 3569], [4655, 3681], [4692, 3719], [4772, 3732], [4860, 3702], [5170, 3760], [5227, 3711], [5288, 3690], [5455, 3713], [5474, 3639], [5456, 3560], [5498, 3501], [5489, 3378], [5527, 3354], [5694, 3333], [5788, 3354], [5889, 3411], [6023, 3377], [6049, 3272], [6138, 3302], [6221, 3290], [6308, 3356], [6462, 3415], [6556, 3424], [6555, 3241], [6470, 3211], [6437, 3175], [6407, 2983], [6340, 2893], [6138, 2688], [6161, 2615], [6225, 2582], [6295, 2430]]]}}, {"type": "Feature", "id": "LT.KS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.62, "hc-middle-y": 0.44, "hc-key": "lt-ks", "hc-a2": "KS", "labelrank": "7", "hasc": "LT.KS", "alt-name": "Kaunas|Kowno|Kovno", "woe-id": "55848084", "subregion": null, "fips": "LH57", "postal-code": "KS", "name": "<PERSON><PERSON>", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "23.9879", "woe-name": "<PERSON><PERSON>", "latitude": "55.0673", "woe-label": "Kaunas County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[5455, 3713], [5288, 3690], [5227, 3711], [5170, 3760], [4860, 3702], [4772, 3732], [4692, 3719], [4655, 3681], [4675, 3569], [4564, 3527], [4450, 3444], [4415, 3458], [4240, 3452], [4220, 3472], [4258, 3537], [4259, 3594], [4227, 3690], [4145, 3718], [4049, 3815], [4086, 3894], [4072, 3934], [3973, 3998], [3986, 4027], [4070, 4064], [4083, 4125], [4048, 4275], [4053, 4340], [4009, 4462], [3960, 4496], [4021, 4575], [3978, 4783], [3888, 4866], [3826, 4886], [3736, 4853], [3696, 4987], [3659, 5030], [3568, 5055], [3574, 5110], [3642, 5148], [3753, 5145], [3817, 5235], [3890, 5260], [3880, 5318], [3845, 5350], [3721, 5354], [3626, 5433], [3588, 5445], [3609, 5545], [3671, 5661], [3666, 5785], [3712, 5885], [3676, 5931], [3629, 5939], [3524, 5916], [3433, 5986], [3243, 5975], [3198, 6001], [3182, 6062], [3119, 6094], [3042, 6068], [2982, 6077], [2948, 6052], [2848, 6043], [2825, 6076], [2857, 6149], [2699, 6284], [2654, 6203], [2590, 6152], [2564, 6209], [2478, 6301], [2475, 6448], [2437, 6481], [2373, 6472], [2335, 6508], [2325, 6600], [2230, 6663], [2190, 6744], [2248, 6792], [2355, 6831], [2492, 6849], [2549, 6771], [2596, 6758], [2881, 6866], [2948, 6911], [3017, 7026], [3055, 7054], [3169, 7067], [3242, 7056], [3307, 6947], [3441, 7027], [3579, 6908], [3681, 6865], [3730, 6872], [3738, 6913], [3816, 6957], [3921, 6885], [3888, 6803], [3909, 6751], [4007, 6723], [4101, 6843], [4185, 6903], [4435, 7028], [4499, 7013], [4530, 6887], [4575, 6854], [4662, 6864], [4772, 6749], [4778, 6595], [4830, 6635], [4936, 6647], [5064, 6713], [5141, 6633], [5218, 6632], [5235, 6665], [5322, 6618], [5444, 6460], [5402, 6314], [5396, 6212], [5445, 6116], [5460, 6002], [5498, 5954], [5597, 5900], [5549, 5850], [5568, 5774], [5650, 5700], [5674, 5579], [5782, 5569], [5807, 5484], [5863, 5457], [5885, 5357], [5757, 5250], [5700, 5150], [5837, 5066], [5937, 4972], [6052, 4954], [6119, 4914], [6171, 4840], [6117, 4796], [6061, 4703], [5938, 4633], [5905, 4599], [5856, 4421], [5848, 4321], [5815, 4300], [5725, 4317], [5691, 4267], [5582, 4223], [5563, 4167], [5578, 4037], [5605, 4005], [5575, 3892], [5508, 3897], [5488, 3778], [5455, 3713]]]}}, {"type": "Feature", "id": "LT.MA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.5, "hc-middle-y": 0.49, "hc-key": "lt-ma", "hc-a2": "MA", "labelrank": "7", "hasc": "LT.MA", "alt-name": "Marijampol?|Mariampol", "woe-id": "55848078", "subregion": null, "fips": "LH59", "postal-code": "MA", "name": "<PERSON><PERSON><PERSON><PERSON>", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "23.1751", "woe-name": "<PERSON><PERSON><PERSON><PERSON>", "latitude": "54.6829", "woe-label": "Marijampole County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[3588, 5445], [3626, 5433], [3721, 5354], [3845, 5350], [3880, 5318], [3890, 5260], [3817, 5235], [3753, 5145], [3642, 5148], [3574, 5110], [3568, 5055], [3659, 5030], [3696, 4987], [3736, 4853], [3826, 4886], [3888, 4866], [3978, 4783], [4021, 4575], [3960, 4496], [4009, 4462], [4053, 4340], [4048, 4275], [4083, 4125], [4070, 4064], [3986, 4027], [3973, 3998], [4072, 3934], [4086, 3894], [4049, 3815], [4145, 3718], [4227, 3690], [4259, 3594], [4258, 3537], [4220, 3472], [4122, 3498], [4041, 3594], [4003, 3595], [3844, 3469], [3816, 3428], [3813, 3306], [3669, 3252], [3637, 3214], [3671, 3159], [3548, 3121], [3487, 3057], [3444, 2940], [3446, 2853], [3420, 2689], [3383, 2722], [3231, 2781], [3009, 2943], [2882, 2917], [2848, 2965], [2868, 3066], [2770, 3108], [2740, 3179], [2701, 3205], [2638, 3200], [2525, 3260], [2438, 3245], [2386, 3152], [2350, 3124], [2240, 3328], [2192, 3440], [2183, 3567], [2201, 3818], [2255, 4029], [2243, 4139], [2252, 4194], [2294, 4254], [2291, 4313], [2367, 4356], [2409, 4416], [2497, 4488], [2527, 4601], [2495, 4792], [2458, 4861], [2348, 4915], [2354, 4984], [2287, 5050], [2165, 5058], [2135, 5075], [2091, 5187], [2062, 5334], [2026, 5420], [2079, 5443], [2161, 5395], [2319, 5421], [2490, 5490], [2584, 5501], [2685, 5471], [2735, 5509], [2827, 5540], [3416, 5422], [3588, 5445]]]}}, {"type": "Feature", "id": "LT.PA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.35, "hc-middle-y": 0.44, "hc-key": "lt-pa", "hc-a2": "PA", "labelrank": "7", "hasc": "LT.PA", "alt-name": "Panev??ys|Ponewiesch|Ponewjesh|Poneviezli", "woe-id": "55848086", "subregion": null, "fips": "LH60", "postal-code": "PA", "name": "Panevezio", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "24.9143", "woe-name": "Panevezio", "latitude": "56.0725", "woe-label": "Panevezys County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[5444, 6460], [5322, 6618], [5235, 6665], [5218, 6632], [5141, 6633], [5064, 6713], [4936, 6647], [4830, 6635], [4778, 6595], [4772, 6749], [4662, 6864], [4575, 6854], [4530, 6887], [4499, 7013], [4435, 7028], [4524, 7203], [4676, 7246], [4688, 7290], [4667, 7424], [4729, 7497], [4718, 7653], [4671, 7760], [4588, 7821], [4565, 7915], [4635, 7923], [4659, 8009], [4772, 8077], [4806, 8207], [4803, 8291], [4700, 8373], [4685, 8506], [4697, 8567], [4754, 8560], [4786, 8668], [4844, 8765], [4878, 8898], [4952, 8926], [4969, 9020], [4912, 9056], [4910, 9122], [4936, 9169], [4932, 9255], [4979, 9264], [5196, 9378], [5252, 9388], [5310, 9366], [5425, 9282], [5484, 9267], [5649, 9355], [5827, 9591], [5897, 9648], [6124, 9751], [6182, 9737], [6215, 9839], [6278, 9851], [6311, 9797], [6360, 9662], [6409, 9466], [6448, 9374], [6527, 9277], [6561, 9173], [6615, 9076], [6680, 9030], [7306, 8933], [7429, 8958], [7551, 8911], [7681, 8913], [7731, 8777], [7770, 8722], [7876, 8652], [8072, 8442], [8238, 8337], [8204, 8272], [8204, 8181], [8094, 8048], [8075, 7982], [8027, 7906], [7986, 7759], [7965, 7732], [7880, 7753], [7759, 7585], [7719, 7637], [7522, 7626], [7396, 7558], [7353, 7624], [7275, 7603], [7193, 7717], [7056, 7635], [7031, 7596], [7030, 7517], [6938, 7527], [6730, 7440], [6633, 7434], [6630, 7486], [6537, 7576], [6422, 7583], [6333, 7549], [6250, 7550], [6198, 7504], [6128, 7528], [6057, 7508], [5938, 7525], [5933, 7445], [5991, 7366], [5947, 7321], [5901, 7352], [5840, 7310], [5921, 7235], [5929, 7157], [5877, 7111], [5858, 7053], [5872, 6942], [5836, 6882], [5782, 6852], [5840, 6780], [5832, 6750], [5697, 6766], [5621, 6576], [5555, 6504], [5496, 6501], [5444, 6460]]]}}, {"type": "Feature", "id": "LT.SH", "properties": {"hc-group": "admin1", "hc-middle-x": 0.51, "hc-middle-y": 0.46, "hc-key": "lt-sh", "hc-a2": "SH", "labelrank": "7", "hasc": "LT.SH", "alt-name": "?iauliai|Schaulen|Shavli", "woe-id": "55848079", "subregion": null, "fips": "LH61", "postal-code": "SH", "name": "?iauliai", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "23.2677", "woe-name": "?iauliai", "latitude": "55.9585", "woe-label": "Siauliai County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[4435, 7028], [4185, 6903], [4101, 6843], [4007, 6723], [3909, 6751], [3888, 6803], [3921, 6885], [3816, 6957], [3738, 6913], [3730, 6872], [3681, 6865], [3579, 6908], [3441, 7027], [3307, 6947], [3242, 7056], [3169, 7067], [3055, 7054], [3017, 7026], [2948, 6911], [2881, 6866], [2596, 6758], [2549, 6771], [2492, 6849], [2355, 6831], [2248, 6792], [2190, 6744], [2146, 6768], [2137, 6844], [2177, 6903], [2056, 7072], [1948, 7151], [1922, 7212], [1935, 7288], [1868, 7343], [1858, 7410], [1948, 7487], [1939, 7591], [1955, 7693], [1928, 7777], [1955, 7819], [2068, 7880], [2090, 8015], [2190, 8062], [2218, 8141], [2273, 8205], [2267, 8241], [2316, 8279], [2253, 8351], [2152, 8316], [2105, 8410], [2158, 8469], [2144, 8590], [2165, 8634], [2251, 8651], [2331, 8701], [2290, 8786], [2301, 8852], [2265, 8874], [2170, 8855], [2080, 8812], [2012, 8823], [1932, 8786], [1861, 8813], [1843, 8877], [1805, 8884], [1826, 8938], [1745, 8987], [1774, 9175], [1887, 9315], [1899, 9377], [1876, 9416], [1912, 9488], [2003, 9484], [2080, 9507], [2106, 9589], [2081, 9654], [2127, 9700], [2174, 9673], [2252, 9587], [2310, 9577], [2566, 9678], [2686, 9759], [2750, 9770], [2808, 9736], [2911, 9482], [2992, 9417], [3083, 9437], [3175, 9534], [3193, 9588], [3401, 9635], [3441, 9626], [3747, 9493], [3812, 9488], [3920, 9551], [3978, 9568], [4152, 9569], [4185, 9486], [4240, 9477], [4364, 9505], [4424, 9503], [4645, 9432], [4814, 9299], [4932, 9255], [4936, 9169], [4910, 9122], [4912, 9056], [4969, 9020], [4952, 8926], [4878, 8898], [4844, 8765], [4786, 8668], [4754, 8560], [4697, 8567], [4685, 8506], [4700, 8373], [4803, 8291], [4806, 8207], [4772, 8077], [4659, 8009], [4635, 7923], [4565, 7915], [4588, 7821], [4671, 7760], [4718, 7653], [4729, 7497], [4667, 7424], [4688, 7290], [4676, 7246], [4524, 7203], [4435, 7028]]]}}, {"type": "Feature", "id": "LT.TG", "properties": {"hc-group": "admin1", "hc-middle-x": 0.33, "hc-middle-y": 0.54, "hc-key": "lt-tg", "hc-a2": "TG", "labelrank": "7", "hasc": "LT.TG", "alt-name": "Taurag?|Tauroggen", "woe-id": "55848087", "subregion": null, "fips": "LH62", "postal-code": "TG", "name": "Taurages", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "22.6458", "woe-name": "Taurages", "latitude": "55.2657", "woe-label": "Taurage County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[1858, 7410], [1868, 7343], [1935, 7288], [1922, 7212], [1948, 7151], [2056, 7072], [2177, 6903], [2137, 6844], [2146, 6768], [2190, 6744], [2230, 6663], [2325, 6600], [2335, 6508], [2373, 6472], [2437, 6481], [2475, 6448], [2478, 6301], [2564, 6209], [2590, 6152], [2654, 6203], [2699, 6284], [2857, 6149], [2825, 6076], [2848, 6043], [2948, 6052], [2982, 6077], [3042, 6068], [3119, 6094], [3182, 6062], [3198, 6001], [3243, 5975], [3433, 5986], [3524, 5916], [3629, 5939], [3676, 5931], [3712, 5885], [3666, 5785], [3671, 5661], [3609, 5545], [3588, 5445], [3416, 5422], [2827, 5540], [2735, 5509], [2685, 5471], [2584, 5501], [2490, 5490], [2319, 5421], [2161, 5395], [2079, 5443], [2026, 5420], [1971, 5459], [1775, 5405], [1467, 5451], [1473, 5542], [1526, 5660], [1493, 5717], [1335, 5737], [1265, 5785], [1217, 5778], [1147, 5727], [1123, 5746], [1133, 5834], [1086, 5971], [1059, 6009], [885, 6092], [806, 6114], [821, 6183], [1036, 6417], [1053, 6506], [1082, 6521], [1066, 6568], [933, 6623], [878, 6665], [828, 6635], [791, 6657], [750, 6841], [715, 6896], [706, 7003], [672, 7076], [769, 7114], [792, 7192], [1001, 7216], [1068, 7210], [1125, 7268], [1149, 7376], [1241, 7380], [1337, 7429], [1534, 7447], [1610, 7412], [1718, 7391], [1858, 7410]]]}}, {"type": "Feature", "id": "LT.VI", "properties": {"hc-group": "admin1", "hc-middle-x": 0.3, "hc-middle-y": 0.56, "hc-key": "lt-vi", "hc-a2": "VI", "labelrank": "7", "hasc": "LT.VI", "alt-name": "Vilnius|Wilna|Vilna|Vilnious", "woe-id": "55848083", "subregion": null, "fips": "LH65", "postal-code": "VI", "name": "<PERSON><PERSON><PERSON><PERSON>", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "25.1588", "woe-name": "<PERSON><PERSON><PERSON><PERSON>", "latitude": "54.7338", "woe-label": "Vilnius County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[9743, 6071], [9672, 6049], [9593, 5976], [9553, 5900], [9527, 5715], [9460, 5658], [9261, 5738], [9165, 5678], [8958, 5725], [8875, 5707], [8820, 5613], [8810, 5429], [8746, 5278], [8659, 5146], [8593, 5106], [8368, 5053], [8231, 5066], [8140, 5015], [8121, 4928], [8005, 4807], [7987, 4745], [8002, 4598], [7903, 4514], [7909, 4306], [7883, 4176], [7896, 4071], [7946, 3957], [7950, 3830], [7890, 3742], [7776, 3671], [7729, 3565], [7721, 3350], [7688, 3279], [7604, 3173], [7570, 3102], [7569, 3021], [7637, 2964], [7719, 2972], [7831, 3034], [7886, 3028], [7925, 2913], [8007, 2863], [8065, 2744], [8038, 2698], [8040, 2550], [7983, 2465], [7889, 2469], [7839, 2416], [7778, 2402], [7609, 2426], [7514, 2491], [7498, 2548], [7565, 2591], [7568, 2643], [7525, 2699], [7624, 2732], [7464, 2941], [7393, 2923], [7319, 2809], [7273, 2778], [7118, 2767], [6997, 2807], [6940, 2758], [6904, 2605], [6873, 2537], [6797, 2449], [6715, 2394], [6630, 2379], [6480, 2435], [6392, 2446], [6295, 2430], [6225, 2582], [6161, 2615], [6138, 2688], [6340, 2893], [6407, 2983], [6437, 3175], [6470, 3211], [6555, 3241], [6556, 3424], [6462, 3415], [6308, 3356], [6221, 3290], [6138, 3302], [6049, 3272], [6023, 3377], [5889, 3411], [5788, 3354], [5694, 3333], [5527, 3354], [5489, 3378], [5498, 3501], [5456, 3560], [5474, 3639], [5455, 3713], [5488, 3778], [5508, 3897], [5575, 3892], [5605, 4005], [5578, 4037], [5563, 4167], [5582, 4223], [5691, 4267], [5725, 4317], [5815, 4300], [5848, 4321], [5856, 4421], [5905, 4599], [5938, 4633], [6061, 4703], [6117, 4796], [6171, 4840], [6119, 4914], [6052, 4954], [5937, 4972], [5837, 5066], [5700, 5150], [5757, 5250], [5885, 5357], [5863, 5457], [5807, 5484], [5782, 5569], [5674, 5579], [5650, 5700], [5568, 5774], [5549, 5850], [5597, 5900], [5498, 5954], [5460, 6002], [5445, 6116], [5396, 6212], [5402, 6314], [5444, 6460], [5496, 6501], [5555, 6504], [5621, 6576], [5697, 6766], [5832, 6750], [5840, 6780], [5782, 6852], [5836, 6882], [6092, 6692], [6150, 6594], [6097, 6534], [6124, 6432], [6177, 6404], [6266, 6403], [6315, 6321], [6446, 6364], [6455, 6252], [6490, 6239], [6631, 6253], [6627, 6188], [6746, 6075], [6840, 6086], [6853, 6054], [6802, 5957], [6822, 5905], [6881, 5908], [6971, 5856], [6993, 5823], [6940, 5798], [6845, 5716], [6782, 5690], [6767, 5537], [6785, 5509], [6861, 5491], [6921, 5453], [6950, 5340], [7038, 5339], [7186, 5296], [7489, 5400], [7681, 5450], [7739, 5450], [7767, 5416], [7902, 5410], [7891, 5476], [7947, 5540], [7932, 5592], [7980, 5650], [7864, 5726], [7723, 5885], [7708, 5934], [7728, 6024], [7834, 6126], [7878, 6284], [8028, 6213], [8097, 6254], [8177, 6171], [8231, 6186], [8275, 6131], [8351, 6149], [8403, 6229], [8444, 6257], [8560, 6264], [8635, 6236], [8672, 6192], [8648, 6114], [8684, 6039], [8752, 6069], [8876, 6016], [8970, 5998], [9220, 5994], [9424, 6061], [9542, 6148], [9626, 6178], [9695, 6154], [9743, 6071]]]}}, {"type": "Feature", "id": "LT.UN", "properties": {"hc-group": "admin1", "hc-middle-x": 0.45, "hc-middle-y": 0.51, "hc-key": "lt-un", "hc-a2": "UN", "labelrank": "7", "hasc": "LT.UN", "alt-name": "Utena", "woe-id": "55848081", "subregion": null, "fips": "LH64", "postal-code": "UN", "name": "Utenos", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "25.622", "woe-name": "Utenos", "latitude": "55.5267", "woe-label": "Utena County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[5836, 6882], [5872, 6942], [5858, 7053], [5877, 7111], [5929, 7157], [5921, 7235], [5840, 7310], [5901, 7352], [5947, 7321], [5991, 7366], [5933, 7445], [5938, 7525], [6057, 7508], [6128, 7528], [6198, 7504], [6250, 7550], [6333, 7549], [6422, 7583], [6537, 7576], [6630, 7486], [6633, 7434], [6730, 7440], [6938, 7527], [7030, 7517], [7031, 7596], [7056, 7635], [7193, 7717], [7275, 7603], [7353, 7624], [7396, 7558], [7522, 7626], [7719, 7637], [7759, 7585], [7880, 7753], [7965, 7732], [7986, 7759], [8027, 7906], [8075, 7982], [8094, 8048], [8204, 8181], [8204, 8272], [8238, 8337], [8343, 8272], [8459, 8148], [8644, 7997], [8839, 7659], [8956, 7576], [9214, 7461], [9318, 7437], [9423, 7432], [9453, 7270], [9437, 7112], [9326, 6941], [9321, 6862], [9358, 6796], [9354, 6758], [9291, 6690], [9256, 6532], [9188, 6357], [9227, 6304], [9370, 6266], [9480, 6301], [9788, 6259], [9832, 6228], [9851, 6174], [9833, 6121], [9743, 6071], [9695, 6154], [9626, 6178], [9542, 6148], [9424, 6061], [9220, 5994], [8970, 5998], [8876, 6016], [8752, 6069], [8684, 6039], [8648, 6114], [8672, 6192], [8635, 6236], [8560, 6264], [8444, 6257], [8403, 6229], [8351, 6149], [8275, 6131], [8231, 6186], [8177, 6171], [8097, 6254], [8028, 6213], [7878, 6284], [7834, 6126], [7728, 6024], [7708, 5934], [7723, 5885], [7864, 5726], [7980, 5650], [7932, 5592], [7947, 5540], [7891, 5476], [7902, 5410], [7767, 5416], [7739, 5450], [7681, 5450], [7489, 5400], [7186, 5296], [7038, 5339], [6950, 5340], [6921, 5453], [6861, 5491], [6785, 5509], [6767, 5537], [6782, 5690], [6845, 5716], [6940, 5798], [6993, 5823], [6971, 5856], [6881, 5908], [6822, 5905], [6802, 5957], [6853, 6054], [6840, 6086], [6746, 6075], [6627, 6188], [6631, 6253], [6490, 6239], [6455, 6252], [6446, 6364], [6315, 6321], [6266, 6403], [6177, 6404], [6124, 6432], [6097, 6534], [6150, 6594], [6092, 6692], [5836, 6882]]]}}, {"type": "Feature", "id": "LT.TL", "properties": {"hc-group": "admin1", "hc-middle-x": 0.51, "hc-middle-y": 0.63, "hc-key": "lt-tl", "hc-a2": "TL", "labelrank": "7", "hasc": "LT.TL", "alt-name": "Tel?iai|Telsche|Telschi|Telshe", "woe-id": "55848080", "subregion": null, "fips": "LH63", "postal-code": "TL", "name": "Tel?iai", "country": "Lithuania", "type-en": "County", "region": null, "longitude": "22.1086", "woe-name": "Tel?iai", "latitude": "56.0398", "woe-label": "Telsiai County, LT, Lithuania", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[1858, 7410], [1718, 7391], [1610, 7412], [1534, 7447], [1337, 7429], [1241, 7380], [1149, 7376], [1125, 7268], [1068, 7210], [1001, 7216], [792, 7192], [697, 7280], [645, 7361], [596, 7519], [563, 7582], [582, 7689], [565, 7711], [361, 7688], [340, 7713], [359, 7770], [318, 7836], [317, 7893], [231, 7853], [154, 7863], [153, 7901], [198, 7962], [297, 8038], [347, 8103], [305, 8197], [292, 8276], [304, 8407], [297, 8466], [407, 8549], [474, 8671], [488, 8737], [597, 8828], [667, 8829], [736, 8883], [849, 8884], [883, 8908], [998, 9138], [1049, 9155], [1048, 9200], [970, 9369], [963, 9495], [911, 9595], [930, 9633], [1020, 9684], [1027, 9717], [1261, 9819], [1342, 9811], [1475, 9726], [2008, 9731], [2127, 9700], [2081, 9654], [2106, 9589], [2080, 9507], [2003, 9484], [1912, 9488], [1876, 9416], [1899, 9377], [1887, 9315], [1774, 9175], [1745, 8987], [1826, 8938], [1805, 8884], [1843, 8877], [1861, 8813], [1932, 8786], [2012, 8823], [2080, 8812], [2170, 8855], [2265, 8874], [2301, 8852], [2290, 8786], [2331, 8701], [2251, 8651], [2165, 8634], [2144, 8590], [2158, 8469], [2105, 8410], [2152, 8316], [2253, 8351], [2316, 8279], [2267, 8241], [2273, 8205], [2218, 8141], [2190, 8062], [2090, 8015], [2068, 7880], [1955, 7819], [1928, 7777], [1955, 7693], [1939, 7591], [1948, 7487], [1858, 7410]]]}}]}