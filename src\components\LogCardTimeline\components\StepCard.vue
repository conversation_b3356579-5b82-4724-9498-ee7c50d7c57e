<template>
  <div
    class="step-card"
    :class="`step-card--${status}`"
    :style="calcStyle"
  >
    <div class="step-card__icon">
      <i :class="iconClass" />
    </div>
    <div class="step-card__content">
      <titan-tooltip
        effect="light"
        :content="description"
        :disabled="!description"
        :open-delay="100"
      >
        <span>
          <span>{{ stepName }}</span>
          <i
            v-if="step === 'get_proxy'"
            class="icon fa fa-copy"
            @click="handleCopy"
          />
          <i
            v-if="status === 'fail' || status === 'warn'"
            class="icon fa fa-info-circle"
          />
        </span>
      </titan-tooltip>
    </div>
    <titan-tooltip
      v-if="status !== 'SUCCESS' && errorStack"
      popper-class="errorStack"
      effect="light"
      :content="errorStack"
      :open-delay="100"
    >
      <i
        class="fa fa-exclamation-circle"
        aria-hidden="true"
      />
    </titan-tooltip>
    <div class="step-card__divider" />

    <div class="step-card__time">
      {{ stepTime }}
    </div>
  </div>
</template>

<script>
import { formatTime } from '@/utils/time-utils';

const STATUS_LIST = [
  {
    type: 'SUCCESS',
    icon: 'fa fa-check',
    color: '#0fd2be',
    bgColor: '#edfbfa'
  },
  {
    type: 'FAILURE',
    icon: 'fa fa-close',
    color: '#ff274b',
    bgColor: '#feeeee'
  },
  {
    type: 'CREATE',
    icon: 'fa fa-clock-o',
    color: '#909399',
    bgColor: '#ffffff'
  },
  {
    type: 'PAUSED',
    icon: 'fa fa-exclamation',
    color: '#ffa600',
    bgColor: '#fffaec'
  }
];
export default {
  name: 'StepCard',
  props: {
    step: {
      type: String,
      default: ''
    },
    stepName: {
      type: String,
      default: ''
    },
    time: {
      type: Number,
      default: undefined
    },
    status: {
      type: String,
      default: 'WAIT'
    },
    description: {
      type: String,
      default: ''
    },
    errorStack: {
      type: String,
      default: null
    }
  },
  computed: {
    stepTime() {
      return this.time ? formatTime(this.time, 'fmt:Hs') : '';
    },
    iconClass() {
      const obj = STATUS_LIST.find(o => o.type === this.status) || {};
      return obj['icon'] || 'fa fa-clock-o';
    },
    calcStyle() {
      const obj = STATUS_LIST.find(o => o.type === this.status) || {};
      return {
        '--color': obj['color'] || '#909399',
        '--bgColor': obj['bgColor'] || '#fff'
      };
    }
  },
  methods: {
    handleCopy() {
      this.$emit('copy-proxy');
    }
  }
};
</script>

<style lang="scss" scoped>
.step-card {
  display: flex;
  position: relative;
  height: 38px;
  margin-bottom: 20px;
  &__icon {
    position: absolute;
    left: 0;
    top: -1px;
    width: 24px;
    height: 24px;
    background: var(--color);
    color: #fff;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &__content {
    margin-left: 14px;
    height: 22px;
    padding: 4px 13px;
    box-sizing: border-box;
    border: solid 1px var(--color);
    border-radius: 0px 13px 13px 0px;
    background-color: var(--bgColor);
    color: var(--color);
    font-size: 14px;
    line-height: 14px;
    text-align: center;
    .icon {
      cursor: pointer;
      margin-left: 6px;
    }
  }
  &__divider {
    margin: 10px 6px 10px 6px;
    width: 8px;
    border-top: 1px dashed #606266;
  }
  .fa-exclamation-circle {
    margin: 5px 6px;
    color: #ff274b;
  }
  &__time {
    position: absolute;
    top: 22px;
    left: 14px;
    font-size: 12px;
    color: #909399;
  }
  &:last-of-type {
    .step-card__divider {
      display: none;
    }
  }
}
</style>

<style>
.errorStack {
  max-height: 400px !important;
  overflow-y: scroll !important;
}
</style>
