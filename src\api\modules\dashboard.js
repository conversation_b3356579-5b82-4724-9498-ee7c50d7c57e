import http from '@/api/request';
import URL from '../urls';

export class DashboardAPI {
  /**
   * 获取热点事件列表
   */
  static getEvents(data) {
    return http.post(URL.dashboard.events, data);
  }

  /**
   * 根据热点事件ID获取相应报表数据
   */
  static getEventDetail(id) {
    return http.post(URL.dashboard.eventDetail, { id });
  }

  /**
   * 根据热点事件ID及相应筛选条件获取热点文章列表
   */
  static getEventNews(data) {
    return http.post(URL.dashboard.articles, data);
  }

  /**
   * 获取热点文章详情
   */
  static getEventNewsDetail(data) {
    return http.post(URL.dashboard.articleDetail, data);
  }
}
// 新主页接口
export class NewDashboardAPI {
  /**
     * 获取热点事件列表(首页)
     */
  static getEvents(data) {
    return http.post(URL.new_dashboard.events, data);
  }

  /**
   * 创建热点事件
   */
  static saveHotEvent(data) {
    return http.post(URL.new_dashboard.createEvent, data);
  }

  /**
   * 关注的热点事件-详情
   */
  static getFollowedEventDetail(data) {
    return http.post(URL.new_dashboard.followEventDetail, data);
  }

  /**
   * 热点事件-编辑
   */
  static editEvent(data) {
    return http.post(URL.new_dashboard.editEvent, data);
  }

  /**
   * 热点事件-删除
   */
  static deleteEvent(data) {
    return http.post(URL.new_dashboard.deleteEvent, data);
  }

  /**
   * 热点事件-关注
   */
  static followEvent(data) {
    return http.post(URL.new_dashboard.followEvent, data);
  }

  /**
   * 获取热点事件列表
   */
  static getEventsNew(data) {
    return http.post(URL.new_dashboard.new_events, data);
  }

  /**
   * 获取热门论坛
   */
  static getHotForums(data) {
    return http.post(URL.new_dashboard.hotForum, data);
  }

  /**
   * 获取需要显示的热门论坛
   */
  static getDisplayHotForums(typeSource) {
    return http.post(URL.new_dashboard.displayHotForum(typeSource));
  }

  /**
   * 保存需要显示的热门论坛
   */
  static saveDisplayHotForums(typeSource, data) {
    return http.post(URL.new_dashboard.saveDisplayHotForum(typeSource), data);
  }

  /**
   * 获取热点话题 筛选类型
   */
  static getTopicTypes(data) {
    return http.post(URL.new_dashboard.topicType, data);
  }

  /**
   * 获取热点话题 筛选语言
   */
  static getTopicLanguage() {
    return http.post(URL.new_dashboard.topicLanguage);
  }

  /**
   * 根据热点事件ID获取相应报表数据
   */
  static getEventDetail(data) {
    return http.post(URL.new_dashboard.eventDetail, data);
  }

  /**
   * 根据热点事件ID获取相应报表数据
   */
  static getEventDetailNew(data) {
    return http.post(URL.new_dashboard.new_eventDetail, data);
  }

  /**
   * 根据热点事件ID及相应筛选条件获取热点文章列表(首页)
   */
  static getEventNews(data) {
    return http.post(URL.new_dashboard.articles, data);
  }

  /**
   * 根据热点事件ID及相应筛选条件获取热点文章列表
   */
  static getEventNewsNew(data) {
    return http.post(URL.new_dashboard.new_articles, data);
  }

  /**
   * 获取热点文章详情
   */
  static getEventNewsDetail(data) {
    return http.post(URL.new_dashboard.articleDetail, data);
  }

  /**
   * 地域分布
   */
  static getLocation(data) {
    return http.post(URL.new_dashboard.location, data);
  }

  /**
     * 上传热点事件
     */
  static uploadSource(file) {
    const form = new FormData();
    form.append('file', file);
    return http.post(URL.new_dashboard.uploadSource, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /**
     * 热点事件下载模板
     */
  static HotTemplate(data) {
    return http.post(URL.dashboard.download, data, { responseType: 'blob' });
  }

  /**
     * 更新情绪分析立场
     */
  static updateEmotion(data) {
    return http.post(URL.new_dashboard.updateEmotion, data);
  }

  /*
  * 获取总览地区 语言 话题码表 热点事件 地区 话题码表
  */

  static getSearchCode(data) {
    return http.post(URL.new_dashboard.getSearchCode, data);
  }

  /*
  * 获取平台树
  */
  static getPlatformCode(data) {
    return http.post(URL.new_dashboard.getPlatformCode, data);
  }
}

// 账户报表监控
export class AccountReportAPI {
  /**
   * 获取热点作者
   */
  static getAuthor(data) {
    return http.post(URL.accout_report.author, data);
  }

  /**
   * 获取热点新闻网站作者
   */
  static getNewsAuthor(data) {
    return http.post(URL.accout_report.newsAuthor, data);
  }

  /**
   * 获取 发帖趋势 情绪分析 热词图表数据
   */
  static getChartData(data) {
    return http.post(URL.accout_report.chart, data);
  }

  /**
   * 获取 近30天使用频率和时长
   */
  static getTimeData(data) {
    return http.post(URL.accout_report.time, data);
  }

  /**
   * 获取 相关文章列表
   */
  static getArticle(data) {
    return http.post(URL.accout_report.article, data);
  }

  /**
   * 获取热点文章详情
   */
  static getArticleDetail(data) {
    return http.post(URL.accout_report.articleDetail, data);
  }

  /**
   * 获取主要言论
   */
  static getSpeech(data) {
    return http.post(URL.accout_report.speech, data);
  }

  /**
   * 获取账号详情政治倾向
   */
  static getPoliticalData(data) {
    return http.post(URL.accout_report.politicalData, data);
  }

  /**
   * 获取账号详情关系类型
   */
  static getRelationData(data) {
    return http.post(URL.accout_report.relationData, data);
  }

  /* 获取对应标签群体的映射 */
  static testGroupMap() {
    return http.post(URL.accout_report.testGroupMap);
  }
}

// 知识图谱
export class GraphAPI {
  // 获取 地区、主题、话题、标签
  static getEnum(type) {
    return http.get(URL.knowledge_graph.enum(type));
  }

  /**
   * 查询某类型下深度为 depth 的所有标签
   * @param {String} type 所查询类型
   * @param {Number} depth 深度
   */
  static getTypeEnum(type, depth) {
    return http.get(URL.knowledge_graph.subjectOrTopicEnum(type, depth));
  }

  /** 获取所有的方向顶点*/
  static getAllRegion(query) {
    return http.post(URL.knowledge_graph.getAllRegion, query);
  }

  /** 获取节点信息*/
  static getVertex(query) {
    return http.post(URL.knowledge_graph.getVertex, query);
  }

  /** 获取顶点的相邻点*/
  static getAdjacent(query) {
    return http.post(URL.knowledge_graph.getAdjacent, query);
  }

  /** 下载模板文件*/
  static downloadTemplate(data) {
    return http.post(URL.knowledge_graph.downloadTemplate, data, { responseType: 'blob' });
  }

  /** 上传文件*/
  static uploadTemplate(data) {
    return http.post(URL.knowledge_graph.uploadTemplate, data, { header: { 'Content-Type': 'multipart/form-data;charset=utf-8' } });
  }
}

export class MapAPI {
  /**
   * 获取地图json
   * @param {*} region
   */
  static getMapJSON(region) {
    return http.get(URL.new_dashboard.mapJSON(region));
  }

  /**
   * 获取指定区域的联系人数据
   * @param {array}} area 查询区域列表
   */
  static getMapData(data) {
    return http.post(URL.new_dashboard.mapRankByRegion, data);
  }
}
export class PlatformAPI {
  // 平台聚合
  static getPlatform(params) {
    return http.get(URL.platform.plarformList, { params });
  }
}
