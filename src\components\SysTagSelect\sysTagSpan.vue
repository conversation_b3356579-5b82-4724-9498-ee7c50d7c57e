<template>
  <el-popover
    v-if="m_tags && m_tags.length > 0"
    v-model="visible"
    popper-class="sys-tag-popover"
    :trigger="toolEffectTrigger"
    :placement="placement"
    :disabled="popperDisplay !== 'group'"
  >
    <div
      slot="reference"
      ref="tagContainer"
      class="el-tags-warp"
    >
      <template v-if="isElTag">
        <template v-if="popperDisplay === 'group'">
          <el-tag
            v-for="(item, it) in m_tags"
            :key="'tag_' + it"
            :type="elTagType"
            :class="{ 'editable': couldEdit }"
            :style="msty"
          >
            <span>{{ item.name }}</span>
            <i
              v-if="couldEdit"
              class="el-icon el-icon-close"
              @click.stop="removeTag(item)"
            />
          </el-tag>
        </template>
        <template v-if="popperDisplay === 'every'">
          <el-tooltip
            v-for="(item, it) in m_tags"
            :key="'tag_' + it"
            :content="tagTxt + computeTypeName(item)"
            effect="light"
            placement="top"
          >
            <el-tag
              :type="elTagType"
              :class="{ 'editable': couldEdit }"
              :style="msty"
            >
              <span>{{ item.name }}</span>
              <i
                v-if="couldEdit"
                class="el-icon el-icon-close"
                @click.stop="removeTag(item)"
              />
            </el-tag>
          </el-tooltip>
        </template>
        <template v-if="popperDisplay === 'everyAndHidden'">
          <el-tooltip
            v-for="(item, it) in visibleTags"
            :key="'tag_' + it"
            :content="tagTxt + computeTypeName(item)"
            effect="light"
            placement="top"
          >
            <el-tag
              :type="elTagType"
              :class="{ 'editable': couldEdit }"
              :style="msty"
            >
              <span>{{ item.name }}</span>
              <i
                v-if="couldEdit"
                class="el-icon el-icon-close"
                @click.stop="removeTag(item)"
              />
            </el-tag>
          </el-tooltip>

          <!-- 隐藏的个数 -->
          <el-tooltip
            v-if="m_tags.length - visibleTags.length > 0"
            :content="tagTxt +
              m_tags
                .slice(visibleTags.length)
                .map(item => computeTypeName(item))
                .join('、')
            "
            effect="light"
            placement="top"
          >
            <el-tag
              :type="elTagType"
              class="more"
            >
              <span>+{{ m_tags.length - visibleTags.length }}</span>
            </el-tag>
          </el-tooltip>
        </template>
      </template>
      <p
        v-else
        class="tag-txt"
        v-text="tags_txt"
      />
    </div>
    <div class="pop-warp">
      <p
        v-for="(item, it) in m_tags"
        :key="'p_' + it"
      >
        {{ computeTypeName(item) }}
      </p>
    </div>
  </el-popover>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { tagPrimaryAlias } from '@/assets/libs/enum';
import CountryNameMap from '@/assets/libs/country';
import i18n from '@/lang';

export default {
  name: 'SysTagSpan',
  props: {
    primary: {
      type: String,
      default: '角色培育通用'
    },
    tags: {
      type: Array,
      default: () => []
    },
    isElTag: {
      type: Boolean,
      default: true
    },
    elTagType: {
      type: String,
      default: 'primary'
    },
    toolEffectTrigger: {
      type: String,
      default: 'hover'
    },
    placement: {
      type: String,
      default: 'top'
    },
    // popper 的展示方式  group:整体展示  every:每个展示  everyAndHidden:每个展示且超出宽度隐藏
    popperDisplay: {
      type: String,
      default: 'group'
    },
    couldEdit: {
      type: Boolean,
      default: false
    },
    // 提示信息是否展示标签类型
    tooltipShowType: {
      type: Boolean,
      default: true
    },
    // 所展示的tag盒子最大的宽度
    containerWidth: {
      type: Number,
      default: 0
    },
    translateName: {
      type: String,
      default: ''
    },
    tagTxt: {
      type: String,
      default: i18n.t('accountCenter["标签："]')
    },
    // 每个标签的最大宽度
    tagMaxWidth: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return { visible: false, visibleTags: [] };
  },
  computed: {
    ...mapGetters(['canImport']),
    ...mapState('tag', {
      tagDict(state) {
        const primary = tagPrimaryAlias[this.primary];
        return state[primary].tagDict;
      }
    }),
    m_tags() {
      if (_.isEmpty(this.tags)) return [];
      return this.tags
        .filter(a => {
          const item = this.tagDict[a];
          // 过滤掉没有值的
          if (!item) return false;
          // 根据权限过滤掉 ##SPARE 开头的标签
          if (this.canImport) {
            // 有权限直接返回
            return true;
          } else if (!item.name.startsWith('##SPARE')) {
            // 没有权限过滤掉 ##SPARE 开头的标签
            return true;
          }
          return false;
        })
        .map(a => ({
          id: a,
          name: this.tagDict[a]['name'],
          type_name: this.tagDict[a]['type_name']
        }));
    },
    tags_txt() {
      if (_.isEmpty(this.m_tags)) return '';
      const list = this.m_tags.map(a => a.name);
      return list.join('、');
    },
    msty() {
      if (!this.tagMaxWidth) return undefined;
      return { maxWidth: _.isNumber(this.tagMaxWidth) ? this.tagMaxWidth + 'px' : this.tagMaxWidth };
    }
  },
  watch: {
    m_tags: {
      deep: true,
      handler() {
        if (this.popperDisplay === 'everyAndHidden') {
          this.updateTags();
        }
      }
    }
  },
  mounted() {
    if (this.popperDisplay === 'everyAndHidden') {
      this.updateTags();
    }
  },
  methods: {
    removeTag(tag) {
      this.$emit('remove-tag', tag);
    },
    updateTags() {
      this.visibleTags = [];
      if (_.isEmpty(this.m_tags)) return;

      if (!this.containerWidth || this.containerWidth <= 50) {
        this.visibleTags = this.m_tags;
        return;
      }

      const containerWidth = this.containerWidth - 50; // 减去+num的宽度
      let tagVisibleWidth = 0;
      const visibleTags = [];
      for (let i = 0; i < this.m_tags.length; i++) {
        const tag = this.m_tags[i];
        const span = document.createElement('span');
        span.style.display = 'inline-block';
        span.style.padding = '0 8px'; // 标签有宽度
        if (this.tagMaxWidth) {
          span.style.maxWidth = _.isNumber(this.tagMaxWidth) ? this.tagMaxWidth + 'px' : this.tagMaxWidth;
        }
        span.className = 'tag';
        span.textContent = tag.name;
        document.body.appendChild(span);
        const width = span.offsetWidth;
        tagVisibleWidth += width;
        if (i === 0) {
          // 第一个标签必须被添加
          visibleTags.push(tag);
        } else if (tagVisibleWidth <= containerWidth) {
          visibleTags.push(tag);
        } else {
          // 超出容器最大宽度直接移除最后添加的span然后结束循环
          document.body.removeChild(span);
          break;
        }
        document.body.removeChild(span);
      }
      this.visibleTags = visibleTags;
    },
    computeTypeName(tag) {
      if (!this.tooltipShowType) return tag.name;
      if (!this.translateName) return tag.type_name ? (tag.type_name + '/' + tag.name) : tag.name;
      if (this.translateName === 'AreaCode') return (CountryNameMap[tag.type_name] || tag.type_name) + '/' + tag.name;
    }
  }
};
</script>

<style lang="scss">
@at-root body {
  .el-popover.el-popper.sys-tag-popover {
    padding: 8px;
    font-size: 12px;
  }
}
</style>

<style lang="scss" scoped>
.el-tag {
  margin-right: 10px;
  &:not(.more) {
    span {
      display: inline-block;
      @include utils-ellipsis(1);
      vertical-align: middle;
      max-width: 100%;
    }
    &.editable{
      span{
        max-width: calc(100% - 14px);
      }
    }
  }
  i {
    color: #409eff;
    cursor: pointer;
  }
}
.tag-txt {
  font-size: 12px;
}
</style>
