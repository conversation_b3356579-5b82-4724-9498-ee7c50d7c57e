<template>
  <div class="dan-tag-prompt">
    <titan-form
      :column="column"
      :model="form"
      size="mini"
      inline
    />

    <ul class="list">
      <li
        v-for="(item, it) in result"
        :key="it"
      >
        <span class="tag">{{ item.tagName }}</span>{{ translate('标签下的') }}<span class="type">{{ item.categoryName }}</span><span class="platform">{{ item.platformName }}</span>{{ translate('平台被封率达到') }}<span class="ban-rate">{{ item.rate }}</span>
      </li>
    </ul>

    <div class="footer">
      <el-button
        type="primary"
        size="mini"
        :loading="loading"
        @click="handleRead"
      >
        {{ translate('已读') }}
      </el-button>
      <el-button
        size="mini"
        :loading="loading"
        @click="go2detail"
      >
        {{ translate('查看详情') }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { PlatformMap } from '@/assets/libs/platform';
import { Message } from '@/api/modules/messages';
import { clearCloneForm } from '@/utils';
import { app } from '@/main';
import i18n from '@/lang';

export default {
  name: 'DanTagPrompt',
  props: {
    messageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        platform: '',
        category: ''
      },
      result: [],
      loading: false
    };
  },
  computed: {
    source() {
      return this.messageInfo.content ? JSON.parse(this.messageInfo.content) : {};
    },
    platformList() {
      if (_.isEmpty(this.source)) return [];
      const arr = [];
      Object.keys(this.source).forEach(key => {
        const oneList = this.source[key];
        oneList.forEach(item => {
          arr.push({
            label: PlatformMap[item.platform],
            value: item.platform
          });
        });
      });

      return _.unionBy(arr, 'value');
    },
    column() {
      return [
        {
          prop: 'platform',
          render: (h, form) => (
            <titan-select
              v-model={form.platform}
              filterable
              placeholder={i18n.t('store.平台')}
              clearable
              config={{
                dataSource: this.platformList,
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        },
        {
          prop: 'category',
          render: (h, form) => (
            <titan-select
              v-model={form.category}
              filterable
              clearable
              placeholder={i18n.t('store.资源类型')}
              config={{
                dataSource: this.categoryList,
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        }
      ];
    },
    categoryList() {
      return [
        {
          label: i18n.t('store.手机卡'),
          value: 'phoneMessages'
        },
        {
          label: i18n.t('store.邮箱'),
          value: 'emailMessages'
        }
      ];
    },
    list() {
      if (_.isEmpty(this.source)) return [];
      const source = _.cloneDeep(this.source);
      const list = [];
      Object.keys(source).forEach(key => {
        const oneList = source[key];
        oneList.forEach(item => {
          item.platformName = PlatformMap[item.platform];
          item.rate = parseInt(item.banRate * 100, 10) + '%';
          const category = this.categoryList.find(a => a.value === key);
          item.category = key;
          item.categoryName = category.label;
          list.push(item);
        });
      });
      return list;
    }
  },
  watch: {
    form: {
      handler(val) {
        this.result = this.list.filter(item => {
          let is_category = true;
          let is_platform = true;
          if (val.category) {
            is_category = item.category === val.category;
          }
          if (val.platform) {
            is_platform = item.platform === val.platform;
          }

          return is_category && is_platform;
        });
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.loading = false;
  },
  methods: {
    async setRead() {
      this.loading = true;
      // 标记已读
      Message.markRead(this.messageInfo.id);
      // 更新 NavBar 里面的未读消息提示
      const res = await Message.getTypes();
      app.$store.commit('info/UPDATE_UNREAD', res.data ? clearCloneForm(res.data) : {});
    },
    async handleRead() {
      await this.setRead();
      this.$parent.close();
    },
    async go2detail() {
      await this.setRead();
      this.$parent.close();
      app.$router.push({ path: '/info/center', query: { type: 'MISSION_ASSIGN' } });
    },
    translate(txt) {
      return i18n.t('store.' + txt);
    }
  }
};
</script>

<style scoped lang="scss">
.dan-tag-prompt{
  .list{
    max-height: 200px;
    overflow-y: auto;
    padding-right: 10px;
    li + li{
      margin-top: 10px;
    }
    .tag{
      color: #3569e7;
    }
    .platform{
      color: #3569e7;
    }
    .ban-rate{
      color: #F56C6C;
    }
  }
  .footer{
    text-align: right;
  }
}
</style>
