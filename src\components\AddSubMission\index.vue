<template>
  <titan-dialog
    :title="dialogTitle"
    v-bind="$attrs"
    width="36%"
    @open="handleOpen"
    @opened="init"
    v-on="$listeners"
  >
    <titan-form
      ref="subMissionForm"
      :model="form"
      label-position="top"
      :column="column"
      label-width="140px"
      @submit.native.prevent
    />
    <template
      v-if="!isQuick"
      #footer
    >
      <el-button
        v-if="!isEdit"
        size="small"
        type="primary"
        round
        :loading="loading.decompose"
        @click="() => handleConfirm(true)"
      >
        {{ $t('components.创建并策划') }}
      </el-button>
      <el-button
        size="small"
        type="primary"
        round
        :loading="loading.add"
        @click="() => handleConfirm()"
      >
        {{ isEdit ? $t('components.确定') : $t('components.创建') }}
      </el-button>
      <el-button
        size="small"
        type="info"
        round
        @click="handleClose"
      >
        {{ $t('components.关闭') }}
      </el-button>
    </template>
    <template
      v-else
      #footer
    >
      <el-button
        v-if="!isEdit"
        size="small"
        type="primary"
        :loading="loading.decompose"
        round
        @click="() => handleConfirmQuickTask(true)"
      >
        {{ $t('components.创建任务') }}
      </el-button>
    </template>
  </titan-dialog>
</template>

<script>
import { MissionAPI } from '@/api/modules/mission';
import {
  isEmpty,
  checkSpecialKey,
  roleNameRule,
  calcMediaSize,
  calcMediaDuration
} from '@/utils';
import { mapState, mapGetters } from 'vuex';
import { getPlatformByDepartment } from '@/utils/fetchApiData';
import { formatTime } from '@/utils/time-utils';
import SocialActions from '@/views/mission/utils/social-actions';
import { PostConfigMap } from '@/assets/libs/platform';

export default {
  name: 'AddSubMission',
  inheritAttrs: false,
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    isUser: {
      // 判断是否是人物还
      type: Boolean,
      default: false
    },
    isArticle: {
      // 判断是否是文章
      type: Boolean,
      default: false
    },
    isComment: {
      // 判断是否是评论
      type: Boolean,
      default: false
    },
    isImage: {
      // 判断是否是图片
      type: Boolean,
      default: false
    },
    isVideo: {
      // 判断是否是视频
      type: Boolean,
      default: false
    },
    isSysAccount: {
      // 判断是否是自足创建账号账号
      type: Boolean,
      default: false
    },
    isImportAccount: {
      // 判断是否是自足创建账号账号
      type: Boolean,
      default: false
    },
    isCrops: {
      // 判断是否是团体
      type: Boolean,
      default: false
    },
    task: {
      type: Object,
      default: () => ({})
    },
    isQuick: {
      type: Boolean,
      default: false
    },
    platformRadio: {
      //
      type: String,
      default: ''
    },
    quickTaskInfo: {
      type: Object,
      default: () => ({})
    },
    isMaterialLib: {
      type: Boolean,
      default: false
    },
    // 简洁模式
    isSimpleMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataCode: 0, // 接口状态码
      loading: {
        add: false,
        decompose: false,
        groupList: false
      },
      form: {
        name: '',
        platform: '',
        action: '',
        groupId: ''
      },
      platformRadios: [],
      groupList: []
    };
  },
  computed: {
    ...mapState('platform', ['platformLoaded']),
    ...mapGetters(['isManageByPermission']),

    ...mapState({ user: state => state.user }),
    isManualWatchAll() {
      return this.isManageByPermission(this.$qx['mission_center_manual_watch']);
    },
    dialogTitle() {
      if (this.isSimpleMode) {
        return this.isEdit
          ? this.$t('mission["修改任务"]')
          : this.$t('mission["创建任务"]');
      } else {
        return this.isEdit
          ? this.$t('components["修改子任务"]')
          : this.isQuick
            ? this.$t('components["创建快速任务"]')
            : this.$t('components["创建子任务"]');
      }
    },
    videoConfig() {
      return PostConfigMap[this.form.platform].video || {};
    },
    imageConfig() {
      return PostConfigMap[this.form.platform].image || {};
    },
    snsActions() {
      return SocialActions.filter(
        item => item.enable && item.enable.includes(this.form.platform)
      );
    },
    showGroup() {
      let show = false;
      if (this.isSimpleMode) {
        show = false;
      } else if (!this.isQuick) {
        show = true;
      }
      return show;
    },
    column() {
      if (this.isEdit) {
        return this.editColumn;
      }
      return this.addColumn;
    },
    addColumn() {
      return [
        {
          label: this.$t('components["任务名称"]'),
          prop: 'name',
          rules: [
            {
              required: true,
              message: this.$t('components["请输入任务名称"]'),
              trigger: 'blur'
            },
            {
              validator: (rule, value, callback) => {
                if (!checkSpecialKey(value)) {
                  // eslint-disable-next-line no-useless-escape
                  callback(new Error(this.$t('components["不允许输入！@#￥%……&*+=~·、？?“”：《》<>();!$^/`^特殊字符"]')));
                } else if (this.dataCode === 50021) {
                  callback(new Error(this.$t('components["任务名称重复请修改"]')));
                } else {
                  callback();
                }
              },
              trigger: 'change'
            }
          ],
          render: (h, form) => (
            <titan-input
              maxLength={50}
              trim
              v-model={form.name}
              placeholder={this.$t('components["请输入任务名称"]')}
            />
          )
        },
        {
          prop: 'groupId',
          label: this.$t('components["任务分组"]'),
          show: () => this.showGroup,
          rules: [
            {
              required: true,
              message: this.$t('components["请选择任务分组"]'),
              trigger: 'change'
            }
          ],
          render: (h, form) => (
            <titan-select
              v-model={form.groupId}
              v-loading={this.loading.groupList}
              config={{
                dataSource: this.groupList,
                itemValue: 'id',
                itemLabel: 'name'
              }}
            ></titan-select>
          )
        },
        {
          prop: 'platform',
          label: this.$t('components["平台选择"]'),
          rules: [
            {
              required: true,
              message: this.$t('components["请选择平台"]'),
              trigger: 'change'
            }
          ],
          //  disabled={this.isUser || this.isSysAccount || this.isImportAccount || this.isCrops}
          render: (h, form) => {
            let optionList = [];
            if (
              this.isUser ||
              this.isSysAccount ||
              this.isImportAccount ||
              this.isCrops
            ) {
              optionList = this.platformRadios.map(it => (
                <el-radio
                  disabled={it.disabled || it.value !== this.platformRadio}
                  label={it.value}
                >
                  {it.label}
                </el-radio>
              ));
            } else {
              optionList = this.platformRadios.map(it => (
                <el-radio disabled={it.disabled} label={it.value}>
                  {it.label}
                </el-radio>
              ));
            }
            return (
              <div>
                <titan-select
                  v-model={form.platform}
                  filterable
                  clearable
                  config={{
                    dataSource: this.platformRadios,
                    itemValue: 'value',
                    itemLabel: 'label',
                    itemDisable: it => ((
                        this.isUser ||
                        this.isSysAccount ||
                        this.isImportAccount ||
                        this.isCrops
                      ) ? it.disabled || it.value !== this.platformRadio
                        : it.disabled)
                  }}
                />
                <el-radio-group v-model={form.platform} class='platform-item' style='margin-top: 15px'>
                  {optionList}
                </el-radio-group>
              </div>
            );
          }
        },
        {
          prop: 'action',
          label: this.$t('components["行为选择"]'),
          show: () => this.isQuick,
          rules: [
            {
              required: true,
              message: this.$t('components["请选择行为"]'),
              trigger: 'change'
            }
          ],
          render: (h, form) => {
            let radioArr = [];
            if (this.isUser && this.snsActions.length > 0) {
              radioArr = this.snsActions.filter(it => [
                'followSchema',
                'privateMessageSchema',
                'reportSchema',
                'addFriendSchema'
              ].includes(it.schema)
              );
            } else if (this.isArticle && this.snsActions.length > 0) {
              if (this.platformRadio !== form.platform) {
                radioArr = this.snsActions.filter(it => (this.isMaterialLib ? ['postSchema'] : ['postSchema', 'commentSchema']).includes(it.schema)
                );
              } else {
                radioArr = this.snsActions.filter(it => (
                  this.isMaterialLib ? [
                    'postSchema',
                    'repostSchema',
                    'likeSchema',
                    'reportSchema'
                  ] : [
                    'postSchema',
                    'commentSchema',
                    'repostSchema',
                    'likeSchema',
                    'reportSchema'
                  ]
                ).includes(it.schema)
                );
              }
            } else if (this.isComment && this.snsActions.length > 0) {
              radioArr = this.snsActions.filter(it => ['postSchema', 'commentSchema'].includes(it.schema)
              );
            } else if (this.isImage && this.snsActions.length > 0) {
              if (this.platformRadio !== form.platform) {
                if (
                  Object.prototype.hasOwnProperty.call(
                    PostConfigMap[this.form.platform] || {},
                    'image'
                  )
                ) {
                  // 判断改平台是否有图片发帖
                  radioArr = this.snsActions.filter(it => ['postSchema'].includes(it.schema)
                  );
                }
              } else {
                radioArr = this.snsActions.filter(it => [
                  'postSchema',
                  'commentSchema',
                  'likeSchema',
                  'repostSchema'
                ].includes(it.schema)
                );
              }
            } else if (this.isVideo && this.snsActions.length > 0) {
              if (this.platformRadio !== form.platform) {
                if (
                  Object.prototype.hasOwnProperty.call(
                    PostConfigMap[this.form.platform] || {},
                    'video'
                  )
                ) {
                  // 判断改平台是否有视频发帖
                  radioArr = this.snsActions.filter(it => ['postSchema'].includes(it.schema)
                  );
                }
              } else {
                radioArr = this.snsActions.filter(it => [
                  'postSchema',
                  'commentSchema',
                  'likeSchema',
                  'repostSchema'
                ].includes(it.schema)
                );
              }
            } else if (this.isImportAccount && this.snsActions.length > 0) {
              radioArr = this.snsActions.filter(it => [
                'commentSchema',
                'dislikeSchema',
                'followSchema',
                'repostSchema',
                'likeSchema',
                'privateMessageSchema',
                'reportSchema'
              ].includes(it.schema)
              );
            } else {
              radioArr = this.snsActions;
            }

            if (!this.form.platform) {
              return <span>{this.$t('components["请选择平台"]')}</span>;
            } else if (radioArr.length === 0) {
              return <span>{this.$t('components["暂无可用行为"]')}</span>;
            }

            const optionList = radioArr.map(it => (
              <el-radio disabled={it.disabled} label={it.schema}>
                {it.label}
              </el-radio>
            ));
            return (
              <el-radio-group v-model={form.action} class='platform-item'>
                {optionList}
              </el-radio-group>
            );
          }
        }
      ];
    },
    editColumn() {
      return [
        {
          prop: 'name',
          label: this.$t('components["任务名称"]'),
          rules: [
            { required: true, message: this.$t('components["请输入任务名称"]') },
            {
              type: 'string',
              maxLength: 50,
              message: this.$t('components["任务名称最长不超过50个字符"]')
            },
            {
              validator: (rule, value, callback) => {
                if (!checkSpecialKey(value)) {
                  // eslint-disable-next-line no-useless-escape
                  callback(new Error(this.$t('components["不允许输入！@#￥%……&*+=~·、？?“”：《》<>();!$^/`^特殊字符"]')));
                } else if (this.dataCode === 50021) {
                  this.dataCode = 0;
                  callback(new Error(this.$t('components["任务名称重复请修改"]')));
                } else {
                  callback();
                }
              },
              trigger: 'change'
            }
          ],
          render: (h, form) => (
            <titan-input
              maxLength={50}
              v-model={form.name}
              style='width: 100%;'
            />
          )
        }
      ];
    }
  },
  watch: {
    platformLoaded() {
      this.getPlatformList();
    },
    'form.platform': {
      handler(val) {
        this.form.action = '';
      }
    }
  },
  async mounted() {
    await this.getPlatformList();
    this.init();
  },
  methods: {
    handleOpen() {
      this.getGroupList(); // 获取任务分组的下拉
    },

    // 初始化数据
    init() {
      this.$nextTick(() => {
        if (this.$refs.subMissionForm) {
          this.$refs.subMissionForm.resetFields();
        }
        if (this.isEdit) {
          this.form = { name: this.task.name };
          return;
        }

        /* 判断是否为快速创建的任务 */
        if (this.isQuick) {
          /* 判断单选平台的时候可以赋值 */
          if (
            this.platformRadios.filter(it => !it.disabled).length > 0 &&
            this.platformRadios.find(it => it.value === this.platformRadio)
          ) {
            this.form.platform = this.platformRadio;
          }
          const timestamp = formatTime('', 'fmt:Ys');
          this.form.name = this.$t('components["任务"]') + timestamp;
          return;
        }

        this.form = {
          name: '',
          platform: '',
          groupId: this.$route.name !== 'MISSION-DETAIL' || !this.$route.query.activeId
            ? ''
            : this.$route.query.activeId === 'all_task'
              ? ''
              : this.$route.query.activeId
        };

        // 简洁模式
        if (this.isSimpleMode) {
          const timestamp = formatTime('', 'fmt:Ym');
          this.form.name = this.isEdit ? this.task.name : this.$t('mission["任务"]') + timestamp;
          this.form.platform = this.task.platform;
        }
      });
    },

    async getPlatformList() {
      this.platformRadios = (await getPlatformByDepartment.call(this, 'delivery')) || [];
    },

    async getGroupList() {
      try {
        this.loading.groupList = true;
        const { data } = await MissionAPI.getTaskGroupList({
          groupType: 'TASK',
          parentId: this.$route.params.id,
          page: 1,
          size: 999,
          canSeeAll: this.isManualWatchAll,
          isSelf: true
        });
        this.groupList = !data.records ? [] : data.records.filter(item => item.id !== 'all_task');
      } catch (err) {
        console.error(err);
      } finally {
        this.loading.groupList = false;
      }
    },

    /**
     * 确认新建/编辑
     * @param {Boolean} toDecompose 是否跳转策划页
     */
    handleConfirm(toDecompose = false) {
      if (this.isEdit) {
        this.handleEdit();
        return;
      }
      this.handleAdd(toDecompose);
    },

    // 新建子任务-生成默认参数
    _generateAddQuery() {
      let isValid = false;
      this.$refs.subMissionForm.validate(valid => {
        if (valid) {
          isValid = true;
        } else {
          isValid = false;
        }
      });

      const formData = {
        ...this.form,
        parentId: this.task.id,
        type: 'MANUAL',
        priority: this.task.priority || 'MEDIUM',
        taskDefinitionExt: { taskSourceType: this.isSimpleMode ? 'MANUAL_SIMPLE' : 'MANUAL_ENTIRE' }
      };
      return isValid ? formData : null;
    },

    /**
     * 新建子任务
     */
    async handleAdd(toDecompose) {
      const query = this._generateAddQuery();

      if (!isEmpty(query)) {
        // 设置loading状态
        if (toDecompose) {
          this.loading.decompose = true;
        } else {
          this.loading.add = true;
        }

        try {
          const res = await MissionAPI.createChildTask(query);
          this.dataCode = res.code || 0;
          if (this.dataCode === 50021) {
            this.$refs.subMissionForm.validate();
            return;
          }
          this.$message.success(this.$t('components["子任务创建成功"]'));
          if (toDecompose) {
            if (this.isSimpleMode) {
              this.$router.push({
                name: 'MISSION-DECOMPOSE',
                query: {
                  parentId: res.data,
                  id: res.data,
                  from: this.$route.query.from
                }
              });
            } else {
              this.$router.push({
                name: 'MISSION-DECOMPOSE',
                query: {
                  parentId: this.task.id,
                  id: res.data
                }
              });
            }
          } else if (this.$route.name === 'MISSION-DETAIL') {
            this.$emit('refresh');
          } else if (this.isSimpleMode) {
            this.$emit('refresh');
          } else {
            this.$router.push({
              name: 'MISSION-DETAIL',
              params: { id: this.task.id }
            });
          }
        } catch (error) {
          console.error(error);
        } finally {
          Object.keys(this.loading).forEach(key => {
            this.loading[key] = false;
          });
          if (this.dataCode !== 50021) {
            this.$emit('update:visible', false);
          }
          this.dataCode = 0;
        }
      }
    },

    /**
     * 编辑子任务
     */
    async handleEdit() {
      await this.$refs.subMissionForm.validate();
      try {
        const res = await MissionAPI.updateTask({
          taskId: this.task.id,
          name: this.form.name
        });
        if (res.code === 50021) {
          this.dataCode = res.code;
          this.$refs.subMissionForm.validate();
          return;
        }
        this.$message.success(this.$t('components["任务信息修改成功"]'));
        this.$emit('refresh');
        this.$emit('update:visible', false);
      } catch (err) {
        this.$message.error(this.$t('components["任务信息修改失败"]'));
        console.error(err);
      } finally {
        this.dataCode = 0;
      }
    },

    /* 创建快速任务 */
    handleConfirmQuickTask() {
      this.$refs.subMissionForm.validate(async valid => {
        if (valid) {
          const timestamp = formatTime('', 'fmt:Ym');
          const query = {
            // 创建快速任务 默认参数
            name: this.$t('components["快速"]') + this.form.name,
            type: 'MANUAL',
            priority: 'MEDIUM',
            notes: `${this.$t('components["快速父任务"]')}${timestamp}${this.$t('components["详情"]')}`,
            assignees: [{ userId: this.user.id }],
            children: [
              {
                name: this.form.name,
                platform: this.form.platform,
                type: 'MANUAL',
                priority: 'MEDIUM'
              }
            ]
          };
          const { data, code } = await MissionAPI.createQuickChildTask(query);
          if (code === 0) {
            if (this._generateQuickTaskInfo()) {
              this.$store.commit(
                'autoTaskInfo/SET_AUTO_TASK_INFO',
                this._generateQuickTaskInfo()
              );

              this.$router.push({
                name: 'MISSION-DECOMPOSE',
                query: {
                  parentId: data.parentId,
                  id: data.id
                }
              });
            }
          } else {
            this.$message.error(this.$t('components["创建快速任务失败"]'));
          }
        }
      });
    },

    /* 生成存在vuex 中的数据 */
    _generateQuickTaskInfo() {
      const action = this.snsActions.filter(
        item => item.schema === this.form.action
      );
      let quickTaskInfo = {};
      const taskInfo = _.cloneDeep(this.quickTaskInfo);
      let dataType = '';
      if (this.isUser) {
        quickTaskInfo = {
          target_user: [taskInfo?.user_url || taskInfo?.url],
          target_user_temp: [taskInfo?.user_url || taskInfo?.url]
        };
        dataType = 'user';
      }
      if (this.isArticle) {
        quickTaskInfo = {
          content_list: [taskInfo?.content],
          content: taskInfo?.source_content || taskInfo?.content,
          title: taskInfo?.source_title || taskInfo?.title
        };
        dataType = 'article';
        if (this.platformRadio === this.form.platform) {
          quickTaskInfo.target_url = [taskInfo?.url];
          delete quickTaskInfo.content_list;
        }
      }
      if (this.isComment) {
        quickTaskInfo = {
          content_list: [taskInfo?.content],
          content: taskInfo?.source_content || taskInfo?.content,
          title: taskInfo?.source_title || taskInfo?.title
        };
        dataType = 'comment';
      }
      if (this.isImage) {
        if (taskInfo.file_size > this.imageConfig.size) {
          // 防止错误弹框重叠
          this.$message.error(
            `${this.$t('components["图片的大小超出"]')}${calcMediaSize(this.imageConfig.size)}${this.$t('components["限制"]')}`
          );
          return false;
        }
        quickTaskInfo = {
          image: [`${taskInfo?.s3_url}?${taskInfo?.format}`],
          title: taskInfo?.title
        };
        if (this.platformRadio === this.form.platform) {
          quickTaskInfo.target_url = [taskInfo?.url];
        }
        dataType = 'image';
      }
      if (this.isVideo) {
        if (taskInfo.file_size > this.videoConfig.size) {
          // 防止错误弹框重叠
          this.$message.error(
            `${this.$t('components["视频的大小超出"]')}${calcMediaSize(this.videoConfig.size)}${this.$t('components["限制"]')}`
          );
          return false;
        }

        // 如果视频有时长的限制
        if (this.videoConfig.duration) {
          if (taskInfo.duration * 1000 > this.videoConfig.duration) {
            this.$message.error(
              `${this.$t('components["上传视频的时长不得超过"]')}${calcMediaDuration(
                this.videoConfig.duration
              )}`
            );
            return false;
          }
        }
        quickTaskInfo = {
          video: `${taskInfo?.s3_url}?${taskInfo?.format}`,
          title: taskInfo?.title
        };
        if (this.platformRadio === this.form.platform) {
          quickTaskInfo.target_url = [taskInfo?.url];
        }
        dataType = 'video';
      }
      if (this.isSysAccount) {
        quickTaskInfo = {
          account_filter: {
            can_task: 1,
            // account_list: taskInfo.account, // 后端说已弃用该字段
            id_list: taskInfo.account.map(item => item.id),
            account_info_list: taskInfo.account.map(item => ({
              id: item.id,
              account: item.account,
              status: item.status,
              platform: item.platform,
              account_photo: item.account_photo,
              extra_info: item.extra_info
            }))
          }
        };
        dataType = 'SysAccount';
      }
      if (this.isImportAccount) {
        quickTaskInfo = {
          import_account_filter: {
            count: 1,
            tag: [],
            account_list: taskInfo.account
          }
        };
        dataType = 'ImportAccount';
      }
      if (this.isCrops) {
        const group_lists = [{ group_id: taskInfo.data.id, group_level: taskInfo.data.level }];
        quickTaskInfo = {
          corps_filter: {
            count: taskInfo.count,
            group_id: taskInfo.nodeIds[taskInfo.nodeIds.length - 1],
            group_list: [taskInfo.nodeIds],
            group_lists: group_lists,
            group_level: taskInfo.nodeIds.length - 1,
            platform: taskInfo.platform,
            can_task: 1
          }
        };
        dataType = 'crops';
      }
      const query = {
        quickTaskInfo,
        action,
        dataType
      };
      return query;
    },

    /**
     * 关闭对话框
     */
    async handleClose() {
      let needConfirm = false;

      if (this.isEdit) {
        needConfirm = this.form.name !== this.task.name;
      } else {
        needConfirm = Object.keys(this.form).some(
          key => !isEmpty(this.form[key])
        );
      }

      if (!needConfirm) {
        this.$emit('update:visible', false);
        return;
      } else {
        await this.$confirm(this.$t('components["将不保留现有的操作信息，确认关闭？"]'), this.$t('components["提示"]'), {
          confirmButtonText: this.$t('components["确定"]'),
          cancelButtonText: this.$t('components["取消"]'),
          type: 'warning'
        }).then(() => {
          this.$emit('update:visible', false);
        });
      }
    }
  }
};
</script>

<style scoped>
::v-deep .el-radio {
  margin-bottom: 10px;
  width: 185px;
}
::v-deep .el-dialog__footer {
  text-align: center;
}
</style>
