import request from '@/api/request';
import URL from '../urls';
export class CaptchaAPI {
  /**
   * 上传普通二维码图片
   */
  static uploadImageCaptcha(data) {
    const formData = new FormData();
    formData.append('img', data);
    return request.post(URL.systemTest.captchaAnalysis.normal, formData, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
   * 上传普通二维码图片
   */
  static uploadImageCaptchaLocal(data) {
    const formData = new FormData();
    formData.append('img', data);
    return request.post(URL.systemTest.captchaAnalysis.localNormal, formData, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
   * 获取在线验证码
   * @param {string} captcha_id 随机UUID
   */
  static getNetCaptcha(captchaId) {
    return request.get(URL.systemTest.captchaAnalysis.netCaptcha, { params: { captchaId } });
  }

  /**
   * 分析Google 密钥
   */
  static getGoogleKeys() {
    return request.post(
      URL.systemTest.captchaAnalysis.googleCaptcha,
      {
        'page_url': URL.systemTest.captchaAnalysis.google,
        'site_key': '6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-'
      }
    );
  }

  /**
   * 获取 geetest challenge参数
   */
  static getGeetestParams(params) {
    return request.get(URL.systemTest.captchaAnalysis.geetest, { params });
  }

  /**
   * 分析Geetest 密钥
   * @param {String} challenge
   */
  static getGeetestKeys(challenge) {
    return request.post(
      URL.systemTest.captchaAnalysis.geetestCaptcha,
      {
        'gt': '4faf9dd37eb683586977947ce87a866a',
        'challenge': challenge,
        'api_server': '',
        'page_url': URL.systemTest.captchaAnalysis.geetestUrl
      }
    );
  }

  /**
   * 获取分析结果
   */
  static getAnalysisResult(taskId) {
    return request.post(`${URL.systemTest.captchaAnalysis.result}?task_id=${taskId}`);
  }
}

export class IntelligentAPI {
  /**
   * 发送消息
   */
  static sendMessage(data) {
    return request.post(URL.systemTest.intelligent.chat, data, { headers: { 'userId': localStorage.getItem('id') } });
  }

  /**
   * 发送消息
   */
  static sendMessageV2(data) {
    return request({
      url: URL.systemTest.intelligent.chatV2,
      method: 'POST',
      timeout: 300000,
      data: data
    });
  }

  // /* 发送消息中文 */
  // static sendMessageCN(params) {
  //   return request.post(URL.systemTest.intelligent.chatCN, params);
  // }

  // /**
  //  * 重置会话英文
  //  */
  // static resetEn() {
  //   return request.get(URL.systemTest.intelligent.resetEn);
  // }

  // /**
  //  * 重置会话中文
  //  */
  // static resetZh() {
  //   return request.get(URL.systemTest.intelligent.resetZh);
  // }
  /**
   * 重置会话
   */
  static reset() {
    return request.post(URL.systemTest.intelligent.reset);
  }
}

export class OnlineTranslation {
  /* 获取原文语言 */
  static getLang(data) {
    return request.post(URL.systemTest.onlineTranslation.getLang, data);
  }

  /* 翻译 */
  static translate(data) {
    return request.post(URL.systemTest.onlineTranslation.translate, data);
  }
}

export class ArticleTagMatch {
  /* 文章预审 */
  static tagsMatching(data) {
    return request({
      url: URL.systemTest.tagsMatching,
      method: 'POST',
      timeout: 300000,
      data: data
    });
  }
}
export class ArticleCleanAPI {
  static clean(data) {
    return request({
      url: URL.systemTest.articleClean.commit,
      method: 'POST',
      timeout: 300000,
      data: data
    });
  }

  /* AI润色 */
  static aiWashText(data) {
    return request.post(URL.systemTest.articleClean.aiWashText, data);
  }

  /* AI润色获取任务 */
  static queryTask(params) {
    return request.get(URL.systemTest.articleClean.queryTask, { params });
  }
}

