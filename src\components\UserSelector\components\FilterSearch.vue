<template>
  <div class="filter-search-container">
    <titan-form
      :model="filter"
      :column="column"
      inline
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import sysTagCascader from '@/components/SysTagSelect/sysTagCascader';
import CrossTagSelect from '@/components/SysTagSelect/crossTagSelect';
import {
  AccountStatusOptions,
  ActivityLevelOptions,
  exstatus,
  FilterTypeOptions,
  PlatformIdxMap,
  PlatformMap,
  SortOptions
} from '@/assets/libs/enum';
import { AccountEnumsAPI } from '@/api/modules/enum';
import { MissionAPI } from '@/api/modules/mission';
import { CharacterAPI, SysAccountAPI } from '@/api/modules/character';
import { dayjs } from '@/plugins/dayjs';
const defaultSearchParams = {
  activity_level: '',
  platforms: [],
  source: [],
  tag_id: [], // 培育标签
  simple_tag_id: [], // 备注
  area_query: {},
  status: [],
  breed_type: '',
  keyword: '',
  sort: 'desc_time',
  locked: null,
  spare_account: null,
  web_regs: [],
  code_platform: '',
  redistributed_only: false,
  non_redistributed_only: false,
  date: null,
  accountDistribution: null,
  createDate: null,
  can_task: true, // 默认使用推荐账号
  exported: false // 未出库
};

export default {
  name: 'FilterSearch',
  props: {
    searchParams: {
      type: Object,
      default: () => ({}),
      info: '默认的查询条件参数'
    },
    hideItems: {
      type: Array,
      default: () => [],
      info: '隐藏哪些查询条件'
    },
    checkAll: {
      type: Boolean,
      default: false
    },
    checkIndeterminate: {
      type: Boolean,
      default: false
    },
    selectedList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      filterType: 'key_word',
      filter: {},
      platformList: [], // 可筛选平台
      allDeviceType: [], // 所有设备类型树结构
      codeList: [] // 注册接码来源列表
    };
  },
  computed: {
    ...mapGetters('codeDict', ['accountBreedTypeList']),
    ...mapGetters(['canImport', 'isManageByPermission']),
    choose_all: {
      set(val) {
        this.$emit('update:checkAll', val);
      },
      get() {
        return this.checkAll;
      }
    },
    column() {
      return [
        {
          show: () => !this.hideItems.includes('keyword'),
          render: (h, form) => (
              <titan-input
                class='select-input'
                v-model={form.keyword}
                placeholder={
                  this.filterType === 'key_word'
                    ? this.$t('components[" 请输入关键词"]')
                    : this.$t('components[" 请输入搜索内容"]')
                }
                clearable
                maxLength={50}
              >
                <titan-select
                  v-model={this.filterType}
                  slot='prepend'
                  style='width: 100px;'
                  onChange={this.handleCleanKeyword}
                  config={{
                    dataSource: FilterTypeOptions,
                    itemLabel: 'label',
                    itemValue: 'value'
                  }}
                />
              </titan-input>
            )
        },
        {
          prop: 'tag_id',
          show: () => !this.hideItems.includes('tag_id'),
          render: (h, form) => (
            <sysTagCascader
              tagIds={form.tag_id}
              disabled={Object.keys(this.searchParams).includes('tag_id')}
              v-on:updateTagIds={val => {
                form.tag_id = val;
              }}
            />
          )
        },
        {
          prop: 'simple_tag_id',
          show: () => !this.hideItems.includes('simple_tag_id'),
          render: (h, form) => (
            <CrossTagSelect
              tagIds={form.simple_tag_id}
              disabled={Object.keys(this.searchParams).includes('simple_tag_id')}
              collapseTags={true}
              visibleItem='自主创建账号'
              placeholder={this.$t('components["请选择备注"]')}
              v-on:updateTagIds={val => {
                form.simple_tag_id = val;
              }}
            />
          )
        },
        {
          prop: 'area_query',
          show: () => !this.hideItems.includes('area_query'),
          render: (h, form) => (
            <cascader-area
              placeholder={this.$t('components[" 请选择归属地"]')}
              disabled={Object.keys(this.searchParams).includes('area_query')}
              type='account'
              value={form.area_query}
              v-on:change={val => {
                this.$set(form, 'area_query', val);
              }} />
          )
        },
        {
          prop: 'activity_level',
          show: () => !this.hideItems.includes('activity_level'),
          render: (h, form) => (
            <titan-select
              v-model={form.activity_level}
              disabled={Object.keys(this.searchParams).includes('activity_level')}
              placeholder={this.$t('components[" 请选择账号活跃度"]')}
              clearable
              config={{
                dataSource: ActivityLevelOptions,
                itemLabel: 'label',
                itemValue: 'value'
              }}
            />
          )
        },
        {
          prop: 'status',
          show: () => !this.hideItems.includes('status'),
          render: (h, form) => (
            <titan-select
              v-model={form.status}
              placeholder={this.$t('components[" 请选择账号状态"]')}
              multiple
              collapse-tags
              disabled={Object.keys(this.searchParams).includes('status')}
              clearable
              config={{
                dataSource: AccountStatusOptions,
                itemLabel: 'label',
                itemValue: 'value'
              }}
            />
          )
        },
        {
          prop: 'breed_type',
          show: () => !this.hideItems.includes('breed_type'),
          render: (h, form) => {
            const optionList = this.accountBreedTypeList.map(item => (
              <el-option label={item.label} value={item.value} />
            ));
            return (
              <titan-select
                v-model={form.breed_type}
                placeholder={this.$t('components[" 请选择账号属性"]')}
                disabled={Object.keys(this.searchParams).includes('breed_type')}
                clearable
              >
                {optionList}
              </titan-select>
            );
          }
        },
        {
          prop: 'platforms',
          show: () => !this.hideItems.includes('platforms'),
          render: (h, form) => (
            <titan-select
              v-model={form.platforms}
              disabled={Object.keys(this.searchParams).includes('platforms')}
              filterable
              multiple
              collapse-tags
              placeholder={this.$t('components["请选择平台"]')}
              clearable
              config={{
                dataSource: this.platformList,
                itemLabel: data => (data === 'all' ? this.$t('components[" 全部"]') : PlatformMap[data] || data)
              }}
            />
          )
        },
        {
          prop: 'sort',
          show: () => !this.hideItems.includes('sort'),
          render: (h, form) => (
            <titan-select
              v-model={form.sort}
              disabled={Object.keys(this.searchParams).includes('sort')}
              clearable
              placeholder={this.$t('components[" 请选择排序"]')}
              config={{
                dataSource: SortOptions,
                itemLabel: 'label',
                itemValue: 'value'
              }}
            />
          )
        },
        {
          prop: 'locked',
          show: () => !this.hideItems.includes('locked'),
          render: (h, form) => (
            <titan-select
              v-model={form.locked}
              disabled={Object.keys(this.searchParams).includes('locked')}
              clearable
              placeholder={this.$t('components[" 账号是否锁定"]')}
              config={{
                dataSource: [
                  { label: this.$t('components[" 已锁定"]'), value: 1 },
                  { label: this.$t('components[" 未锁定"]'), value: 0 }
                ],
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        },
        {
          prop: 'exported',
          show: () => !this.hideItems.includes('exported'),
          render: (h, form) => (
            <titan-select
              v-model={form.exported}
              clearable
              disabled={Object.keys(this.searchParams).includes('exported')}
              placeholder={this.$t('accountCenter["请选择出库状态"]')}
              config={{
                dataSource: exstatus,
                itemLabel: 'label',
                itemValue: 'value'
              }}
            />
          )
        },
        {
          prop: 'web_regs',
          show: () => !this.hideItems.includes('web_regs'),
          render: (h, form) => {
            const config = {
              clearable: true,
              'show-all-levels': false,
              'collapse-tags': true,
              'popper-class': 'tag-cascader',
              options: this.allDeviceType,
              props: {
                expandTrigger: 'click',
                multiple: true,
                // checkStrictly: true,
                value: 'code',
                label: 'name'
              }
            };
            return (
              <el-cascader
                placeholder={this.$t('components[" 请选择设备类型"]')}
                v-model={form.web_regs}
                disabled={Object.keys(this.searchParams).includes('web_regs')}
                {...{ props: config }}
              />
            );
          }
        },
        {
          prop: 'code_platform',
          show: () => !this.hideItems.includes('code_platform'),
          render: (h, form) => (
            <titan-select
              v-model={form.code_platform}
              disabled={Object.keys(this.searchParams).includes('code_platform')}
              clearable
              placeholder={this.$t('components[" 请选择注册接码来源"]')}
              config={{
                dataSource: this.codeList,
                itemShowTooltip: true,
                itemLabel: 'name',
                itemValue: 'type'
              }}
            />
          )
        },
        {
          prop: 'source',
          show: form => this.canImport && !this.hideItems.includes('source'),
          render: (h, form) => (
            <titan-select
              v-model={form.source}
              placeholder={this.$t('components[" 请选择来源*"]')}
              collapse-tags
              multiple
              clearable
              config={{
                itemShowTooltip: true,
                // onceRequest: true,
                method: SysAccountAPI.getSource,
                scrollable: false
              }}
            />
          )
        },
        {
          prop: 'spare_account',
          show: form => this.canImport && !this.hideItems.includes('spare_account'),
          render: (h, form) => (
            <titan-select
              v-model={form.spare_account}
              placeholder={this.$t('components[" 查看备用库*"]')}
              clearable
              config={{
                dataSource: [
                  { label: '*************', value: 0 },
                  { label: '**************', value: 1 },
                  { label: '************', value: 2 },
                  { label: '**************:3280', value: 3 },
                  { label: '*************', value: 4 }
                ],
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        },
        {
          prop: 'accountDistribution',
          render: (h, form) => (
            <titan-select
              v-model={form.accountDistribution}
              placeholder={this.$t('components[" 请选择分配状态"]')}
              clearable
              disabled={Object.keys(this.searchParams).includes('accountDistribution')}
              config={{
                dataSource: [
                  { label: this.$t('components[" 全部"]'), value: 0 },
                  { label: this.$t('components[" 已分配"]'), value: 1 },
                  { label: this.$t('components[" 未分配"]'), value: 2 },
                  { label: this.$t('components[" 已接收"]'), value: 3 }
                ],
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        },
        {
          prop: 'copy',
          show: () => this.isManageByPermission(this.$qx['mission_center_copytask_manage']) && !this.hideItems.includes('copy'),
          render: (h, form) => (
            <titan-select
              v-model={form.copy}
              placeholder={this.$t('components[" 请选择克隆状态"]')}
              clearable
              config={{
                dataSource: [
                  { label: this.$t('components[" 全部"]'), value: '' },
                  { label: this.$t('components["cs_324"]'), value: true },
                  { label: this.$t('components[" 非克隆"]'), value: false }
                ],
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        },
        {
          prop: 'last_active_code',
          show: () => !this.hideItems.includes('last_active_code'),
          render: (h, form) => (
            <titan-select
              v-model={form.last_active_code}
              placeholder={this.$t('components[" 请选择培育时间"]')}
              clearable
              config={{
                dataSource: [
                  { label: this.$t('components[" 近24H培育"]'), value: 1 },
                  { label: this.$t('components[" 近48H培育"]'), value: 2 },
                  { label: this.$t('components[" 超过48H未培育"]'), value: 3 }
                ],
                itemValue: 'value',
                itemLabel: 'label'
              }}
            />
          )
        },
        {
          prop: 'createDate',
          show: () => !this.hideItems.includes('createDate'),
          render: (h, form, root) => {
            const pickerOptions = {
              disabledDate(time) {
                return (
                  time.getTime() > Date.now() ||
                  time.getTime() < new Date('1970-01-01')
                );
              }
            };
            return (
              <el-date-picker
                v-model={form.createDate}
                type='daterange'
                value-format='timestamp'
                picker-options={pickerOptions}
                range-separator={this.$t('components[" 至"]')}
                start-placeholder={this.$t('components[" 开始注册日期"]')}
                end-placeholder={this.$t('components[" 结束注册日期"]')}
              />
            );
          }
        },
        {
          prop: 'temp_can_task',
          render: (h, form) => (
            <el-checkbox
              v-model={form.temp_can_task}
              true-label={1}
              false-label={2}
            >
            {this.$t('components[" 仅显示推荐账号"]')}
            </el-checkbox>
          )
        },
        {
          render: (h, form) => {
            const selectNum = this.selectedNum ? <span class='page-selected-num'>{ this.selectedNum }</span> : '';
            return (
              <div>
                <el-checkbox
                  v-model={this.choose_all}
                  indeterminate={this.checkIndeterminate}
                  onChange={val => this.$emit('checked', val)}
                  style='margin-right: 8px'
                >
                {this.$t('components[" 全选"]')}
                </el-checkbox>
                { selectNum }
              </div>
            );
          }
        }
      ];
    },
    selectedNum() {
      return this.selectedList?.length;
    }
  },
  watch: {
    searchParams: {
      handler() {
        this.resetFilter();
      },
      immediate: true,
      deep: true
    },
    filter: {
      deep: true,
      handler() {
        this.debounceWrapper();
      }
    }
  },
  created() {
    this.getPlatformList();
    this.getAllDeviceType();
    this.getCodeList();
    this.resetFilter();
  },
  methods: {
    debounceWrapper: _.debounce(function() {
      this.$emit('update', this.generateQuery());
    }, 400),
    resetFilter() {
      this.filter = {
        ...defaultSearchParams,
        ...this.searchParams
      };

      // 给使用推荐账号赋值
      this.$set(this.filter, 'temp_can_task', this.filter.can_task ? 1 : 2);
    },

    /* 生成查询参数*/
    generateQuery() {
      const query = _.cloneDeep(this.filter);
      query.web_regs = query.web_regs.map(item => item.at(-1)).flat(Infinity);
      switch (this.filterType) {
        case 'phone':
          // 单独输入 - 不做处理，输入 xx-xx 时替换掉第一个
          query.phone =
            query.keyword === '-'
              ? query.keyword
              : query.keyword.replace('-', '');
          break;
        case 'key_word':
          break;
        default:
          query[this.filterType] = query.keyword;
      }
      query.filterType = this.filterType;
      // if (!this.filter.accountDistribution) {
      //   this.filter.redistributed_only = false;
      //   this.filter.non_redistributed_only = false;
      // } else if (this.filter.accountDistribution === 1) {
      //   this.filter.redistributed_only = true;
      //   this.filter.non_redistributed_only = false;
      // } else if (this.filter.accountDistribution === 3) {
      //   this.filter.non_redistributed_only = false;
      //   this.filter.redistributed_only = false;
      // } else {
      //   this.filter.redistributed_only = false;
      //   this.filter.non_redistributed_only = true;
      // }
      if (query.sort) {
        if (query.sort === 'desc_fans') {
          query['fans'] = -1;
          delete query['reg_time'];
          delete query['rec_time'];
        } else if (query.sort === 'asc_fans') {
          query['fans'] = 1;
          delete query['reg_time'];
          delete query['rec_time'];
        } else if (query.sort === 'desc_time') {
          query['rec_time'] = -1;
          delete query['reg_time'];
          delete query['fans'];
        } else if (query.sort === 'asc_time') {
          query['rec_time'] = 1;
          delete query['reg_time'];
          delete query['fans'];
        } else if (query.sort === 'desc_reg_time') {
          query['reg_time'] = -1;
          delete query['fans'];
          delete query['rec_time'];
        } else if (query.sort === 'asc_reg_time') {
          query['reg_time'] = 1;
          delete query['fans'];
          delete query['rec_time'];
        }
        delete query.sort;
      }
      if (this.filterType !== 'key_word') delete query.keyword;

      if (query.date) {
        query.date = {
          begin_date: dayjs(query.date).format('YYYY-MM-DD'),
          end_date: dayjs(query.date).format('YYYY-MM-DD')
        };
      }
      if (query.createDate) {
        query.date = {
          begin_date: dayjs(query.createDate[0]).format('YYYY-MM-DD'),
          end_date: dayjs(query.createDate[1]).format('YYYY-MM-DD')
        };
        delete query.createDate;
      }

      if (query.area_query) {
        Object.keys(query.area_query).forEach(key => {
          query[key] = query.area_query[key];
        });
        delete query.area_query;
      }

      if (query.copy === 'total') delete query['copy'];

      query.can_task = query.temp_can_task === 1 ? true : undefined;
      delete query.temp_can_task; // 删除多余的参数
      return query;
    },

    /* 获取平台列表 */
    async getPlatformList() {
      const { data } = await AccountEnumsAPI.availabelPlatform();
      this.platformList = ['all', ...data]
        .filter(it => !['odnoklassniki', 'tumblr', 'douban'].includes(it))
        .sort((a, b) => PlatformIdxMap[a] - PlatformIdxMap[b]);
    },

    /* 获取所有的注册设备树结构 */
    async getAllDeviceType() {
      const resp = await MissionAPI.getAllDeviceType({ allDevices: true });
      this.allDeviceType = resp.data.map(item => {
        if (item.name === '浏览器') {
          item.code = [0, 6];
          item.children = null;
        }
        return item;
      });
    },

    /* 获取注册接码来源下拉列表*/
    async getCodeList() {
      const { data } = await CharacterAPI.getCvsListByPlatform({});
      this.codeList = data;
    },

    // 清空关键字
    handleCleanKeyword() {
      this.filter = {
        ...this.filter,
        keyword: '',
        account: '',
        email: '',
        phone: ''
      };
    }
  }
};
</script>

<style scoped lang="scss">
.filter-search-container{
  .titan-form.el-form--inline{
    display: flex;
    flex-wrap: wrap;
    ::v-deep .el-form-item{

      &:first-child{
        .titan-form-item__render{
          width: 250px;
        }
      }
      &:nth-child(8){
        .titan-form-item__render{
          width: 250px;
        }
      }
    }
  }

}

</style>
