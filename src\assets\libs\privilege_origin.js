/**
 * 权限映射
 */
const PrivilegeMap = {
  // 主页
  home: 'home',
  check_home: 'check_home',
  monitor_account_report: 'monitor_account_report',
  group_portrait: 'group_portrait',
  knowledge_graph: 'knowledge_graph',
  system_running_status: 'system_running_status',
  // 任务中心
  mission_center: 'mission_center',
  daily_mission_manage: 'daily_mission_manage',
  war_mission_manage: 'war_mission_manage',
  // 运营
  home_operation: 'home_operation',
  create_public_page: 'create_public_page',
  post_manage: 'post_manage',
  promotion_setting: 'promotion_setting',
  post_audit: 'post_audit',
  // 虚拟角色
  virtual_character: 'virtual_character',
  account_import: 'account_import',
  account_label_manage: 'account_label_manage',
  // 系统资源
  system_resources: 'system_resources',
  check_resource_report: 'check_resource_report',
  content_cultivate_manage: 'content_cultivate_manage',
  edit_article: 'edit_article',
  ip_import_manage: 'ip_import_manage',
  cloud_manage: 'cloud_manage',
  phone_manage: 'phone_manage',
  email_manage: 'email_manage',
  // 网络资源
  net_resource: 'net_resource',
  check_video_library: 'check_video_library',
  check_voice_library: 'check_voice_library',
  check_picture_library: 'check_picture_library',
  net_account_resource: 'net_account_resource',
  view_collect_report: 'view_collect_report',
  // 监控账号
  monitor_center: 'monitor_center',
  monitor_account_manage: 'monitor_account_manage',
  // 信息中心
  notice: 'notice',
  check_notice: 'check_notice',
  // 模板中心
  template_center: 'template_center',
  template_manage: 'template_manage',
  // 系统设置
  system_setting: 'system_setting',
  import_rule_manage: 'import_rule_manage',
  proxy_rule_manage: 'proxy_rule_manage',
  // 系统管理
  system_manage: 'system_manage',
  role_manage: 'role_manage',
  // 系统测试
  system_test: 'system_test',
  // 部门管理
  department_manage: 'department_manage',
  read_only_department: 'read_only_department'
};
export default PrivilegeMap
