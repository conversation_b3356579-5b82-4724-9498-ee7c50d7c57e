<template>
  <div class="log-timeline">
    <yq-timeline>
      <template v-for="(item, idx) in list">
        <yq-timeline-item
          :key="idx"
          item-offset="35px"
        >
          <template #left>
            <div class="log-timeline__left">
              <div
                v-if="item.showCategory"
                class="category"
              >
                <span class="category__text">{{ item.category }}</span>
                <div class="category__btn">
                  <slot :item="item" />
                </div>
              </div>
              <div class="label">
                {{ item.logTime | formatTime('fmt:Hm') }}
              </div>
            </div>
          </template>
          <div class="log-timeline__content">
            <log-card
              :info="item"
            />
          </div>
        </yq-timeline-item>
      </template>
    </yq-timeline>
  </div>
</template>

<script>
import YqTimeline from '@/components/Timeline';
import YqTimelineItem from '@/components/Timeline/Item';
import { formatTime } from '@/utils/time-utils';
import LogCard from './components/LogCard';

export default {
  name: 'LogCardTimeline',

  components: { YqTimeline, YqTimelineItem, LogCard },
  props: {
    result: {
      type: Array,
      default: () => []
    },
    platform: {
      type: String,
      default: ''
    }
  },
  computed: {
    list() {
      const categoryMap = {};
      return this.result.map(item => {
        const category = formatTime(item.logTime, 'fmt:YD');
        if (!categoryMap[category]) {
          categoryMap[category] = 1;
          item.showCategory = true;
        } else {
          item.showCategory = false;
        }
        return {
          ...item,
          category
        };
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.log-timeline {
  width: 100%;
  height: 100%;
  padding: 50px 20px 20px;

  &__left {
    position: relative;

    .category {
      position: absolute;
      top: -35px;
      left: 0;

      display: flex;
      flex-wrap: nowrap;

      &__text {
        position: relative;
        display: inline-block;
        width: 110px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        // padding: 0 16px;
        background-color: #e8f4ff;
        border-radius: 14px;
        color: #3569e7;
        flex: 0 0 auto;
        &::after {
          content: '';
            position: absolute;
            right: 2px;
            bottom: -5px;

            width: 0;
            height: 0;
            border-top: 14px solid transparent;
            border-right: 14px solid #e8f4ff;
            border-bottom: 14px solid transparent;
            border-left: 14px solid transparent;
        }
      }

      &__btn {
        flex: 0 0 auto;
        margin-left: 30px;
      }

    }

    .label {
      margin-left: 50px;
      width: 40px;
    }
  }

  &__content {
    position: relative;
    padding: 16px;
    border-radius: 10px;
    border: 1px solid #f4f4f5;
    overflow-wrap: anywhere;
    background-color: #f5f7fa;
    &:before {
      content: "";

      width: 0;
      height: 0;

      position: absolute;
      top: 20px;
      left: -10px;
      margin-top: -10px;

      border-style: solid;
      border-width: 10px 10px 10px 0;
      border-color: transparent #f4f4f5 transparent transparent;
    }
  }
}
</style>
