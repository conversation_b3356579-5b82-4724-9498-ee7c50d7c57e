<template>
  <div class="emotion-analysis">
    <section class="left">
      <titan-input
        v-model.trim="content"
        type="textarea"
        resize="none"
        :placeholder="$t('userCenter.输入或粘贴需要分析的内容，仅支持简体中文')"
      />
      <div class="control">
        <el-button
          type="primary"
          size="small"
          round
          :disabled="!content"
          @click="resetContent"
        >
          {{ $t('userCenter.重置内容') }}
        </el-button>
        <el-button
          type="primary"
          round
          size="small"
          :disabled="!content"
          :loading="analysising"
          @click="startAnalysis"
        >
          {{ analysising ? $t('userCenter.分析中') : $t('userCenter.开始分析') }}
        </el-button>
      </div>
    </section>
    <section
      v-loading="analysising"
      class="right"
    >
      <div
        class="timeline__wrapper"
        :class="status"
      >
        <template v-if="showResult">
          <!-- <yq-timeline-item> -->
          <div
            class="result_status"
            :style="setColor('emotion',emotionText)"
          >
            <div
              class="label"
            >
              {{ $t('userCenter.分析结果') }}
            </div>
            <div
              class="status"
              :style="setColor('emotion',emotionText)"
            >
              {{ emotionText | newformatEmotionLabel }}
            </div>
          </div>

        <!-- </yq-timeline-item> -->
        <!-- <yq-timeline-item
            v-for="(val, i) of funcs"
            :key="i"
          >
            <div class="result_card">
              <div class="label">
                {{ val.label }}
              </div>
              <div class="desc">
                {{ message[i].join('、') }}
              </div>
            </div>
            <div
              slot="dot"
              class="dot"
            />
          </yq-timeline-item> -->
        </template>
        <titan-placement
          :empty="!showResult"
          :empty-text="$t('userCenter.暂无分析结果')"
        />
      </div>
    </section>
  </div>
</template>

<script>
// import YqTimeline from '@/components/Timeline';
// import YqTimelineItem from '@/components/Timeline/Item';
import { ArticleAPI } from '@/api/modules/net-resource';
import { getEmotionInfo } from '@/assets/libs/enum';
import { PoliticalOptions, EmotionOptions } from '@/assets/libs/emotion';

export default {
  name: 'EmotionAnalysis',
  components: { },
  data() {
    return {
      content: '',
      showResult: false,

      status: '',
      emotionText: '',
      analysising: false
    };
  },
  computed: {
    funcs() {
      return {
        labels: { label: this.$t('userCenter["文本标签"]'), value: [] },
        pers: { label: this.$t('userCenter["相关人物"]'), value: [] },
        locs: { label: this.$t('userCenter["相关位置"]'), value: [] },
        text_types: { label: this.$t('userCenter["文本分类"]'), value: [] }
        // orgs: { label: '相关机构', value: [] }
      };
    },
    message() {
      const labels = this.funcs['labels'].value.map(item => item.name);
      const pers = this.funcs['pers'].value.map(item => item.name);
      const locs = this.funcs['locs'].value.map(item => item.name);
      const text_types = this.funcs['text_types'].value.map(item => item.label);
      // const orgs = this.funcs['orgs'].value.map(item => item.name);
      return { labels, pers, locs, text_types };
    }
  },
  methods: {
    resetContent() {
      this.showResult = false;
      this.content = '';
    },
    setColor(type, val) {
      if (val === -1) return;
      if (type === 'political') {
        const obj = PoliticalOptions.find(item => item.value === val);
        return obj ? `color: ${obj.color};background-color: ${obj.backgroundColor};border: 1px solid ${obj.borderColor}` : '';
      } else {
        const obj = EmotionOptions.find(item => item.value === val);
        return obj ? `color: ${obj.color};background-color: ${obj.backgroundColor};border: 1px solid ${obj.borderColor}` : '';
      }
    },

    async startAnalysis() {
      try {
        this.analysising = true;
        const { data } = await ArticleAPI.analysisEmotion({ content: this.content });
        this.emotionText = data;

        // this.calcEmotion(data);
        this.analysising = false;
        this.showResult = true;
      } catch (error) {
        this.analysising = false;
        console.error(error);
      }
    },
    handlerClose() {
      this.content = '';
      this.showResult = false;
    }
  }
};
</script>

<style lang="scss">
  .emotion-analysis {
    display: flex;
    height: 100%;
    .left {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .el-textarea {
        flex: 1;
        .el-textarea__inner {
          height: 100%;
        }
      }
      .control {
        margin-top: 16px;
      }
    }
    .right {
      margin-left: 30px;
      flex-shrink: 0;
      width: 290px;
      height: 100%;
    }
    .timeline__wrapper {
      position: relative;
      box-sizing: border-box;
      height: 100%;
      padding-right: 2px;
      overflow-x: hidden;
      overflow-y: auto;
      .yq-timeline-item {
        margin-bottom: 16px;
        &:last-of-type {
          margin-bottom: 0px;
        }
      }
      .yq-timeline-item__wrapper {
        margin-bottom: 0px;
        top: 0;
      }
      .yq-timeline-item__tail {
        position: absolute;
        top:50%;
        left: 6px;
        height: calc(100% + 16px);
        border: 1px dashed #dcdfe6 !important;
        z-index: 1;
      }
      .dot {
        position: absolute;
        top: 50%;
        left:0;
        transform: translate(0, -50%);
        width: 15px;
        height: 15px;
        background-color: white;
        border-radius: 50%;
        border: solid 3px #909399;
        z-index: 2;
      }
      .result_status {
        // width: 250px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 14px;
        background: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #e9e9eb;
        border-left: 4px solid #909399;
        color: #909399;
        .label {
          line-height: 16px;
        }
        .status {
          padding: 5px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 12px;
          background: #ebebeb;
        }
      }
      .result_card {
        width: 250px;
        box-sizing: border-box;
        padding: 12px 14px;
        background: #f4f4f5;
        border-radius: 4px;
        border: 1px solid #e9e9eb;
        border-left: 4px solid #d1d4d9;
        .label {
          color: #909399;
          line-height: 16px;
        }
        .desc {
          line-height: 16px;
          margin-top: 2px;
          color: #303133;
        }
      }
      &.positive {
        .dot {
          border-color: #0fd2be;
        }
        .result_status {
          background: rgba(#0fd2be, 0.1);
          border: 1px solid rgba(#0fd2be, 0.2);
          border-left: 4px solid #0fd2be;
          .status {
            background: rgba(#0fd2be, 0.3);
            color: #0fd2be;
          }
        }
      }
      &.negative {
        .dot {
          border-color: #ff274b;
        }
        .result_status {
          background: rgba(#ff274b, 0.1);
          border: 1px solid rgba(#ff274b, 0.2);
          border-left: 4px solid #ff274b;
          .status {
            background: rgba(#ff274b, 0.3);
            color: #ff274b;
          }
        }
      }
    }
  }
</style>
