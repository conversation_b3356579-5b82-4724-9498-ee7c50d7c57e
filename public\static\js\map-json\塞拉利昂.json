{"title": "Sierra Leone", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:2159"}}, "hc-transform": {"default": {"crs": "+proj=tmerc +lat_0=6.666666666666667 +lon_0=-12 +k=1 +x_0=152399.8550907544 +y_0=0 +a=6378300 +b=6356751.689189189 +to_meter=0.3047997101815088 +no_defs", "scale": 0.000626950195019, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 30625.9701514, "yoffset": 1208324.27434}}, "features": [{"type": "Feature", "id": "SL.SO", "properties": {"hc-group": "admin1", "hc-middle-x": 0.6, "hc-middle-y": 0.49, "hc-key": "sl-so", "hc-a2": "SO", "labelrank": "7", "hasc": "SL.SO", "alt-name": null, "woe-id": "2347002", "subregion": null, "fips": "SL03", "postal-code": "SO", "name": "Southern", "country": "Sierra Leone", "type-en": "Province", "region": null, "longitude": "-12.0512", "woe-name": "Southern", "latitude": "7.85416", "woe-label": "Southern, SL, Sierra Leone", "type": "Province"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[1466, 1446], [1500, 1428], [1636, 1432], [1749, 1383], [1828, 1288], [1788, 1263], [1806, 1119], [1796, 1056], [1710, 927], [1733, 710], [1688, 669], [1571, 680], [1539, 732], [1611, 732], [1501, 907], [1431, 973], [987, 1089], [872, 1148], [574, 1193], [214, 1291], [214, 1317], [345, 1348], [403, 1441], [590, 1482], [974, 1481], [1166, 1524], [1376, 1541], [1518, 1507], [1466, 1446]]], [[[6575, 504], [6316, 182], [6026, 51], [5967, 39], [5844, -153], [5770, -225], [5829, -343], [5834, -418], [5712, -455], [5714, -550], [5650, -727], [5554, -782], [5547, -931], [5408, -999], [5349, -952], [5204, -919], [5014, -789], [5086, -741], [4987, -739], [4893, -765], [4861, -686], [4625, -479], [4127, -190], [3303, 108], [2454, 445], [1779, 660], [1886, 749], [2015, 753], [2262, 659], [2213, 727], [2151, 739], [2094, 804], [1991, 833], [1928, 800], [1829, 884], [1906, 1017], [2055, 1166], [2118, 1145], [2370, 1191], [2454, 1145], [2651, 1232], [2745, 1237], [2723, 1287], [2900, 1340], [2916, 1409], [2795, 1385], [2769, 1431], [2661, 1418], [2638, 1367], [2511, 1315], [2409, 1306], [2337, 1271], [2142, 1215], [1931, 1266], [1886, 1408], [1828, 1458], [1854, 1579], [1802, 1539], [1734, 1557], [1734, 1507], [1649, 1590], [1693, 1708], [1828, 1894], [1905, 1846], [1927, 1870], [1903, 1965], [1973, 2014], [1902, 2062], [1838, 2007], [1735, 1846], [1559, 1738], [1445, 1697], [1367, 1735], [1060, 1797], [826, 1951], [794, 2016], [793, 2124], [890, 2184], [827, 2174], [648, 2102], [482, 2209], [403, 2340], [311, 2373], [190, 2501], [259, 2514], [379, 2581], [445, 2597], [558, 2572], [675, 2522], [604, 2594], [439, 2726], [380, 2898], [358, 2922], [416, 3094], [484, 3177], [371, 3192], [324, 3237], [243, 3394], [115, 3471], [99, 3528], [133, 3683], [312, 3729], [399, 3782], [557, 3944], [673, 4118], [729, 4131], [850, 4102], [900, 4030], [1049, 4071], [1258, 4073], [1378, 4001], [1505, 3994], [1564, 3957], [1579, 3857], [1625, 3824], [1893, 3808], [1972, 3744], [2023, 3778], [2095, 3882], [2202, 3928], [2281, 3991], [2439, 4008], [2549, 3988], [2650, 4032], [2755, 4041], [2850, 4022], [3065, 3902], [3238, 3834], [3339, 3691], [3426, 3676], [3447, 3734], [3422, 3818], [3348, 3912], [3354, 3981], [3426, 4074], [3601, 4079], [3708, 4112], [3795, 4066], [3928, 4031], [4110, 4031], [4338, 4120], [4482, 4270], [4509, 4457], [4625, 4535], [4706, 4550], [4905, 4540], [5036, 4602], [5073, 4454], [5182, 4320], [5197, 4155], [5216, 4105], [5368, 3967], [5517, 3774], [5757, 3537], [5791, 3479], [5796, 3395], [5883, 3157], [5896, 3086], [5909, 2780], [5855, 2644], [5760, 2554], [5533, 2390], [5481, 2197], [5489, 2063], [5590, 2152], [5649, 2001], [5623, 1840], [5591, 1776], [5530, 1755], [5460, 1823], [5249, 1685], [5298, 1655], [5336, 1528], [5351, 1338], [5381, 1218], [5433, 1199], [5500, 1224], [5688, 1426], [5808, 1460], [5904, 1359], [5817, 1263], [5806, 1198], [5818, 1040], [5822, 782], [5881, 746], [6012, 734], [6224, 659], [6316, 606], [6575, 504]]]]}}, {"type": "Feature", "id": "SL.WE", "properties": {"hc-group": "admin1", "hc-middle-x": 0.48, "hc-middle-y": 0.59, "hc-key": "sl-we", "hc-a2": "WE", "labelrank": "7", "hasc": "SL.WE", "alt-name": null, "woe-id": "2347003", "subregion": null, "fips": "SL04", "postal-code": "WE", "name": "Western", "country": "Sierra Leone", "type-en": "Province", "region": null, "longitude": "-13.0935", "woe-name": "Western", "latitude": "8.310409999999999", "woe-label": "Western Area, SL, Sierra Leone", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[133, 3683], [147, 3709], [27, 3656], [-34, 3661], [-189, 3638], [-365, 3552], [-453, 3485], [-506, 3420], [-520, 3476], [-544, 3743], [-526, 3783], [-567, 3817], [-688, 4012], [-736, 4035], [-761, 4100], [-912, 4301], [-956, 4314], [-892, 4404], [-896, 4482], [-955, 4581], [-836, 4571], [-812, 4533], [-689, 4556], [-572, 4506], [-456, 4336], [-336, 4273], [-162, 4129], [-99, 4138], [-28, 4126], [32, 4081], [251, 4037], [336, 4004], [352, 3854], [399, 3782], [312, 3729], [133, 3683]]]}}, {"type": "Feature", "id": "SL.NO", "properties": {"hc-group": "admin1", "hc-middle-x": 0.46, "hc-middle-y": 0.49, "hc-key": "sl-no", "hc-a2": "NO", "labelrank": "7", "hasc": "SL.NO", "alt-name": null, "woe-id": "2347001", "subregion": null, "fips": "SL02", "postal-code": "NO", "name": "Northern", "country": "Sierra Leone", "type-en": "Province", "region": null, "longitude": "-11.9468", "woe-name": "Northern", "latitude": "9.14067", "woe-label": "Northern, SL, Sierra Leone", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[399, 3782], [352, 3854], [336, 4004], [251, 4037], [32, 4081], [-28, 4126], [-99, 4138], [-67, 4167], [-147, 4194], [-187, 4264], [-139, 4288], [-339, 4345], [-390, 4399], [-356, 4483], [-164, 4579], [-88, 4672], [-42, 4651], [6, 4795], [273, 4769], [378, 4746], [510, 4820], [305, 4831], [297, 4871], [362, 4954], [489, 5230], [344, 5027], [276, 4954], [150, 4917], [-11, 4931], [-102, 4877], [-223, 4892], [-259, 4940], [-180, 4955], [-103, 5048], [-41, 5086], [-137, 5159], [-170, 5050], [-235, 4991], [-291, 5008], [-404, 5136], [-489, 5084], [-379, 5039], [-427, 5015], [-330, 4943], [-325, 4864], [-452, 4726], [-437, 4665], [-490, 4630], [-540, 4668], [-658, 4879], [-709, 5047], [-793, 5187], [-789, 5354], [-772, 5356], [-788, 5729], [-690, 5839], [-594, 5791], [-325, 5792], [-197, 5832], [105, 5837], [-40, 5882], [-149, 5852], [-256, 5884], [-402, 5863], [-435, 5924], [-498, 5935], [-445, 6049], [-375, 6081], [-208, 6100], [-269, 6147], [-498, 6079], [-667, 6090], [-736, 6141], [-760, 6225], [-787, 6174], [-838, 6225], [-880, 6175], [-956, 6236], [-943, 6404], [-999, 6490], [-916, 6551], [-671, 6614], [-615, 6600], [-483, 6516], [-350, 6497], [-243, 6574], [-44, 6680], [4, 6739], [113, 6948], [201, 6968], [134, 7091], [151, 7193], [206, 7301], [280, 7353], [348, 7282], [425, 7322], [634, 7334], [693, 7382], [777, 7521], [886, 7565], [873, 7652], [980, 7739], [1030, 7721], [1105, 7820], [1226, 8203], [1222, 8251], [1297, 8301], [1327, 8417], [1453, 8487], [1457, 8636], [1557, 8780], [1682, 8861], [1734, 8927], [1718, 8995], [1785, 9371], [1911, 9445], [2069, 9502], [2595, 9613], [2676, 9610], [2885, 9461], [3068, 9421], [3836, 9588], [3860, 9621], [3877, 9836], [6105, 9851], [6360, 9803], [6425, 9691], [6473, 9458], [6499, 9417], [6759, 9254], [6903, 9116], [7025, 8960], [7109, 8802], [7172, 8648], [7287, 8570], [7310, 8478], [7426, 8381], [7473, 8318], [7491, 8221], [7579, 8148], [7557, 8010], [7572, 7948], [7725, 7783], [7654, 7726], [7886, 7702], [7970, 7603], [8054, 7555], [8203, 7425], [8159, 7259], [8174, 7169], [8141, 7095], [8031, 7030], [7969, 6920], [7931, 6767], [7957, 6639], [8083, 6601], [8228, 6607], [8449, 6570], [8498, 6492], [8300, 6482], [8089, 6346], [8189, 6229], [8123, 6168], [7910, 6181], [7807, 6151], [7717, 6155], [7440, 6342], [7262, 6524], [7207, 6542], [6849, 6588], [6744, 6549], [6680, 6464], [6650, 6303], [6592, 6216], [6456, 6131], [6200, 6041], [6148, 5996], [6047, 5714], [5872, 5551], [5886, 5314], [5923, 5231], [5920, 5152], [5876, 5007], [5917, 4913], [5870, 4747], [5792, 4663], [5593, 4757], [5417, 4712], [5212, 4732], [5036, 4602], [4905, 4540], [4706, 4550], [4625, 4535], [4509, 4457], [4482, 4270], [4338, 4120], [4110, 4031], [3928, 4031], [3795, 4066], [3708, 4112], [3601, 4079], [3426, 4074], [3354, 3981], [3348, 3912], [3422, 3818], [3447, 3734], [3426, 3676], [3339, 3691], [3238, 3834], [3065, 3902], [2850, 4022], [2755, 4041], [2650, 4032], [2549, 3988], [2439, 4008], [2281, 3991], [2202, 3928], [2095, 3882], [2023, 3778], [1972, 3744], [1893, 3808], [1625, 3824], [1579, 3857], [1564, 3957], [1505, 3994], [1378, 4001], [1258, 4073], [1049, 4071], [900, 4030], [850, 4102], [729, 4131], [673, 4118], [557, 3944], [399, 3782]]]}}, {"type": "Feature", "id": "SL.EA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.46, "hc-middle-y": 0.47, "hc-key": "sl-ea", "hc-a2": "EA", "labelrank": "7", "hasc": "SL.EA", "alt-name": null, "woe-id": "2347000", "subregion": null, "fips": "SL01", "postal-code": "EA", "name": "Eastern", "country": "Sierra Leone", "type-en": "Province", "region": null, "longitude": "-10.9314", "woe-name": "Eastern", "latitude": "8.274150000000001", "woe-label": "Eastern, SL, Sierra Leone", "type": "Province"}, "geometry": {"type": "Polygon", "coordinates": [[[8498, 6492], [8446, 6274], [8430, 6152], [8437, 5895], [8473, 5707], [8666, 5464], [8739, 5392], [8890, 5192], [8815, 5047], [8749, 5001], [8597, 4980], [8530, 4924], [8419, 4746], [8341, 4652], [8324, 4535], [8286, 4530], [8236, 4225], [8041, 3945], [8005, 3830], [8076, 3768], [8101, 3886], [8170, 3960], [8269, 3993], [8386, 3987], [8539, 3901], [8640, 3919], [8763, 4000], [8970, 4203], [9013, 4258], [9131, 4479], [9216, 4546], [9375, 4582], [9553, 4526], [9594, 4532], [9592, 4350], [9486, 3917], [9424, 3782], [9418, 3703], [9472, 3533], [9462, 3468], [9308, 3344], [9167, 3312], [8884, 3310], [8731, 3258], [8679, 3193], [8598, 3042], [8441, 2931], [8421, 2856], [8433, 2004], [8327, 1982], [8161, 1892], [8028, 1778], [7523, 1161], [7437, 1110], [7303, 1079], [7159, 998], [6638, 585], [6575, 504], [6316, 606], [6224, 659], [6012, 734], [5881, 746], [5822, 782], [5818, 1040], [5806, 1198], [5817, 1263], [5904, 1359], [5808, 1460], [5688, 1426], [5500, 1224], [5433, 1199], [5381, 1218], [5351, 1338], [5336, 1528], [5298, 1655], [5249, 1685], [5460, 1823], [5530, 1755], [5591, 1776], [5623, 1840], [5649, 2001], [5590, 2152], [5489, 2063], [5481, 2197], [5533, 2390], [5760, 2554], [5855, 2644], [5909, 2780], [5896, 3086], [5883, 3157], [5796, 3395], [5791, 3479], [5757, 3537], [5517, 3774], [5368, 3967], [5216, 4105], [5197, 4155], [5182, 4320], [5073, 4454], [5036, 4602], [5212, 4732], [5417, 4712], [5593, 4757], [5792, 4663], [5870, 4747], [5917, 4913], [5876, 5007], [5920, 5152], [5923, 5231], [5886, 5314], [5872, 5551], [6047, 5714], [6148, 5996], [6200, 6041], [6456, 6131], [6592, 6216], [6650, 6303], [6680, 6464], [6744, 6549], [6849, 6588], [7207, 6542], [7262, 6524], [7440, 6342], [7717, 6155], [7807, 6151], [7910, 6181], [8123, 6168], [8189, 6229], [8089, 6346], [8300, 6482], [8498, 6492]]]}}]}