<template>
  <div class="cross-tag-warp">
    <template v-if="popperDisplay === 'group' && tagList.length>0">
      <el-tag
        v-for="item in tagList"
        :key="item.id"
        :type="tagType"
        :style="msty"
        :disable-transitions="disableTransitions"
      >
        {{ item.name }}
      </el-tag>
    </template>
    <template v-if="popperDisplay === 'everyAndHidden' && visibleTags.length > 0">
      <el-tooltip
        v-for="(item, it) in visibleTags"
        :key="'tag_' + it"
        :content="tagTxt + item.name"
        effect="light"
        placement="top"
      >
        <el-tag
          :type="tagType"
          :style="msty"
          :disable-transitions="disableTransitions"
        >
          <span>{{ item.name }}</span>
        </el-tag>
      </el-tooltip>

      <!-- 隐藏的个数 -->
      <el-tooltip
        v-if="tagList.length - visibleTags.length > 0"
        :content="tagTxt +
          tagList
            .slice(visibleTags.length)
            .map(item => item.name)
            .join('、')
        "
        effect="light"
        placement="top"
      >
        <el-tag
          :type="tagType"
          :disable-transitions="disableTransitions"
          class="more"
        >
          <span>+{{ tagList.length - visibleTags.length }}</span>
        </el-tag>
      </el-tooltip>
    </template>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import i18n from '@/lang';

export default {
  name: 'CrossTagSpan',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    tagType: {
      type: String,
      default: ''
    },
    popperDisplay: {
      type: String,
      default: 'group'
    },
    // 是否禁用渐变动画
    disableTransitions: {
      type: Boolean,
      default: false
    },

    /* 所展示的tag盒子最大的宽度 */
    containerWidth: {
      type: Number,
      default: 0
    },
    tagTxt: {
      type: String,
      default: i18n.t('accountCenter["备注："]')
    },
    // 每个备注的最大宽度
    tagMaxWidth: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return { tagList: [], visibleTags: [] };
  },
  computed: {
    ...mapGetters(['canImport']),
    msty() {
      if (!this.tagMaxWidth) return undefined;
      return { maxWidth: _.isNumber(this.tagMaxWidth) ? this.tagMaxWidth + 'px' : this.tagMaxWidth };
    }
  },
  watch: {
    list: {
      handler() {
        // 过滤掉没有值的list
        const useList = this.list.filter(item => !!item);
        if (this.canImport) {
          this.tagList = [...useList];
        } else {
          this.tagList = useList.filter(item => !item.name.startsWith('##SPARE'));
        }
      },
      immediate: true
    },
    tagList: {
      deep: true,
      handler() {
        if (this.popperDisplay === 'everyAndHidden') {
          this.updateTags();
        }
      }
    }
  },
  mounted() {
    if (this.popperDisplay === 'everyAndHidden') {
      this.updateTags();
    }
  },
  methods: {
    add(arr) {
      const res = this.tagList.concat(arr);
      this.tagList = _.uniqBy(res, 'id');
    },
    updateTags() {
      this.visibleTags = [];
      if (_.isEmpty(this.tagList)) return;

      if (!this.containerWidth || this.containerWidth <= 50) {
        this.visibleTags = this.tagList;
        return;
      }

      const containerWidth = this.containerWidth - 50; // 减去+num的宽度
      let tagVisibleWidth = 0;
      const visibleTags = [];
      for (let i = 0; i < this.tagList.length; i++) {
        const tag = this.tagList[i];
        const span = document.createElement('span');
        span.style.display = 'inline-block';
        span.style.padding = '0 8px'; // 备注有宽度
        if (this.tagMaxWidth) {
          span.style.maxWidth = _.isNumber(this.tagMaxWidth) ? this.tagMaxWidth + 'px' : this.tagMaxWidth;
        }
        span.className = 'tag';
        span.textContent = _.isObject(tag) ? tag.name : tag;
        document.body.appendChild(span);
        const width = span.offsetWidth;
        tagVisibleWidth += width;
        if (i === 0) {
          // 第一个标签必须被添加
          visibleTags.push(tag);
        } else if (tagVisibleWidth <= containerWidth) {
          visibleTags.push(tag);
        } else {
          // 超出容器最大宽度直接移除最后添加的span然后结束循环
          document.body.removeChild(span);
          break;
        }
        document.body.removeChild(span);
      }
      this.visibleTags = visibleTags;
    }
  }
};
</script>

<style lang="scss" scoped>
.cross-tag-warp{
  width: 100%;
  .el-tag{
    margin-right: 10px;
    background-color: #fff4ed;
    border: solid 1px #ffd4ae;
    color: #EFBE62;
    &:not(.more){
      span {
        display: inline-block;
        @include utils-ellipsis(1);
        vertical-align: middle;
        max-width: 100%;
      }
    }
  }
}
</style>
