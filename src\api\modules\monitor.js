import http from '@/api/request';
import URL from '../urls';

/**
 * 监控账号
 */
export class MonitorAPI {
  /**
   * 查询监控列表
   */
  static getMonitorList(data) {
    return http.post(URL.monitor.list, data);
  }

  /**
   * 移除监控
   */
  static delMonitorUser(data) {
    return http.post(URL.monitor.removeAccounts, data);
  }

  /**
   * 新移除监控
   */
  static delMontior(data) {
    return http.post(URL.monitor.delMontior, data);
  }

  /**
   * 监控账号操作
   */
  static MonitorAction(data) {
    return http.post(URL.monitor.MonitorAction, data);
  }

  /**
   * 添加监控
   */
  static addMonitorUser(data) {
    return http.post(URL.monitor.addAccounts, data);
  }

  /* 添加监控任务 */
  static addMonitorMission(data) {
    return http.post(URL.monitor.addMonitorMission, data);
  }

  /* 添加监控 */
  static addMonitor(data) {
    return http.post(URL.monitor.addMonitor, data);
  }

  /* 新的添加监控*/
  static addNewMontior(data) {
    return http.post(URL.monitor.addNewMontior, data);
  }

  /**
   * 获取用户的关联任务ID
   */
  static getMissionId(params) {
    return http.get(URL.monitor.account, { params });
  }

  /**
   * 获取监控账号分组列表
   */
  static getGroupList(data) {
    return http.post(URL.monitor.groupList, { ...data, gatherType: 'MONITOR' });
  }

  /**
   * 分组修改
   */
  static editGroup(data) {
    return http.post(URL.monitor.editGroup, data);
  }

  /**
   * 删除分组
   */
  static deleteGroup(data) {
    return http.post(URL.monitor.deleteGroup, data);
  }

  /**
   * 删除分组并将组内人员移除监控
   */
  static deleteGroupStrictly(data) {
    return http.post(URL.monitor.deleteGroupStrictly, data);
  }

  /**
   * 监控账号 采集任务操作 关闭 暂停 继续
   */
  static monitorMissionAction(data) {
    return http.post(URL.monitor.monitorMissionAction, data);
  }

  /**
   * 监控账号 获取支持新增监控账号的列表
   */
  static supportMonitorList(data) {
    return http.post(URL.monitor.supportMonitorList, data);
  }

  /**
   * 监控账号 数据个人画像详情
   */
  static monitorPersonInfo(data) {
    return http.post(URL.monitor.monitorPersonInfo, data);
  }

  /**
   * 查询当前id是否被监控和是否被添加到榜样用户
   */
  static extraInfo(data) {
    return http.post(URL.monitor.extraInfo, data);
  }

  /**
   * 分页获取用户关系
   */
  static personRelationship(data) {
    return http({
      url: URL.monitor.personRelationship,
      method: 'POST',
      data
    });
  }

  /**
   * 获取好友关系，统计总数
   */
  static userCountRelationships(data) {
    return http.post(URL.monitor.userCountRelationships, data);
  }

  /**
   * 获取用户行为
   */
  static pageInteractAction(data) {
    return http.post(URL.monitor.pageInteractAction, data);
  }

  /**
   * 获取监控账号分组树
   */
  static groupTreeList(data) {
    return http.post(URL.monitor.groupTreeList, data);
  }

  /**
   * 添加监控账号分组树
   */
  static addGroupTree(data) {
    return http.post(URL.monitor.addGroupTree, data);
  }

  /**
   * 修改监控账号分组树
   */
  static updateGroupTree(data) {
    return http.post(URL.monitor.updateGroupTree, data);
  }

  /**
   * 删除监控账号分组树
   */
  static deleteGroupTree(data) {
    return http.post(URL.monitor.deleteGroupTree, data);
  }

  /**
   * 获取账号地区分布
   */
  static getAreaList(data) {
    return http.post(URL.monitor.getArea, data);
  }

  /**
   * 获取监控账号列表---新接口
   */
  static getMonitor(data) {
    return http.post(URL.monitor.getMonitor, data);
  }

  /**
   * 获取监控进度列表---新接口
   */
  static logList(data) {
    return http.post(URL.monitor.logList, data);
  }
}
