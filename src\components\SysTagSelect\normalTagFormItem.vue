<template>
  <div
    v-loading="loading"
    class="normal-tag-span-warp"
  >
    <i
      v-if="showEditBtn && !isEdit"
      class="fa fa-pencil"
      @click="handleEdit"
    />
    <sys-tag-span
      v-if="!isEdit"
      :primary="primary"
      :tags="tagIds"
      :is-el-tag="isElTag"
      :container-width="containerWidth"
      :el-tag-type="elTagType"
      :popper-display="popperDisplay"
    />
    <div
      v-else
      class="select-main"
      :class="{ 'have-select-btn': isEdit }"
    >
      <normal-tag-select
        ref="select"
        :primary="primary"
        :tag-ids.sync="visible_tag_ids"
        :item-show-tooltip="false"
      />
      <svg-icon
        icon-class="btn-error"
        @click="handleCancel"
      />
      <div
        class="svg-btn-warp"
        @click="handleConfirm"
      >
        <svg-icon icon-class="btn-ok" />
        <span>点击确定</span>
      </div>
    </div>
  </div>
</template>

<script>
import SysTagSpan from '@/components/SysTagSelect/sysTagSpan.vue';
import NormalTagSelect from '@/components/SysTagSelect/normalTagSelect.vue';
import { mapGetters, mapMutations, mapState } from 'vuex';
import { tagPrimaryAlias } from '@/assets/libs/enum';
import { clearForm } from '@/utils';
import { TagManagerAPI } from '@/api/modules/tag-manager';

export default {
  name: 'NormalTagFormItem',
  components: { SysTagSpan, NormalTagSelect },
  props: {
    tagIds: {
      type: Array,
      default: () => []
    },
    primary: {
      type: String,
      default: '角色培育通用'
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    isElTag: {
      type: Boolean,
      default: true
    },
    // 标签样式
    elTagType: {
      type: String,
      default: 'primary'
    },
    // 是否显示编辑按钮
    showEditBtn: {
      type: Boolean,
      default: true
    },
    confirmAction: {
      type: [String, Function],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    popperDisplay: {
      type: String,
      default: 'everyAndHidden'
    },
    tagTypeLabel: {
      type: String,
      default: '标签类型'
    },
    tagNameLabel: {
      type: String,
      default: '标签名称'
    },
    tagTxt: {
      type: String,
      default: '标签'
    },
    // 所展示的tag盒子最大的宽度
    containerWidth: {
      type: Number,
      default: 0
    },
    tagMaxWidth: {
      type: [String, Number],
      default: '',
      info: '每个标签的最大宽度'
    }
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      visible_tag_ids: [], // 当前用户可以看见的标签id
      visible_tag_ids_origin: [], // 当前用户可以看见的标签id
      un_visible_tag_ids: [] // 当前用户不能看见，但是却存在的标签id

    };
  },
  computed: {
    ...mapGetters(['canImport']),
    ...mapState('tag', {
      tagDict(state) {
        const primary = tagPrimaryAlias[this.primary];
        return state[primary].tagDict;
      }
    })
  },
  watch: {
    tagIds: {
      handler(val) {
        setTimeout(() => {
          if (_.isEmpty(val)) {
            this.un_visible_tag_ids = [];
            this.visible_tag_ids = [];
            this.visible_tag_ids_origin = [];
            return;
          }
          const ids = val.filter(id => this.tagDict[id]);
          if (_.isEmpty(ids)) {
            this.un_visible_tag_ids = [];
            this.visible_tag_ids = [];
            this.visible_tag_ids_origin = [];
            return;
          }
          if (this.canImport) {
            this.un_visible_tag_ids = [];
            this.visible_tag_ids = [...val];
          } else {
            this.un_visible_tag_ids = ids.filter(id => this.tagDict[id].name.startsWith('##SPARE'));
            this.visible_tag_ids = ids.filter(id => !this.tagDict[id].name.startsWith('##SPARE'));
          }
          this.visible_tag_ids_origin = [...this.visible_tag_ids];
        }, 1000);
      },
      immediate: true
    }
  },
  methods: {
    ...mapMutations('tag', ['ADD_TAG_DICT']),
    handleEdit() {
      this.isEdit = true;
    },
    handleCancel() {
      this.isEdit = false;
      // 重置 select的 v-model
      this.visible_tag_ids = [...this.visible_tag_ids_origin];
    },
    async handleConfirm() {
      try {
        this.loading = true;
        const old_tag_ids = this.visible_tag_ids.filter(id => this.tagDict[id]);
        const new_tag_names = this.visible_tag_ids.filter(id => !this.tagDict[id]);
        let ids;
        if (!_.isEmpty(new_tag_names)) {
          const add_data = new_tag_names.map(name => clearForm({
            name,
            main: this.primary,
            editable: true
          }));
          const { data: new_tags } = await TagManagerAPI.addTag(add_data);
          const tag_list = new_tags.map(item => ({
            id: item.id,
            name: item.name
          }));
          // 更新标签字典
          this.ADD_TAG_DICT({ tags: tag_list, primary: this.primary });
          const new_tag_ids = new_tags.map(item => item.id);
          ids = _.union(old_tag_ids, new_tag_ids);
        } else {
          ids = old_tag_ids;
        }

        // 如果有隐藏标签, 则需要将隐藏标签的id也提交
        if (!_.isEmpty(this.un_visible_tag_ids)) {
          ids = _.union(ids, this.un_visible_tag_ids);
        }
        await this.confirmAction(ids);
        this.isEdit = false;
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.normal-tag-span-warp{
  .fa-pencil{
    font-size: 16px;
    color: #3381D0;
    cursor: pointer;
    &:hover{
      color: #2c94fc;
    }
  }
  .svg-icon{
    font-size: 20px;
    cursor: pointer;
    margin-left: 10px;
    &:hover{
      font-size: 21px;
    }
  }
  .svg-btn-warp{
    width: 50px;
    line-height: 14px;
    text-align: center;
    display: inline-block;
    transform: translateY(3px);
    vertical-align: middle;
    cursor: pointer;
    .svg-icon{
      margin: 5px auto auto;
    }
    span{
      display: block;
      font-size: 12px;
      color: #52B4FF;
      margin-top: 4px;
    }
    &:hover{
      .svg-icon{
        margin-top: 4px;
        font-size: 21px;
      }
    }
  }
  ::v-deep{
    .select-main{
      .el-select{
        width: 100%;
      }
      &.have-select-btn{
        .el-select{
          width: calc(100% - 82px);
        }
      }
    }
  }
}
</style>
