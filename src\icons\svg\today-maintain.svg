<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="102.445" height="78.65" viewBox="0 0 102.445 78.65">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-2 {
        fill: #111d2a;
      }

      .cls-10, .cls-2, .cls-3, .cls-4, .cls-6, .cls-7, .cls-9 {
        fill-rule: evenodd;
      }

      .cls-3 {
        fill: #fff;
      }

      .cls-4, .cls-8 {
        stroke: rgba(0,0,0,0);
      }

      .cls-4 {
        stroke-miterlimit: 10;
      }

      .cls-4, .cls-9 {
        fill: url(#linear-gradient-2);
      }

      .cls-5, .cls-6 {
        fill: #4d4327;
      }

      .cls-7 {
        fill: #f2b740;
      }

      .cls-8 {
        fill: #988650;
      }

      .cls-10 {
        fill: #f2b741;
      }

      .cls-11 {
        fill: url(#linear-gradient-5);
      }

      .cls-12 {
        filter: url(#路径_10);
      }

      .cls-13 {
        filter: url(#路径_5-2);
      }

      .cls-14 {
        filter: url(#路径_5);
      }

      .cls-15 {
        filter: url(#路径_1);
      }
    </style>
    <linearGradient id="linear-gradient" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2e0900" stop-opacity="0"/>
      <stop offset="1" stop-color="#e3af47"/>
    </linearGradient>
    <filter id="路径_1">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-color="#e1ac46" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-2" y1="0" y2="1" xlink:href="#linear-gradient"/>
    <filter id="路径_5" x="0" y="45.371" width="102.445" height="22.007" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-2"/>
      <feFlood flood-color="#e1ad46"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="路径_5-2" x="0" y="45.371" width="102.445" height="22.007" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-3"/>
      <feFlood flood-color="#fff" flood-opacity="0.161" result="color-2"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-3"/>
      <feComposite operator="in" in="color-2"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="路径_10" x="26.027" y="0.841" width="50.598" height="29.708" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-4"/>
      <feFlood flood-color="#e1ad46"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-5" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#e1ad46"/>
    </linearGradient>
  </defs>
  <g id="今日新增" transform="translate(-287.197 -57.35)">
    <rect id="矩形_2307" data-name="矩形 2307" class="cls-1" width="95" height="22" transform="translate(291 114)"/>
    <g id="组_1" data-name="组 1" transform="translate(448.671 0.976)">
      <g data-type="innerShadowGroup">
        <path id="路径_1-2" data-name="路径 1" class="cls-2" d="M-89.085,95.264h-44.226l-10.975,5.221-9.45,4.5,9.45,4.5,10.975,5.221h44.226l10.975-5.221,9.45-4.5-9.45-4.5Z" transform="translate(0.946 7.766)"/>
        <g class="cls-15" transform="matrix(1, 0, 0, 1, -161.47, 56.37)">
          <path id="路径_1-3" data-name="路径 1" class="cls-3" d="M-89.085,95.264h-44.226l-10.975,5.221-9.45,4.5,9.45,4.5,10.975,5.221h44.226l10.975-5.221,9.45-4.5-9.45-4.5Z" transform="translate(162.42 -48.61)"/>
        </g>
      </g>
      <path id="路径_3" data-name="路径 3" class="cls-4" d="M-88.21,101.91l-6.444,3.066-6.446,3.066h-25.776l-6.446-3.066-6.444-3.066,6.444-3.066,6.446-3.066H-101.1l6.446,3.066Z" transform="translate(3.736 7.868)"/>
      <path id="路径_3_-_轮廓" data-name="路径 3 - 轮廓" class="cls-5" d="M-126.988,95.277h26l13.941,6.633-13.941,6.633h-26l-13.941-6.633Zm25.776,1h-25.55L-138.6,101.91l11.839,5.633h25.55l11.839-5.633Z" transform="translate(3.736 7.868)"/>
      <path id="路径_2" data-name="路径 2" class="cls-4" d="M-88.21,99.81l-6.444,3.066-6.446,3.066h-25.776l-6.446-3.066-6.444-3.066,6.444-3.066,6.446-3.066H-101.1l6.446,3.066Z" transform="translate(3.736 7.449)"/>
      <path id="路径_2_-_轮廓" data-name="路径 2 - 轮廓" class="cls-5" d="M-100.987,106.442h-26l-13.941-6.633,13.941-6.633h26l13.941,6.633Zm-25.776-1h25.55l11.839-5.633-11.839-5.633h-25.55L-138.6,99.81Z" transform="translate(3.736 7.449)"/>
      <path id="路径_4" data-name="路径 4" class="cls-6" d="M-65.491,106.093-76.648,111.4l-11.159,5.309-.184.088h-45.022l-.185-.088-11.158-5.309-11.157-5.309-1.706-.812,1.706-.812,11.157-5.309,11.158-5.309.185-.088h45.022l.184.088,11.159,5.309,11.157,5.309,1.706.812-1.706.812Zm-11.924,3.685,9.45-4.5-9.45-4.5-10.975-5.221h-44.226l-10.974,5.221-9.451,4.5,9.451,4.5,10.974,5.22h44.226Z" transform="translate(0.251 7.466)"/>
      <g data-type="innerShadowGroup">
        <g class="cls-14" transform="matrix(1, 0, 0, 1, -161.47, 56.37)">
          <path id="路径_5-3" data-name="路径 5" class="cls-7" d="M-62.029,104.7l-16.821,8v-3.42l9.63-4.583-9.63-4.582V96.693l16.821,8Zm-79.624,8-2.73-1.3-14.09-6.705,14.09-6.705,2.73-1.3v3.422l-9.631,4.582,9.631,4.583Z" transform="translate(161.47 -48.32)"/>
        </g>
        <g class="cls-13" transform="matrix(1, 0, 0, 1, -161.47, 56.37)">
          <path id="路径_5-4" data-name="路径 5" class="cls-3" d="M-62.029,104.7l-16.821,8v-3.42l9.63-4.583-9.63-4.582V96.693l16.821,8Zm-79.624,8-2.73-1.3-14.09-6.705,14.09-6.705,2.73-1.3v3.422l-9.631,4.582,9.631,4.583Z" transform="translate(161.47 -48.32)"/>
        </g>
      </g>
      <path id="路径_6" data-name="路径 6" class="cls-6" d="M-114.111,56.655l10.417,6.322L-93.278,69.3l.435.265V95.859l-.435.265-10.416,6.322-10.417,6.322-.463.282-.464-.282-10.417-6.322-10.416-6.322-.436-.265V69.566l.436-.265,10.416-6.324,10.417-6.322.464-.281.463.281Zm9.488,7.856-9.951-6.042-9.953,6.042-9.98,6.058V94.856l9.98,6.058,9.953,6.04,9.951-6.04,9.98-6.058V70.569Z" transform="translate(4.427 0)"/>
      <path id="路径_1851" data-name="路径 1851" class="cls-8" d="M-473.113-3933.965l.5-.28,11.989-6.656v-29.089l-11.989-6.656-.5-.28v-2.052l1.376.764,13.2,7.328.464.258v30.365l-.464.258-13.2,7.328-1.376.764Zm-25.9,1.288-13.2-7.328-.463-.258v-30.365l.463-.258,13.2-7.328,1.376-.764v2.052l-.5.28-11.988,6.656v29.089l11.988,6.656.5.28v2.052Z" transform="translate(375.225 4038.157)"/>
      <path id="路径_9" data-name="路径 9" class="cls-9" d="M-104.921,64.161l-9.952-6.041-9.952,6.041-9.98,6.059V94.508l9.98,6.059,9.952,6.041,9.952-6.041,9.98-6.059V70.22Z" transform="translate(4.726 0.349)"/>
      <g class="cls-12" transform="matrix(1, 0, 0, 1, -161.47, 56.37)">
        <path id="路径_10-2" data-name="路径 10" class="cls-10" d="M-95.115,70.249-107.059,63V59.575l14.879,9.032V83.283h-2.934V70.249Zm-38.729,0V83.283h-2.934V68.607l14.879-9.032V63Z" transform="translate(165.81 -55.73)"/>
      </g>
    </g>
    <path id="维护" class="cls-11" d="M234.155,173.923l3.508,6.082a1.82,1.82,0,0,1,0,1.819l-4.862,8.428a1.82,1.82,0,0,1-1.576.911h-5.606l-3.922,6.8a3.64,3.64,0,0,1-4.974,1.332l-1.57-.913a3.64,3.64,0,0,1-1.332-4.971l3.964-6.877-2.718-4.713a1.82,1.82,0,0,1,0-1.819l4.862-8.428a1.82,1.82,0,0,1,1.576-.911h8.226l-3.549,6.148a2.73,2.73,0,0,0,4.729,2.73l3.245-5.622Zm-6.189,12.156a.91.91,0,0,0-.332-1.243l-4.729-2.73a.91.91,0,1,0-.91,1.576l4.729,2.73a.91.91,0,0,0,1.243-.333Zm.839,8.25h7.281a1.82,1.82,0,0,1,0,3.64h-7.281a1.82,1.82,0,0,1,0-3.64Z" transform="translate(112.879 -101.477)"/>
  </g>
</svg>
