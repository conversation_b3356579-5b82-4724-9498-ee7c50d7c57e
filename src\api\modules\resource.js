import http from '@/api/request';
import URL from '../urls';
import { clearCloneForm } from '@/utils';
import { isEmpty } from 'lodash';

/**
 * 文章管理（内容库管理）
 */
export class ArticleAPI {
  /**
   * 上传文章
   * @param {File} file 二进制文件
   */
  static uploadSource(file) {
    const form = new FormData();
    form.append('file', file);
    return http.post(URL.article.upload, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /**
   * 资源管理-批量导出文章
   * @param {object} filter 筛选条件
   */
  static export(filter) {
    return http.post(URL.article.exportArticle, filter, { responseType: 'blob' });
  }

  /**
   * 资源管理-文章模板导出
   * @param {object}
   */
  static articleTemplate() {
    return http.post(URL.article.download);
  }

  /**
   * 获取文章详情
   * @param {number} id 文章ID
   */
  static detail(id) {
    return http.post(URL.article.detail, { id: id, file_type: 'post' });
  }

  /**
   * 编辑文章
   */

  static editArticle(data) {
    return http.post(URL.article.edit, data);
  }

  /**
   * 内容培育
   */
  static addArticle(data) {
    return http.post(URL.article.cultivate, JSON.parse(data));
    // Message.success('文章已生成完成，请在内容库刷新查看');
  }

  /**
    * 内容培育2
    */
  static addArticle2(data) {
    return http.post(URL.article.cultivate2, data);
  }

  /**
    * 生成对抗头像
    */
  static yqHeadPortrait(data) {
    return http.post(URL.article.yq_head_portrait, data);
  }

  /**
    * 生成头像任务查询
    */
  static headPortartTask(id) {
    return http.get(URL.article.headPortartTask(id));
  }

  /**
   * 内容培育记录
   */
  static getBreedRecord(data) {
    return http.post(URL.article.record, data);
  }

  /**
   * 内容培育主体
   */
  static getTopicList(params) {
    return http.get(URL.article.topicList, { params });
  }

  /**
   * 内容培育行为
   */
  static getContentList(params) {
    return http.get(URL.article.contentList, { params });
  }

  /**
   * 内容培育立场
   */
  static getAttitudeList(params) {
    return http.get(URL.article.attitudeList, { params });
  }

  /**
   * 获取 语言种类 供筛选条件用
   */
  static getLanguage(data) {
    return http.post(URL.article.language, data);
  }

  /**
   * 素材库-聚合
   */
  static getMaterialBase(data) {
    return http.post(URL.article.materialBase, data);
  }

  /**
   * 新增文章时支持的语言列表
   */
  static getNewLanguage() {
    return http.get(URL.article.newLanguage);
  }

  /**
   * 获取 标签列表 供筛选条件用
   */
  static getTags() {
    return http.post(URL.article.tag, { file_type: 'post' });
  }

  /**
   * 删除文章
   */
  static delArticle(data) {
    return http.post(URL.article.del, { ids: data, file_type: 'post' });
  }

  /**
   * 新增文章
   */
  static postArticle(content_info) {
    return http.post(URL.article.add, { content_info });
  }

  /**
   * 给文章打标签
   */

  static addTag(data) {
    return http.post(URL.article.addTag, data);
  }
}

/**
 * 评论管理
 */
export class CommentAPI {
  /**
   * 上传评论
   * @param {File} file 二进制文件
   * @param {object} otherParams 其他参数
   */
  static uploadSource(file, otherParams = {}) {
    const form = new FormData();
    form.append('file', file);
    Object.keys(otherParams).forEach(key => {
      form.append(key, otherParams[key]);
    });
    return http.post(URL.comment.update, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /**
   * 资源管理-获取评论列表
   * @param {object} filter 筛选条件
   */
  static resource(filter) {
    return http.post(URL.comment.resource, filter);
  }

  /**
   * 资源管理-批量导出评论
   * @param {object} filter 筛选条件
   */
  static export(filter) {
    return http.post(URL.comment.exportComment, filter, { responseType: 'blob' });
  }

  /**
   * 资源管理-评论模板下载
   * @param {object} filter 筛选条件
   */
  static commentTemplate() {
    return http.post(URL.comment.download);
  }

  /**
   * 获取 标签列表 供筛选条件用
   * @param {object} params 查询参数
   */
  static getTags() {
    return http.post(URL.comment.tag, { file_type: 'comment' });
  }

  /**
   * 分解任务 -根据筛选条件获取评论列表
   * @param {object} filter 评论筛选条件
   */
  static list(filter) {
    return http.post(URL.comment.list, filter);
  }

  /**
   * 获取 语言种类 供筛选条件用
   */
  static getLanguage(data) {
    return http.post(URL.comment.language, data);
  }

  /**
   * 新增评论时支持的语言列表
   */
  static getNewLanguage() {
    return http.get(URL.comment.newLanguage);
  }

  /**
   * 删除评论
   */
  static deleteComment(data) {
    return http.post(URL.comment.del, { ids: data, file_type: 'comment' });
  }

  /**
   * 新增评论
   */
  static addComment(data) {
    return http.post(URL.comment.add, data);
  }

  /**
   * 给评论打标签
   */

  static addTag(data) {
    return http.post(URL.comment.addTag, data);
  }

  /**
   * 编辑评论
   */
  static editComment(data) {
    return http.post(URL.comment.edit, data);
  }
}

/**
 * 云服务器管理
 */
export class CloudServerAPI {
  /**
   * 创建云服务器
   * @param {object} data 创建服务器的参数
   */
  static addServer(data) {
    return http.post(URL.cloudServer.server, data);
  }

  /**
   * 创建用户
   * @param {object} data 创建用户的参数
   */
  static addUser(data) {
    return http.post(URL.cloudServer.user, data);
  }

  /**
   * 删除用户
   * @param {string} id 用户id
   */
  static deleteUser(id) {
    return http.delete(URL.cloudServer.user + '/' + id);
  }

  /**
   * 修改用户信息
   * @param {string} id 用户id
   * @param {object} data 修改用户的参数
   */
  static putUser(id, data) {
    return http.put(URL.cloudServer.user + '/' + id, data);
  }

  /**
   * 资源管理-获取云服务器列表
   * @param {object} params 筛选条件
   */
  static getServer(params) {
    return http.get(URL.cloudServer.server, { params });
  }

  /**
   * 资源管理-获取云平台列表
   * @param {object} params 筛选条件
   */
  static getPlatform(params) {
    return http.get(URL.cloudServer.platform, { params });
  }

  /**
   * 资源管理-获取地区列表
   * @param {object} params 筛选条件
   */
  static getRegion(params) {
    return http.get(URL.cloudServer.region, { params });
  }

  static getUser(params) {
    return http.get(URL.cloudServer.user, { params });
  }

  static getZone(params) {
    return http.get(URL.cloudServer.zone, { params });
  }

  static getHardware(params) {
    return http.get(URL.cloudServer.hardware, { params });
  }

  static getImage(params) {
    return http.get(URL.cloudServer.image, { params });
  }

  static serverAction({ action, id }) {
    return http.post(URL.cloudServer.serverAction(id, action));
  }

  static deleteServer(id) {
    return http.delete(URL.cloudServer.server + '/' + id);
  }

  static getServerVNC(id) {
    return http.get(URL.cloudServer.serverVNC(id));
  }

  static getIps(params) {
    return http.get(URL.cloudServer.ip, { params });
  }

  static getDisk(params) {
    return http.get(URL.cloudServer.disk, { params });
  }

  static addIp(data) {
    return http.post(URL.cloudServer.ip, data);
  }

  static editIp(id, data) {
    return http.put(URL.cloudServer.bindIp(id), data);
  }

  static bindIp(id, data) {
    return http.post(URL.cloudServer.bindIp(id), data);
  }

  static delIp(id) {
    return http.delete(URL.cloudServer.bindIp(id));
  }

  static addDisk(data) {
    return http.post(URL.cloudServer.disk, data);
  }

  static bindDisk(id, data) {
    return http.post(URL.cloudServer.bindDisk(id), data);
  }

  static delDisk(id) {
    return http.delete(URL.cloudServer.bindDisk(id));
  }
}

/**
 * 代理(IP池)管理
 */
export class ProxyAPI {
  /**
   * 资源管理-获取代理资源（IP池）列表
   * @param {object} filter 筛选条件
   */
  static resource(data) {
    return http.post(URL.proxy.resource, data);
  }

  /**
   * 资源管理-导出代理资源（IP池）列表
   * @param {object} filter 筛选条件
   */
  static exportResource(filter) {
    return http.post(URL.proxy.exportResource, filter);
  }

  /**
   * 更新代理
   */
  static update() {
    return http.post(URL.proxy.update);
  }

  /**
   * 获取代理类型枚举值
   */
  static getTypes() {
    return http.get(URL.proxy.types);
  }

  /**
   * 获取代理区域枚举值
   */
  static getAreas() {
    return http.get(URL.proxy.areas);
  }

  /**
   * 获取代理替换规则
   */
  static getPlans(data) {
    return http.post(URL.proxy.plan, data);
  }

  /**
   * 修改代理替换规则
   */
  static modifyPlan(id, data) {
    return http.patch(URL.proxy.modifyPlan(id), data);
  }

  /**
   * 增加代理替换规则
   */
  static increasePlan(data) {
    return http.post(URL.proxy.increasePlan, data);
  }

  /**
   * 删除代理替换规则
   */
  static deletePlan(id) {
    return http.delete(URL.proxy.modifyPlan(id));
  }

  /**
   * 上传代理资源
   * @param {File} file 二进制文件
   * @param {object} otherParams 其他参数
   */
  static uploadSource(file, otherParams = {}) {
    const form = new FormData();
    form.append('file', file);
    Object.keys(otherParams).forEach(key => {
      form.append(key, otherParams[key]);
    });
    return http.post(URL.proxy.upload, form, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
  * 网络资源手动上传文件的Url
  */
  static netResourceUploadUrl(data) {
    return http.post(URL.proxy.netResourceUploadUrl, data);
  }

  /**
   * 删除代理
   */
  static deleteIp(data) {
    return http.delete(URL.proxy.delete, { data });
  }

  static getTags(params) {
    return http.get(URL.proxy.tags, { params });
  }

  // 下载proxy模板
  static uploadProxyTempl() {
    return http.get(URL.proxy.upload);
  }

  // 获取代理标签
  static getProxyTags(params) {
    return http.get(URL.proxy.getProxyTags, { params });
  }

  // 获取代理标签及数量
  static getProxyTagsAndNum(data) {
    delete data.page_index;
    delete data.page_size;
    const params = clearCloneForm(data);
    if (isEmpty(params)) return;
    return http.post(URL.proxy.getProxyTagsAndNum, params);
  }

  // 获取代理标签并展示数量
  static getProxyTagsAndCount(params) {
    return http.get(URL.proxy.getProxyTagsAndCount, { params });
  }

  // 获取代理vps
  static getProxyVPS(data) {
    return http.post(URL.proxy.getProxyVPS, data);
  }

  // 精准搜索代理
  static searchProxy(params) {
    return http.get(URL.proxy.searchProxy, { params });
  }

  // 手动更换账号代理
  static changeAccountProxy(data) {
    return http.post(URL.proxy.changeAccountProxy, data);
  }

  /**
   * 获取代理的详情
   */
  static getProxyDetail(id) {
    return http.get(URL.proxy.getProxyDetail(id));
  }

  /*
  *获取代理树形地区
  */
  static getTreeProxyRegion(data) {
    return http.post(URL.proxy.getTreeProxyRegion, data);
  }

  /*
  *获取测试代理平台
  */
  static getTestProxyPlatform() {
    return http.get(URL.proxy.getTestProxyPlatform);
  }

  /*
  *获取测试代理地区
  */
  static getTestProxyArea() {
    return http.get(URL.proxy.getTestProxyArea);
  }

  /* 创建代理检测任务 */
  static createInspectTask(data) {
    return http.post(URL.proxy.createInspectTask, data);
  }

  /* 获取代理检测任务结果 */
  static getProxyTaskInfo(data) {
    return http.post(URL.proxy.getProxyTaskInfo, data);
  }

  /* 获取代理检测任务结果记录*/
  static getPageInspectRecord(data) {
    return http.post(URL.proxy.getPageInspectRecord, data);
  }

  /* 获取代理检测任务结果记录*/
  static getLastInspectRecord(data) {
    return http.post(URL.proxy.getLastInspectRecord, data);
  }
}

/**
 * 数据处理
 */
export class DataHandleAPI {
  /**
   * 资源管理-获取云服务器列表
   */
  static resource() {
    return http.post(URL.dataHandle.resource);
  }
}

/**
 * 资源管理-报表相关接口
 */
export class DashboardAPI {
  /**
   * 获取资源管里-账号相关报表
   * @param {string} type platform | country | state | breed
   */
  static getAccountReport(params) {
    return http.get(URL.resourceDashboard.account, { params });
  }

  // 接码平台
  static getPassCodeStatus(data) {
    return http({
      url: URL.resourceDashboard.passCodeStatus,
      method: 'POST',
      data
    });
  }

  // 账号分析 平台
  static getAccountPlatform(data) {
    return http.post(URL.resourceDashboard.platform, data);
  }

  // 账号分析 平台
  static getCodeList(data) {
    return http.post(URL.resourceDashboard.getCodeList, data);
  }

  // 邮箱 手机号统计
  static getEmailPhoneStatic(params) {
    return http.get(URL.resourceDashboard.static, { params });
  }

  // 平台余额
  static getPlatformBalance() {
    return http.get(URL.resourceDashboard.balance);
  }

  static getIpStatic() {
    return http.get(URL.resourceDashboard.static);
  }

  // 账号分析 今日被封/今日新增
  static getTodayLockAddNum(data) {
    return http.post(URL.resourceDashboard.todayLockOrAdd, data);
  }

  // 账号状况 被封/正常
  static getAccontStatus(platform) {
    return http.get(URL.resourceDashboard.accountStatus(platform));
  }

  static getPlatformAnalysis(data) {
    return http.post(URL.resourceDashboard.report, data);
  }

  static getCollectionTemp(data) {
    return http.post('/data_statistics', data);
  }

  /**
   * 获取IP图标数据
   */
  static getIPstatistics(params) {
    return http.get(URL.resourceDashboard.ip, { params });
  }

  /**
   * 内容资源视频、音频、图片库
   */
  static getResources(query) {
    return http.post(URL.resourceDashboard.media, query);
  }

  /**
   * 系统资源视频、音频、图片库
   */
  static getSysResources(query) {
    return http.post(URL.resourceDashboard.sysmedia, query);
  }

  /**
   * 删除 视频、音频、图片库
   * @param {*} data
   */
  static delResources(data) {
    return http.post(URL.resourceDashboard.delMedia, data);
  }

  /**
   * 添加 视频、音频、图片库 到系统资源
   * @param {*} data
   */
  static addSysResources(data) {
    return http.post(URL.resourceDashboard.addSysMedia, data);
  }

  /**
   * 编辑 视频、音频、图片库信息
   * @param {*} data
   */
  static updateSysResources(data) {
    return http.post(URL.resourceDashboard.editMedia, data);
  }

  /**
   * 删除 系统资源 视频、音频、图片库
   * @param {*} data
   */
  static delSysResources(data) {
    return http.post(URL.resourceDashboard.delSysMedia, data);
  }

  /**
   * 媒体来源列表
   */
  static getFileSourceList(data) {
    return http.post(URL.resourceDashboard.list, data);
  }

  /**
   * 媒体标签列表
   */
  static getTagList(data) {
    return http.post(URL.resourceDashboard.tagList, data);
  }

  /**
   * 网络资源 媒体标签列表
   */
  static getNetTagList(params) {
    return http.post(URL.resourceDashboard.netTagList, params);
  }

  /* 获取解封账号数量统计图表数据 */
  static getSealedAccount(data) {
    return http.post(URL.resourceDashboard.getSealedAccount, data);
  }

  /* 账号解封趋势 */
  static getAccountUnblockingTrend(data) {
    return http.post(URL.resourceDashboard.getAccountUnblockingTrend, data);
  }

  /* 今日账号统计 */
  static getAccountStatisticsToday(data) {
    return http.post(URL.resourceDashboard.getAccountStatisticsToday, data);
  }

  /* 获取 手机号资源统计（被封、已注册）*/
  static getRegAndBanByPhone(data) {
    return http.post(URL.resourceDashboard.getRegAndBanByPhone, data);
  }

  /* 获取 IP资源统计（被封、已注册）*/
  static getRegAndBanByProxy(data) {
    return http.post(URL.resourceDashboard.getRegAndBanByProxy, data);
  }

  /* 点击标签，获取对应 已注册的手机号列表*/
  static getPhoneRegListByTag(data) {
    return http.post(URL.resourceDashboard.getPhoneRegListByTag, data);
  }

  /* 点击标签，获取对应 已被封的手机号列表*/
  static getPhoneBanListByTag(data) {
    return http.post(URL.resourceDashboard.getPhoneBanListByTag, data);
  }

  /* 点击标签，获取对应 已注册的IP列表*/
  static getIpRegListByTag(data) {
    return http.post(URL.resourceDashboard.getIpRegListByTag, data);
  }

  /* 点击标签，获取对应 已被封的IP列表*/
  static getIpBanListByTag(data) {
    return http.post(URL.resourceDashboard.getIpBanListByTag, data);
  }

  /* 目标库-文章列表 */
  static getAimArticleList(data) {
    return http.post(URL.resourceDashboard.getAimArticleList, data);
  }

  /* 目标库-社群列表 */
  static getAimAssociationList(data) {
    return http.post(URL.resourceDashboard.getAimAssociationList, data);
  }

  /* 目标库-分页社群列表 */
  static getAimAssociationListPage(data) {
    return http.post(URL.resourceDashboard.getAimAssociationListPage, data);
  }

  /* 目标库-编辑更新标签 */
  static updateAimPostTags(data) {
    return http.post(URL.resourceDashboard.updateAimPostTags, data);
  }

  /* 目标库-批量删除文章 */
  static batchDeleteAimArticles(data) {
    return http.post(URL.resourceDashboard.batchDeleteAimArticles, data);
  }

  /* 目标库-单篇新增文章 */
  static aimAddPost(data) {
    return http.post(URL.resourceDashboard.aimAddPost, data);
  }

  /* 目标库-单篇新增文章 */
  static aimPostDetail(data) {
    return http.post(URL.resourceDashboard.aimPostDetail, data);
  }

  /* 目标库-批量删除社群 */
  static batchDeleteAimAssociation(data) {
    return http.post(URL.resourceDashboard.batchDeleteAimAssociation, data);
  }

  /* 目标库-社群编辑更新 */
  static batchUpdateAimAssociation(data) {
    return http.post(URL.resourceDashboard.batchUpdateAimAssociation, data);
  }

  /* 目标库-社群模板下载 */
  static aimAssociationTemplate() {
    return http.post(URL.resourceDashboard.aimAssociationTemplate);
  }

  /* 目标库-文章批量导入 */
  static aimPostBatchImport(file, markTag, professionTag, baseType) {
    const form = new FormData();
    form.append('markTag', markTag);
    form.append('professionTag', professionTag);
    form.append('baseTypes', baseType);
    form.append('file', file);
    return http.post(URL.resourceDashboard.aimPostBatchImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /* 素材库-文章列表 */
  static getMaterialArticleList(data) {
    return http.post(URL.resourceDashboard.getMaterialArticleList, data);
  }

  /* 素材库-评论列表 */
  static getMaterialCommentList(data) {
    return http.post(URL.resourceDashboard.getMaterialCommentList, data);
  }

  /* 素材库-音视图列表 */
  static getMaterialMediaList(data) {
    return http.post(URL.resourceDashboard.getMaterialMediaList, data);
  }

  /* 素材库-音视图编辑更新 */
  static materialMediaDelete(data) {
    return http.post(URL.resourceDashboard.materialMediaDelete, data);
  }

  /* 素材库-文章编辑更新标签 */
  static materialPostUpdate(data) {
    return http.post(URL.resourceDashboard.materialPostUpdate, data);
  }

  /* 素材库-文章批量编辑 */
  static materialPostBatchUpdate(data) {
    return http.post(URL.resourceDashboard.materialPostBatchUpdate, data);
  }

  /* 素材库-文章详情 */
  static materialPostDetail(data) {
    return http.post(URL.resourceDashboard.materialPostDetail, data);
  }

  /* 素材库-音视图编辑更新 */
  static materialMediaUpdate(data) {
    return http.post(URL.resourceDashboard.materialMediaUpdate, data);
  }

  /* 素材库-批量删除文章 */
  static batchDeleteArticles(data) {
    return http.post(URL.resourceDashboard.batchDeleteArticles, data);
  }

  /* 素材库-批量删除评论 */
  static batchDeleteComments(data) {
    return http.post(URL.resourceDashboard.batchDeleteComments, data);
  }

  /* 素材库-编辑更新评论 */
  static batchUpdateComments(data) {
    return http.post(URL.resourceDashboard.batchUpdateComments, data);
  }

  /* 素材库-单篇新增文章 */
  static materialAddPost(data) {
    return http.post(URL.resourceDashboard.materialAddPost, data);
  }

  /* 素材库-单篇新增评论 */
  static materialAddComment(data) {
    return http.post(URL.resourceDashboard.materialAddComment, data);
  }

  /* 素材库-音视图导入 /material/file/fileImport */
  static materialAddMedia(data) {
    return http.post(URL.resourceDashboard.materialAddMedia, data);
  }

  /* 素材库-音视图上传 */
  static upload2TbmsUseBucket(file) {
    const form = new FormData();
    form.append('file', file);
    return http.post(URL.resourceDashboard.upload2TbmsUseBucket, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /* 素材库-文章批量导入 */
  static materialPostBatchImport(file, markTag, professionTag) {
    const form = new FormData();
    form.append('markTag', markTag);
    form.append('professionTag', professionTag);
    form.append('file', file);
    return http.post(URL.resourceDashboard.materialPostBatchImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /* 素材库-评论导入 */
  static materialCommentImport(file, markTag, professionTag) {
    const form = new FormData();
    form.append('markTag', markTag);
    form.append('professionTag', professionTag);

    form.append('file', file);
    return http.post(URL.resourceDashboard.materialCommentImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /* 素材库-视频/图片锁定资源平台聚合 */
  static lockPlatform(data) {
    return http.post(URL.resourceDashboard.lockPlatform, data);
  }
}

/**
 * 手机号管理
 */
export class TelphoneAPI {
  /**
   * 资源管理-获取评论列表
   * @param {object} filter 筛选条件
   */
  static resource(filter) {
    return http.post(URL.telphone.resource, filter);
  }

  /* 后端根据传递的参数，自己去选择资源再用来分享 */
  static shareResource(data) {
    return http.post(URL.telphone.shareResource, data);
  }

  /* 共享已选择的电话号码 */
  static addShareSelected(data) {
    return http.post(URL.telphone.addShare, data);
  }

  /* 取消共享已选择的电话号码 */
  static removeShareSelected(data) {
    return http.post(URL.telphone.removeShare, data);
  }

  /**
   * 上传手机号资源
   * @param {File} file 二进制文件
   * @param {object} otherParams 其他参数
   */
  static upload(file, otherParams = {}) {
    const form = new FormData();
    form.append('file', file);
    Object.keys(otherParams).forEach(key => {
      form.append(key, otherParams[key]);
    });
    return http.post(URL.telphone.upload, form, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  static getAreas() {
    return http.get(URL.telphone.areas);
  }

  /* 共享/不共享 手机号 */
  static togglePhoneShareStatus(data) {
    return http.post(URL.telphone.togglePhoneShareStatus, data);
  }

  /* 获得手机号详情 */
  static detail(id) {
    const url = URL.telphone.detail(id);
    return http.get(url);
  }

  /**
   * 禁用/启用 操作
   */
  static togglePhone(data) {
    return http.patch(URL.telphone.toggle, data);
  }
}

/**
 * 邮箱资源管理
 */
export class EmailAPI {
  /**
   * 资源管理-获取评论列表
   * @param {object} filter 筛选条件
   */
  static resource(filter) {
    return http.post(URL.email.resource, filter);
  }

  /* 共享已选择的邮箱 */
  static addShareSelected(data) {
    return http.post(URL.email.addShare, data);
  }

  /* 取消共享已选择的邮箱 */
  static removeShareSelected(data) {
    return http.post(URL.email.removeShare, data);
  }

  /* 获得手机号详情 */
  static detail(id) {
    const url = URL.email.detail(id);
    return http.get(url);
  }

  /**
   * 上传邮箱资源
   * @param {File} file 二进制文件
   * @param {object} otherParams 其他参数
   */
  static upload(file, otherParams = {}) {
    const form = new FormData();
    form.append('file', file);
    Object.keys(otherParams).forEach(key => {
      form.append(key, otherParams[key]);
    });
    return http.post(URL.email.upload, form, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
   * 禁用/启用 操作
   */
  static toggleEmail(data) {
    return http.patch(URL.email.toggle, data);
  }
}

export class RuleAPI {
  static list(data) {
    return http.post(URL.rule.list, data);
  }

  static add(data) {
    return http.post(URL.rule.add, data);
  }

  static edit(id, data) {
    return http.patch(URL.rule.detail(id), data);
  }

  static delete(id) {
    return http.delete(URL.rule.detail(id));
  }
}

export class MappingAPI {
  static list(data) {
    return http.post(URL.mapping.list, data);
  }

  static add(data) {
    return http.post(URL.mapping.add, data);
  }

  static edit(id, data) {
    return http.patch(URL.mapping.detail(id), data);
  }

  static delete(id) {
    return http.delete(URL.mapping.detail(id));
  }
}

export class AssignmentAPI {
  static list(params) {
    return http.get(URL.assignment.list, { params });
  }

  static save(data) {
    return http.post(URL.assignment.list, data);
  }

  // 获取部门
  static primaryDepartmentList() {
    return http.get(URL.assignment.primaryDepartmentList);
  }
}

// 系统设置，部门可见平台
export class PlatformVisibleAPI {
  static getAllPlatformAndDepartment() {
    return http.get(URL.platformVisible.getAllPlatformAndDepartment);
  }

  static savePlatformRelation(data) {
    return http.post(URL.platformVisible.savePlatformRelation, data);
  }

  static getPlatformByDepartment() {
    return http.get(URL.platformVisible.getPlatformByDepartment);
  }

  static getPlatformsGroupByAreaName() {
    return http.get(URL.platformVisible.getPlatformsGroupByAreaName);
  }

  static getPlatformsByUsually() {
    return http.get(URL.platformVisible.getPlatformsByUsually);
  }
}

// 系统设置，部门可注册账号数设置
export class DepartmentRegisterAPI {
  static getAllDepartmentRegisterAccountsList(data) {
    return http.post(URL.departmentRegisterAccounts.getDepartmentRegisterAccounts, data);
  }

  static updateRegisterAccounts(data) {
    return http.post(URL.departmentRegisterAccounts.updateRegisterAccounts, data);
  }
}
