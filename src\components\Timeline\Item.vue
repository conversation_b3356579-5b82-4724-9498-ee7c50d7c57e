<template>
  <li
    class="yq-timeline-item"
    :style="style"
  >
    <div
      v-if="$slots.left"
      class="yq-timeline-item__left"
      :style="leftStyle"
    >
      <slot name="left" />
    </div>
    <div class="yq-timeline-item__center">
      <div class="yq-timeline-item__tail" />
      <div
        v-if="!$slots.dot"
        class="yq-timeline-item__node"
      />
      <slot name="dot" />
    </div>
    <div
      class="yq-timeline-item__wrapper"
      :style="rightStyle"
    >
      <slot />
    </div>
  </li>
</template>

<script>

/**
 * 提供的slot  left（自定义左侧内容） dot（可自定义小圆点） default(主要内容)
 */
export default {
  name: 'YqTimelineItem',
  props: {
    /**
     * 时间线距离两边的距离
     * 有左侧slot时 距离两边 没有左侧slot则距离右边
     */
    offset: {
      type: [Number, String],
      default: 20
    },
    top: {
      type: [Number, String],
      default: 0
    },
    itemOffset: {
      type: String,
      default: '10px'
    }
  },
  computed: {
    leftStyle() {
      return { paddingRight: this.offset + 'px' };
    },
    rightStyle() {
      return { paddingLeft: this.offset + 'px' };
    },
    style() {
      return {
        '--height': parseInt(this.top, 10) + 'px',
        '--margin-bottom': this.itemOffset
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.yq-timeline-item {
  width: 100%;
  position: relative;
  display: flex;
  margin-bottom: var(--margin-bottom);

  &__left {
    padding-top: var(--height);
  }
  &__center {
    width: 30px;
    position: relative;
    display: flex;
    justify-content: center;
  }
  &__tail {
    position: relative;
    width: 0;
    height: calc(100% + var(--margin-bottom));
    border: 1px dashed #dcdfe6 !important;
    z-index: 1;
    top: calc(4px + var(--height));
  }
  &__node {
    position: absolute;
    /* top: 12px; */
    top: calc(4px + var(--height));
    width: 11px;
    height: 11px;
    background-color: white;
    border-radius: 50%;
    border: solid 3px #3569e7;
    z-index: 2;
  }
  &__wrapper {
    flex: 1;
    position: relative;
    /* top: -3px; */
    /* margin-bottom: 28px; */

    overflow: hidden;
  }
  &:last-child {
    margin-bottom: 0;

    .yq-timeline-item__tail {
      display: none;
    }
  }
}
</style>
