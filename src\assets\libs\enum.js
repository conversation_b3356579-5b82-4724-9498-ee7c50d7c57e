import { PlatformSourceList, PlatformOptionList } from './platform';

import {
  TargetSiteLocationList,
  TargetSiteLocationMap,
  TargetSiteLocationListSecond,
  TargetSiteAllList
} from './target-site';
import i18n from '@/lang'
export * from './target-site';
export * from './platform';
export * from './cloud';
export * from './account';
export * from './mission';
export * from './emotion';
export * from './character';
// i18n.locale='en'
export const CategoryPlateOptionsByHkgolden = [
  { value: 'BW', label: i18n.t('assets["吹水台"]') },
  { value: 'CA', label: i18n.t('assets["時事台"]') },
  { value: 'SP', label: i18n.t('assets["體育台"]') },
  { value: 'FN', label: i18n.t('assets["財經台"]') },
  { value: 'ST', label: i18n.t('assets["學術台"]') },
  { value: 'SY', label: i18n.t('assets["講故台"]') },
  { value: 'EP', label: i18n.t('assets["創意台"]') },
  { value: 'HW', label: i18n.t('assets["硬件台"]') },
  { value: 'IN', label: i18n.t('assets["電訊台"]') },
  { value: 'SW', label: i18n.t('assets["軟件台"]') },
  { value: 'MP', label: i18n.t('assets["手機台"]') },
  { value: 'AP', label: 'App' },
  { value: 'GM', label: i18n.t('assets["遊戲台"]') },
  { value: 'ED', label: i18n.t('assets["飲食台"]') },
  { value: 'TR', label: i18n.t('assets["旅遊台"]') },
  { value: 'CO', label: i18n.t('assets["潮流台"]') },
  { value: 'AN', label: i18n.t('assets["動漫台"]') },
  { value: 'TO', label: i18n.t('assets["玩具台"]') },
  { value: 'MU', label: i18n.t('assets["音樂台"]') },
  { value: 'VI', label: i18n.t('assets["影視台"]') },
  { value: 'DC', label: i18n.t('assets["攝影台"]') },
  { value: 'TS', label: i18n.t('assets["汽車台"]') },
  { value: 'WK', label: i18n.t('assets["上班台"]') },
  { value: 'LV', label: i18n.t('assets["感情台"]') },
  { value: 'SC', label: i18n.t('assets["校園台"]') },
  { value: 'BB', label: i18n.t('assets["親子台"]') },
  { value: 'PT', label: i18n.t('assets["寵物台"]') },
  { value: 'MB', label: i18n.t('assets["站務台"]') },
  { value: 'RA', label: i18n.t('assets["電　台"]') },
  { value: 'AC', label: i18n.t('assets["活動台"]') },
  { value: 'BS', label: i18n.t('assets["買賣台"]') },
  { value: 'JT', label: i18n.t('assets["直播台"]') },
  { value: 'AU', label: i18n.t('assets["成人台"]') }
];

export const CategoryPlateOptionsByMobile01 = [
  {
    "value": i18n.t('assets["親子"]'),
    "label": i18n.t('assets["彩妝保養"]'),
    "children": [
      {
        "value": "彩妝保養",
        "label": i18n.t('assets["彩妝保養"]')
      },
      {
        "value": "時尚流行",
        "label": i18n.t('assets["時尚流行"]')
      },
      {
        "value": "星座占卜",
        "label": i18n.t('assets["星座占卜"]')
      },
      {
        "value": "育兒話題",
        "label": i18n.t('assets["育兒話題"]')
      },
      {
        "value": "女人心事",
        "label": i18n.t('assets["女人心事"]')
      },
      {
        "value": "孕前產後",
        "label": i18n.t('assets["孕前產後"]')
      },
      {
        "value": "嬰幼食品",
        "label": i18n.t('assets["嬰幼食品"]')
      },
      {
        "value": "童書玩具",
        "label": i18n.t('assets["童書玩具"]')
      },
      {
        "value": "寶貝健康",
        "label": i18n.t('assets["寶貝健康"]')
      },
      {
        "value": "婚姻感情",
        "label": i18n.t('assets["婚姻感情"]')
      },
      {
        "value": "親子消費經驗分享",
        "label": i18n.t('assets["親子消費經驗分享"]')
      }
    ]
  },
  {
    "value": "生活",
    "label": i18n.t('assets["生活"]'),
    "children": [
      {
        "value": "遙控與模型",
        "label": i18n.t('assets["遙控與模型"]')
      },
      {
        "value": "逸品博覽會",
        "label": i18n.t('assets["逸品博覽會"]')
      },
      {
        "value": "動物與寵物",
        "label": i18n.t('assets["動物與寵物"]')
      },
      {
        "value": "健康與養生",
        "label": i18n.t('assets["健康與養生"]')
      },
      {
        "value": "品酩享微醺",
        "label": i18n.t('assets["品酩享微醺"]')
      },
      {
        "value": "漫畫與動畫",
        "label": i18n.t('assets["漫畫與動畫"]')
      },
      {
        "value": "閱讀與創作",
        "label": i18n.t('assets["閱讀與創作"]')
      },
      {
        "value": "電子閱讀趣",
        "label": i18n.t('assets["電子閱讀趣"]')
      },
      {
        "value": "料理與食譜",
        "label": i18n.t('assets["料理與食譜"]')
      },
      {
        "value": "就愛咖啡香",
        "label": i18n.t('assets["就愛咖啡香"]')
      },
      {
        "value": "品茗賞壺趣",
        "label": i18n.t('assets["品茗賞壺趣"]')
      },
      {
        "value": "法律新觀點",
        "label": i18n.t('assets["法律新觀點"]')
      },
      {
        "value": "惡魔島",
        "label": i18n.t('assets["惡魔島"]')
      },
      {
        "value": "英雄村",
        "label": i18n.t('assets["英雄村"]')
      }
    ]
  },
  {
    "value": "理財",
    "label": i18n.t('assets["理財"]'),
    "children": [
      {
        "label": i18n.t('assets["股票 / ETF"]'),
        "value": "國內投資",
        "children": [
          {
            "value": "股票 / ETF",
            "label": i18n.t('assets["股票 / ETF"]')
          },
          {
            "value": "基金",
            "label": i18n.t('assets["基金"]')
          },
          {
            "value": "期貨 / 選擇權 / 權證",
            "label": i18n.t('assets["期貨 / 選擇權 / 權證"]')
          }
        ]
      },
      {
        "label": i18n.t('assets["美股"]'),
        "value": "海外投資",
        "children": [
          {
            "value": "美股",
            "label": i18n.t('assets["美股"]')
          },
          {
            "value": "陸股 / 港股",
            "label": i18n.t('assets["陸股 / 港股"]')
          }
        ]
      },
      {
        "value": "信用卡與消費",
        "label": i18n.t('assets["信用卡與消費"]')
      },
      {
        "value": "人身保險",
        "label": i18n.t('assets["人身保險"]')
      },
      {
        "value": "產物保險",
        "label": i18n.t('assets["產物保險"]')
      },
      {
        "value": "行動支付",
        "label": i18n.t('assets["行動支付"]')
      },
      {
        "value": "生活理財",
        "label": i18n.t('assets["生活理財"]')
      },
      {
        "value": "投資理財綜合",
        "label": i18n.t('assets["投資理財綜合"]')
      }
    ]
  },
  {
    "value": "閒聊",
    "label": i18n.t('assets["閒聊"]'),
    "children": [
      {
        "value": "投資與理財",
        "label": i18n.t('assets["投資與理財"]')
      },
      {
        "value": "閒聊與趣味",
        "label": i18n.t('assets["閒聊與趣味"]')
      },
      {
        "value": "兩性與感情",
        "label": i18n.t('assets["兩性與感情"]')
      },
      {
        "value": "軍事迷基地",
        "label": i18n.t('assets["軍事迷基地"]')
      },
      {
        "value": "職場甘苦談",
        "label": i18n.t('assets["職場甘苦談"]')
      },
      {
        "value": "創業夢想家",
        "label": i18n.t('assets["創業夢想家"]')
      }
    ]
  },
  {
    "value": "時事",
    "label": i18n.t('assets["時事"]'),
    "children": [
      {
        "value": "台灣新聞",
        "label": i18n.t('assets["台灣新聞"]')
      },
      {
        "value": "國際新聞",
        "label": i18n.t('assets["國際新聞"]')
      },
      {
        "value": "兩岸新聞",
        "label": i18n.t('assets["兩岸新聞"]')
      },
      {
        "value": "COVID-19專區",
        "label": i18n.t('assets["COVID-19專區"]')
      }
    ]
  },
  {
    "value": "蘋果",
    "label": i18n.t('assets["蘋果"]'),
    "children": [
      {
        "value": "iPad",
        "label": i18n.t('assets["iPad"]')
      },
      {
        "value": "iPad 軟體",
        "label": i18n.t('assets["iPad 軟體"]')
      },
      {
        "value": "iPhone",
        "label": i18n.t('assets["iPhone"]')
      },
      {
        "value": "iPhone 軟體",
        "label": i18n.t('assets["iPhone 軟體"]')
      },
      {
        "value": "iPod",
        "label": i18n.t('assets["iPod"]')
      },
      {
        "value": "iOS Jailbreak",
        "label": i18n.t('assets["iOS Jailbreak"]')
      },
      {
        "value": "Apple TV",
        "label": i18n.t('assets["Apple TV"]')
      },
      {
        "value": "Apple Watch",
        "label": i18n.t('assets["Apple Watch"]')
      },
      {
        "value": "Apple Music",
        "label": i18n.t('assets["Apple Music"]')
      },
      {
        "value": "Mac桌上型電腦",
        "label": i18n.t('assets["Mac桌上型電腦"]')
      },
      {
        "value": "Mac筆記型電腦",
        "label": i18n.t('assets["Mac筆記型電腦"]')
      },
      {
        "value": "蘋果週邊綜合",
        "label": i18n.t('assets["蘋果週邊綜合"]')
      },
      {
        "value": "蘋果軟體綜合",
        "label": i18n.t('assets["蘋果軟體綜合"]')
      },
      {
        "value": "蘋果裝置消費經驗分享",
        "label": i18n.t('assets["蘋果裝置消費經驗分享"]')
      }
    ]
  },
  {
    "value": "汽車",
    "label": i18n.t('assets["汽車"]'),
    "children": [
      {
        "value": "德國車系",
        "label": i18n.t('assets["德國車系"]'),
        "children": [
          {
            "value": "Audi",
            "label": i18n.t('assets["Audi"]')
          },
          {
            "value": "BMW",
            "label": i18n.t('assets["BMW"]')
          },
          {
            "value": "Mercedes Benz",
            "label": i18n.t('assets["Mercedes Benz"]')
          },
          {
            "value": "Opel",
            "label": i18n.t('assets["Opel"]')
          },
          {
            "value": "Porsche",
            "label": i18n.t('assets["Porsche"]')
          },
          {
            "value": "Skoda",
            "label": i18n.t('assets["Skoda"]')
          },
          {
            "value": "Volkswagen",
            "label": i18n.t('assets["Volkswagen"]')
          },
          {
            "value": "德國車系綜合",
            "label": i18n.t('assets["德國車系綜合"]')
          }
        ]
      },
      {
        "value": "英國車系",
        "label": i18n.t('assets["英國車系"]'),
        "children": [
          {
            "value": "英國車系綜合",
            "label": i18n.t('assets["英國車系綜合"]')
          },
          {
            "value": "Aston Martin",
            "label": i18n.t('assets["Aston Martin"]')
          },
          {
            "value": "Bentley",
            "label": i18n.t('assets["Bentley"]')
          },
          {
            "value": "Jaguar",
            "label": i18n.t('assets["Jaguar"]')
          },
          {
            "value": "Land Rover",
            "label": i18n.t('assets["Land Rover"]')
          },
          {
            "value": "Lotus",
            "label": i18n.t('assets["Lotus"]')
          },
          {
            "value": "McLaren",
            "label": i18n.t('assets["McLaren"]')
          },
          {
            "value": "Mini",
            "label": i18n.t('assets["Mini"]')
          }
        ]
      },
      {
        "value": "法國車系",
        "label": i18n.t('assets["法國車系"]'),
        "children": [
          {
            "value": "法國車系綜合",
            "label": i18n.t('assets["法國車系綜合"]')
          },
          {
            "value": "Peugeot",
            "label": i18n.t('assets["Peugeot"]')
          }
        ]
      },
      {
        "value": "瑞典車系",
        "label": i18n.t('assets["瑞典車系"]'),
        "children": [
          {
            "value": "Saab",
            "label": i18n.t('assets["Saab"]')
          },
          {
            "value": "Volvo",
            "label": i18n.t('assets["Volvo"]')
          }
        ]
      },
      {
        "value": "義大利車系",
        "label": i18n.t('assets["義大利車系"]'),
        "children": [
          {
            "value": "Alfa Romeo",
            "label": i18n.t('assets["Alfa Romeo"]')
          },
          {
            "value": "Ferrari",
            "label": i18n.t('assets["Ferrari"]')
          },
          {
            "value": "Maserati",
            "label": i18n.t('assets["Maserati"]')
          },
          {
            "value": "Lamborghini",
            "label": i18n.t('assets["Lamborghini"]')
          },
          {
            "value": "義大利車系綜合",
            "label": i18n.t('assets["義大利車系綜合"]')
          }
        ]
      },
      {
        "value": "美國車系",
        "label": i18n.t('assets["美國車系"]'),
        "children": [
          {
            "value": "美國車系綜合",
            "label": i18n.t('assets["美國車系綜合"]')
          },
          {
            "value": "Ford",
            "label": i18n.t('assets["Ford"]')
          },
          {
            "value": "GM車系",
            "label": i18n.t('assets["GM車系"]')
          },
          {
            "value": "Tesla",
            "label": i18n.t('assets["Tesla"]')
          }
        ]
      },
      {
        "value": "日本車系",
        "label": i18n.t('assets["日本車系"]'),
        "children": [
          {
            "value": "日本車系綜合",
            "label": i18n.t('assets["日本車系綜合"]')
          },
          {
            "value": "Honda",
            "label": i18n.t('assets["Honda"]')
          },
          {
            "value": "INFINITI",
            "label": i18n.t('assets["INFINITI"]')
          },
          {
            "value": "LEXUS",
            "label": i18n.t('assets["LEXUS"]')
          },
          {
            "value": "Mazda",
            "label": i18n.t('assets["Mazda"]')
          },
          {
            "value": "Mitsubishi",
            "label": i18n.t('assets["Mitsubishi"]')
          },
          {
            "value": "Nissan",
            "label": i18n.t('assets["Nissan"]')
          },
          {
            "value": "Subaru",
            "label": i18n.t('assets["Subaru"]')
          },
          {
            "value": "Suzuki",
            "label": i18n.t('assets["Suzuki"]')
          },
          {
            "value": "Toyota",
            "label": i18n.t('assets["Toyota"]')
          }
        ]
      },
      {
        "value": "韓國車系",
        "label": i18n.t('assets["韓國車系"]'),
        "children": [
          {
            "value": "韓國車系綜合",
            "label": i18n.t('assets["韓國車系綜合"]')
          },
          {
            "value": "Hyundai",
            "label": i18n.t('assets["Hyundai"]')
          },
          {
            "value": "KIA",
            "label": i18n.t('assets["KIA"]')
          }
        ]
      },
      {
        "value": "台灣車系",
        "label": i18n.t('assets["台灣車系"]'),
        "children": [
          {
            "value": "LUXGEN",
            "label": i18n.t('assets["LUXGEN"]')
          }
        ]
      },
      {
        "value": "中國車系",
        "label": i18n.t('assets["中國車系"]'),
        "children": [
          {
            "value": "中國車系綜合",
            "label": i18n.t('assets["中國車系綜合"]')
          },
          {
            "value": "tobe",
            "label": i18n.t('assets["tobe"]')
          }
        ]
      },
      {
        "value": "汽車行車記錄器",
        "label": i18n.t('assets["汽車行車記錄器"]'),
        "children": [
          {
            "value": "汽車行車記錄器綜合",
            "label": i18n.t('assets["汽車行車記錄器綜合"]')
          },
          {
            "value": "DoD汽車行車記錄器",
            "label": i18n.t('assets["DoD汽車行車記錄器"]')
          },
          {
            "value": "GARMIN汽車行車記錄器",
            "label": i18n.t('assets["GARMIN汽車行車記錄器"]')
          },
          {
            "value": "Mio汽車行車記錄器",
            "label": i18n.t('assets["Mio汽車行車記錄器"]')
          },
          {
            "value": "PAPAGO汽車行車記錄器",
            "label": i18n.t('assets["PAPAGO汽車行車記錄器"]')
          }
        ]
      },
      {
        "value": "汽車GPS",
        "label": i18n.t('assets["汽車GPS"]'),
        "children": [
          {
            "value": "汽車GPS其他品牌",
            "label": i18n.t('assets["汽車GPS其他品牌"]')
          },
          {
            "value": "Mio汽車GPS",
            "label": i18n.t('assets["Mio汽車GPS"]')
          },
          {
            "value": "GARMIN汽車GPS",
            "label": i18n.t('assets["GARMIN汽車GPS"]')
          },
          {
            "value": "TOMTOM汽車GPS",
            "label": i18n.t('assets["TOMTOM汽車GPS"]')
          },
          {
            "value": "PAPAGO汽車GPS",
            "label": i18n.t('assets["PAPAGO汽車GPS"]')
          },
          {
            "value": "HOLUX汽車GPS",
            "label": i18n.t('assets["HOLUX汽車GPS"]')
          },
          {
            "value": "Panasonic汽車GPS",
            "label": i18n.t('assets["Panasonic汽車GPS"]')
          },
          {
            "value": "中國品牌汽車GPS",
            "label": i18n.t('assets["中國品牌汽車GPS"]')
          },
          {
            "value": "汽車GPS消費經驗分享",
            "label": i18n.t('assets["汽車GPS消費經驗分享"]')
          },
          {
            "value": "樂客導航王",
            "label": i18n.t('assets["樂客導航王"]')
          }
        ]
      },
      {
        "value": "汽車零組件",
        "label": i18n.t('assets["汽車零組件"]')
      },
      {
        "value": "輪圈與輪胎",
        "label": i18n.t('assets["輪圈與輪胎"]')
      },
      {
        "value": "動力研究室",
        "label": i18n.t('assets["動力研究室"]')
      },
      {
        "value": "WRC / F1 賽事討論",
        "label": i18n.t('assets["WRC / F1 賽事討論"]')
      },
      {
        "value": "汽機車七嘴八舌",
        "label": i18n.t('assets["汽機車七嘴八舌"]')
      },
      {
        "value": "汽車消費經驗分享",
        "label": i18n.t('assets["汽車消費經驗分享"]')
      },
      {
        "value": "ETC專區",
        "label": i18n.t('assets["ETC專區"]')
      },
      {
        "value": "購車菜單",
        "label": i18n.t('assets["購車菜單"]')
      }
    ]
  }
];



/**
 * 空白占位符
 */
export const EmptyPlacementText = '--';

/**
 * 公共主页-帖子审核状态
 */
export const PostCheckStatusOptions = [
  {
    label: i18n.t('assets["审核中"]'),
    prop: 'pending',
    value: 0,
    tagType: 'default'
  },
  // {
  //   label: "审核通过",
  //   prop: "pass",
  //   value: 1,
  //   tagType: "success"
  // },
  {
    label: i18n.t('assets["已拒绝"]'),
    prop: 'refused',
    value: 2,
    tagType: 'danger'
  }
];

/**
 * 公共主页-帖子发送状态
 */
export const PostSubmitStatusOptions = [
  {
    label: i18n.t('assets["未发送"]'),
    prop: 'unpost',
    value: 0,
    tagType: 'default'
  },
  {
    label: i18n.t('assets["发送成功"]'),
    prop: 'posted',
    value: 1,
    tagType: 'success'
  },
  {
    label: i18n.t('assets["超时未发送"]'),
    prop: 'timeout',
    value: 2,
    tagType: 'danger'
  },
  {
    label: i18n.t('assets["发送失败"]'),
    prop: 'error',
    value: 3,
    tagType: 'danger'
  },
  {
    label: i18n.t('assets["队列中"]'),
    prop: 'other',
    value: 4,
    tagType: 'default'
  }
];

/**
 * 排序方式
 */
export const SortOptions = [
  { label: i18n.t('assets["按粉丝数量降序排序"]'), value: 'desc_fans' },
  { label: i18n.t('assets["按粉丝数量升序排序"]'), value: 'asc_fans' },
  { label: i18n.t('assets["按最近活动时间降序排序"]'), value: 'desc_time' },
  { label: i18n.t('assets["按最近活动时间升序排序"]'), value: 'asc_time' },
  { label: i18n.t('assets["按最近入库时间排序"]'), value: 'reg_time' }
];

/**
 * 出库状态方式
 */
export const exstatus = [
  { label: i18n.t('assets["已出库"]'), value: true },
  { label: i18n.t('assets["未出库"]'), value: false }
];
/**
 * 账号筛选条件
 */
export const FilterTypeOptionsCrops = [
  // { label: i18n.t('assets["关键词"]'), value: 'key_word' },
  { label: i18n.t('assets["按名称"]'), value: 'account' },
  { label: i18n.t('assets["按手机号"]'), value: 'phone' },
  { label: i18n.t('assets["按邮箱"]'), value: 'email' },
  { label: i18n.t('assets["按标签"]'), value: 'tag_id' }
];

export const FilterTypeOptions = [
  { label: i18n.t('assets["关键词"]'), value: 'key_word' },
  { label: i18n.t('assets["按名称"]'), value: 'account' },
  { label: i18n.t('assets["按手机号"]'), value: 'phone' },
  { label: i18n.t('assets["按邮箱"]'), value: 'email' },
];
/**
 * 系统资源内容库培育状态
 */
export const BreedStatusMap = {
  0: i18n.t('assets["培育中"]'),
  1: i18n.t('assets["培育完成"]'),
  2: i18n.t('assets["培育失败"]')
};

/**
 * 文章态度 示例：[{ value: 0, label: "正面" }]
 */
export const AttitudeOptions = [
  { value: -1, label: i18n.t('assets["不限"]'), prop: 'none' },
  { value: 0, label: i18n.t('assets["正面"]'), prop: 'positive' },
  { value: 1, label: i18n.t('assets["负面"]'), prop: 'negative' },
  { value: 2, label: i18n.t('assets["中立"]'), prop: 'neutral' }
];

export const AttitudeMap = {};
export const AttitudeLabelMap = {};
AttitudeOptions.forEach(item => {
  AttitudeMap[item.prop] = item.value;
  AttitudeLabelMap[item.value] = item.label;
});

/**
 * 内容库来源枚举值
 */
export const ArticalSourceList = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  // {
  //   label: i18n.t('dataCenter["采集自动添加"]'),
  //   value: '采集自动添加'
  // },
  {
    label: i18n.t('dataCenter["内容生成"]'),
    value: '内容生成'
  },
  {
    label: i18n.t('dataCenter["手动生成"]'),
    value: '手动生成'
  },
  {
    label: i18n.t('dataCenter["培育目标库导入"]'),
    value: '培育目标库导入'
  },

];

/**
 * 评论来源枚举值
 */
export const CommentSourceList = [
  {
    label: i18n.t('dataCenter["手动生成"]'),
    value: '手动生成'
  },
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
 {
    label: i18n.t('dataCenter["策略任务"]'),
    value: '策略任务'
  },
];
/**
 * 文件来源枚举值
 */
export const fileSourceList = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  {
    label: i18n.t('dataCenter["策略任务"]'),
    value: '策略任务'
  },
  // {
  //   label: i18n.t('dataCenter["培育目标库导入"]'),
  //   value: '培育目标库导入'
  // },
];
/**
 * 剩下来源枚举值
 */
export const SourceList = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  {
    label: i18n.t('dataCenter["采集自动添加"]'),
    value: '采集自动添加'
  },
  {
    label: i18n.t('dataCenter["培育目标库导入"]'),
    value: '培育目标库导入'
  },
  {
    label: i18n.t('dataCenter["策略任务"]'),
    value: '策略任务'
  },
];


/**
 * 内容库来源枚举值
 */
export const ArticalSourceListBreed = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  {
    label: i18n.t('dataCenter["采集自动添加"]'),
    value: '采集自动添加'
  },
  // {
  //   label: i18n.t('dataCenter["内容生成"]'),
  //   value: '内容生成'
  // },
  // {
  //   label: i18n.t('dataCenter["手动添加"]'),
  //   value: '手动添加'
  // },

];
/**
 * 内容库来源枚举值
 */
export const ArticalSourceListMission = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },

  {
    label: i18n.t('dataCenter["培育目标库导入"]'),
    value: '培育目标库导入'
  },
  {
    label: i18n.t('dataCenter["采集自动添加"]'),
    value: '采集自动添加'
  },
  {
    label: i18n.t('dataCenter["策略任务"]'),
    value: '策略任务'
  },
  // {
  //   label: i18n.t('dataCenter["内容生成"]'),
  //   value: '内容生成'
  // },
  // {
  //   label: i18n.t('dataCenter["手动添加"]'),
  //   value: '手动添加'
  // },

];
/**
 * 评论来源枚举值
 */
export const CommentSourceListBreed = [
  {
    label: i18n.t('dataCenter["手动生成"]'),
    value: '手动生成'
  },
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },

];
/**
 * 文件来源枚举值
 */
export const fileSourceListBreed = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },

];
/**
 * 剩下来源枚举值
 */
export const SourceListBreed = [
  {
    label: i18n.t('assets["文件导入"]'),
    value: '文件导入'
  },
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  {
    label: i18n.t('dataCenter["采集自动添加"]'),
    value: '采集自动添加'
  },

];
export const topicSourceBreed = [
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  {
    label: i18n.t('dataCenter["采集自动添加"]'),
    value: '采集自动添加'
  },
]
export const topicSourceMission = [
  {
    label: i18n.t('dataCenter["网络资源导入"]'),
    value: '网络资源导入'
  },
  {
    label: i18n.t('dataCenter["培育目标库导入"]'),
    value: '培育目标库导入'
  },
  {
    label: i18n.t('dataCenter["采集自动添加"]'),
    value: '采集自动添加'
  },
  {
    label: i18n.t('dataCenter["策略任务"]'),
    value: '策略任务'
  },
]
const newDic = [
  i18n.t('assets["美"]'),
  i18n.t('assets["澳"]'),
  i18n.t('assets["加"]'),
  i18n.t('assets["日"]'),
  i18n.t('assets["台"]'),
  i18n.t('assets["港"]'),
  i18n.t('assets["港独"]'),
  i18n.t('assets["台独"]'),
  i18n.t('assets["疆独"]'),
  i18n.t('assets["藏独"]'),
  i18n.t('assets["法轮功"]'),
  i18n.t('assets["民运"]')
];
const newsArr = [...TargetSiteLocationList, ...newDic]

export const PlatformTreeOption = [
  {
    value: PlatformOptionList,
    label: i18n.t('assets["社交平台"]'),
    children: PlatformSourceList
  },
  {
    value: TargetSiteAllList,
    label: i18n.t('assets["新闻网站"]'),
    children: newsArr.map(location => ({
      value: TargetSiteLocationListSecond[location],
      label: location,
      children: TargetSiteLocationMap[location]
    }))
  }
];
export const PlatformSocialTreeOption = [
  {
    value: PlatformOptionList,
    label: i18n.t('assets["社交平台"]'),
    children: PlatformSourceList
  },

];

/**
 * 消息中心 - 消息类型
 */
export const MessageTypes = {
  SYSTEM: 'MISSION_ASSIGN', // 系统消息
  MISSION: 'MISSION_STATUS', // 任务执行
  SUMMARY: 'MISSION_REPORT', // 汇总报告
  REPORT: 'DAILY_REPORT', // 日报
  WEEKREPORT: 'WEEK_REPORT', // 周报
  MONTHREPORT: 'MONTH_REPORT', // 月报
  AUDIT: 'AUDIT_NOTIFICATION', // 审核通知
  WEBRTC: 'WEBRTC',
  PLATFORMRULESADVERSARIALDETECTION: 'PLATFORM_RULES_ADVERSARIAL_DETECTION',
  ALL: 'ALL' // 全部
};

/**
 * 账号数量限制
 */
export const AccountLimit = {
  MIN: 1,
  MAX: 99999
};

/**
 * 粉丝数数量限制
 */
export const FansLimit = {
  MIN: 0,
  MAX: *********
};

/**
 * 文章数数量限制
 */
export const ArticleLimit = {
  MIN: 0,
  MAX: *********
};

export const MessageCodeMap = {
  'MISSION_ASSIGN': 2,
  'MISSION_STATUS': 4,
  'MISSION_REPORT': 3,
  'DAILY_REPORT': 1,
  'AUDIT_NOTIFICATION': 0
};

/**
 * 标签长度限制
 */
export const TagLimit = {
  MIN: 0,
  MAX: 20
};

/**
 * 标题长度限制
 */
export const TitleLimit = {
  MIN: 0,
  MAX: 50
};

/**
 * 终端设备类型
 */
export const TerminalType = {
  'ARM': i18n.t('assets["ARM模拟器"]'),
  'X86': i18n.t('assets["X86模拟器"]'),
  'PHONE': i18n.t('assets["真机"]')
}

/**
 * 终端设备状态
 */
export const TerminalState = {
  'RUN': i18n.t('assets["开机"]'),
  'STOP': i18n.t('assets["关机"]'),
  'TASK': i18n.t('assets["任务中"]')
}

/**
 * 标签类型对应的各个可见板块
 * */
export const tagPrimary = [
  {
    value: '角色培育通用',
    relation: {
      label: '角色培育通用',
      category: ['虚拟角色', '舆论知识库', '网络资源', '监控账号'],
      visible_items: [
        'account_receive_watch', 'virtual_character_self_account_watch', 'virtual_character_others_account_watch',
        'virtual_character_character_set_watch', 'public_knowledge_resource_watch', 'net_resource_resource_watch',
        'net_resource_account_list_watch', 'net_resource_exam_user_watch', 'monitor_center_monitor_account_watch'
      ],
      icon: 'task-role',
    }
  },
  {
    value: '职业通用',
    relation: {
      label: '职业通用',
      category: ['虚拟角色', '舆论知识库', '网络资源', '监控账号'],
      visible_items: [
        'account_receive_watch', 'virtual_character_self_account_watch', 'virtual_character_others_account_watch',
        'virtual_character_character_set_watch', 'public_knowledge_resource_watch', 'net_resource_resource_watch',
        'net_resource_account_list_watch', 'net_resource_exam_user_watch', 'monitor_center_monitor_account_watch'
      ],
      icon: 'task-role',
    }
  },
  {
    value: '任务通用',
    relation: {
      label: '任务通用',
      category: ['创建虚拟账号', '虚拟角色培育', '舆论采集', '策略任务'],
      visible_items: ['mission_center_register_watch', 'mission_center_bring_watch', 'mission_center_collect_watch', 'mission_center_manual_watch'],
      icon: 'task-list'
    }
  },
  {
    value: '系统资源通用',
    relation: {
      label: '系统资源通用',
      category: ['手机号管理', '邮箱管理'],
      visible_items: ['system_resources_phone_list_watch', 'system_resources_email_list_watch'],
      icon: 'sys_resource_blue'
    }
  }
]

/* 标签 primary 别名*/
export const tagPrimaryAlias = {
  '任务通用': 'missionTag',
  '职业通用': 'professionTag',
  '角色培育通用': 'userCulTag',
  '系统资源通用': 'sysResTag'
}

/**
 * 各个可见板块对应的可见页面
 */
export const tagCategories = [
  {
    label: '任务中心',
    value: 'mission_center',
    children: [
      {
        label: '创建虚拟账号',
        value: 'mission_center_register_watch'
      },
      {
        label: '虚拟角色培育',
        value: 'mission_center_bring_watch'
      },
      {
        label: '舆论采集',
        value: 'mission_center_collect_watch'
      },
      {
        label: '策略任务',
        value: 'mission_center_manual_watch'
      }
    ]
  },
  {
    label: '虚拟角色',
    value: 'virtual_character',
    children: [
      {
        label: '接收账号列表',
        value: 'account_receive_watch'
      },
      {
        label: '自主创建账号列表',
        value: 'virtual_character_self_account_watch'
      },
      {
        label: '非合作手段账号列表',
        value: 'virtual_character_others_account_watch'
      },
      {
        label: '人设列表',
        value: 'virtual_character_character_set_watch'
      }
    ]
  },
  {
    label: '网络资源',
    value: 'net_resource',
    children: [
      {
        label: '资源库',
        value: 'net_resource_resource_watch'
      },
      {
        label: '网络账号资源',
        value: 'net_resource_account_list_watch'
      },
      {
        label: '榜样用户',
        value: 'net_resource_exam_user_watch'
      }
    ]
  },
  {
    label: '系统资源',
    value: 'system_resources',
    children: [
      {
        label: 'IP池管理',
        value: 'system_resources_ip_watch'
      },
      {
        label: '手机号管理',
        value: 'system_resources_phone_list_watch'
      },
      {
        label: '邮箱管理',
        value: 'system_resources_email_list_watch'
      }
    ]
  },
  {
    label: '舆论知识库',
    value: 'public_knowledge',
    children: [
      {
        label: '资源库',
        value: 'public_knowledge_resource_watch'
      }
    ]
  },
  {
    label: '监控账号',
    value: 'monitor_center',
    children: [
      {
        label: '监控账号',
        value: 'monitor_center_monitor_account_watch'
      }
    ]
  }
]

// 主要的标签模块
export const primaryTagDict = {}; // 文字:value
export const levelTagDict = {};   // value:文字
tagCategories.forEach(item => {
  primaryTagDict[item.label] = item.children.map(a => a.value)
  levelTagDict[item.value] = item.label;
  item.children.forEach(one => {
    levelTagDict[one.value] = one.label
  })
})

// 审核管理-审核状态
export const auditStatus = [
  { label: i18n.t('assets["待审核"]'), value: 'PENDING' },
  { label: i18n.t('assets["已审核"]'), value: 'APPROVED' },
  { label: i18n.t('assets["未通过"]'), value: 'REJECTED' },
]

// 舆论采集-任务类型
export const collectTaskOptions = [
  { label: i18n.t('assets["自主创建采集"]'), value: 'GATHER' },
  { label: i18n.t('assets["热点事件采集"]'), value: 'HOT' },
  { label: i18n.t('assets["策略任务采集"]'), value: 'MANUAL' }
]

// 舆论采集-采集类型
export const collectTypeOptions = [
  { label:i18n.t('dataCenter["普通采集"]'), value: 'normalBreed' },
  { label:i18n.t('dataCenter["培育语料采集"]'), value: 'precisionBreed' }
]

// 不能做策略任务的原因
export const inManualReason = {
  facebook: i18n.t('assets["Facebook平台：账号培育时长不足7天，无法支持策略任务"]'),
  twitter: i18n.t('assets["Twitter平台：账号培育时长不足7天，无法支持策略任务"]'),
  reddit: i18n.t('assets["Reddit平台：账号培育时长不足5天，无法支持策略任务"]'),
  threads: i18n.t('assets["Threads平台：发帖文章数不足3篇，无法支持策略任务"]'),
}

/**
 *  cross-module 备注(标记) 的所有类型
 *  label 是唯一标识, 不能使用翻译
 *  labelText 是用来展示的文字，需要使用 i18n 来翻译
 **/
export const crossTagCategories = [
  {
    'label': '任务中心',
    'labelText': i18n.t('unit["任务中心"]'),
    'value': 'mission_center',
    'children': [
      {
        'label': '策略任务',
        'labelText': i18n.t('unit["策略任务"]'),
        'value': 'mission_center_manual_watch'
      }
    ]
  },
  {
    'label': '虚拟角色',
    'labelText': i18n.t('unit["虚拟角色"]'),
    'value': 'virtual_character',
    'children': [
      {
        'label': '自主创建账号',
        'labelText': i18n.t('unit["自主创建账号"]'),
        'value': 'virtual_character_self_account_watch'
      },
      {
        'label': '人设列表',
        'labelText': i18n.t('unit["人设列表"]'),
        'value': 'virtual_character_character_set_watch'
      },
    ]
  },
  {
    'label': '系统资源',
    'labelText': i18n.t('unit["系统资源"]'),
    'value': 'system_resources',
    'children': [
      {
        'label': '手机号管理',
        'labelText': i18n.t('unit["手机号管理"]'),
        'value': 'system_resources_phone_list_watch'
      },
      {
        'label': '邮箱管理',
        'labelText': i18n.t('unit["邮箱管理"]'),
        'value': 'system_resources_email_list_watch'
      }
    ]
  },
  {
    'label': '数据中心',
    'labelText': i18n.t('unit["数据中心"]'),
    'value': 'data_center',
    'children': [
      {
        'label': '素材库管理',
        'labelText': i18n.t('unit["素材库管理"]'),
        'value': 'public_opinion_resource_watch'
      }
    ]
  },
]
export const crossTagLabelDict = {};
crossTagCategories.forEach(item => {
  if (item.children && item.children.length > 0){
    crossTagLabelDict[item.label] = []; //初始化父级
    item.children.forEach(a => {
      crossTagLabelDict[item.label].push(a.value) // 父级载入子级value
      //子级载入自己的value
      if (crossTagLabelDict[a.label] && crossTagLabelDict[a.label].length>0){
        crossTagLabelDict[a.label].push(a.value)
      }
      else {
        crossTagLabelDict[a.label] = [a.value]
      }
    })
  }
  else {
    crossTagLabelDict[item.label] = [item.value];
  }
})

export const groupPlatformOptions = [
  { label: 'Facebook', value: 'facebook' },
  { label: 'Reddit', value: 'reddit' },
  { label: 'Quora', value: 'quora' },
  { label: 'Mewe', value: 'mewe' },
  { label: 'Band', value: 'band' },
  { label: 'Eyny', value: 'eyny' },
  { label: 'Pixnet', value: 'pixnet' },
  { label: 'Gamer', value: 'gamer' },

]
export const boardPlatformOptions = [
  { label: 'Whatsapp', value: 'whatsapp' },
  { label: 'Telegram', value: 'telegram' }
]
export const topicListSource = [
  { label: 'Instagram', value: 'instagram' },
  { label: 'Quora', value: 'quora' },
  { label: 'Twttier', value: 'twitter' },
  { label: i18n.t('assets["知乎"]'), value: 'zhihu' },
  { label: 'TikTok', value: 'tiktok' }
]
export const homePageSource = [
  { label: 'Facebook', value: 'facebook' },
  { label: 'Mewe', value: 'mewe' },

]

/* 同屏消息码表 */
export const  SameScreenMessageCode= {
  ENV_CHECK: { name: i18n.t('assets["同屏环境检测"]'), index: 0 },
  GET_PROXY: { name: i18n.t('assets["获取代理"]'), index: 1 },
  OCCUPY_RESOURCE: { name: i18n.t('assets["申请执行资源"]'), index: 2 },
  SEND_TO_EXE: { name:i18n.t('assets["投送到执行节点"]'), index: 3 },
  EXE_RECEIVE_TASK: { name: i18n.t('assets["执行机接收到任务"]'), index: 4 },
  OPEN_DEVICE: { name: i18n.t('assets["打开容器"]'), index: 5 },
  CHECK_PROXY:{name: i18n.t('assets["代理网络连接"]'), index: 6},
  USER_CONTROL: { name: i18n.t('assets["用户接入"]'), index: 7 },
}

export const SameScreenStatus=[
  { label: i18n.t('assets["已创建"]'), value: 'CREATE' },
  { label: i18n.t('unit["进行中"]'), value: 'RUN' },
  { label: i18n.t('assets["设备正在被控制"]'), value: 'CONTROL' },
  { label: i18n.t('assets["设备被占用"]'), value: 'OCCUPY' },
  { label: i18n.t('components["成功"]'), value: 'SUCCESS' },
  { label: i18n.t('accountCenter["失败"]'), value: 'FAILURE' },
  { label: i18n.t('unit["关闭中"]'), value: 'CLOSING' },
  { label: i18n.t('assets["关闭成功"]'), value: 'CLOSED' }
]
