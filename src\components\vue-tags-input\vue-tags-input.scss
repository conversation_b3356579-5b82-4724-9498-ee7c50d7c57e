$primary: #3569e7;
$error: #e54d42;
$success: #68cd86;
$warn: #ffb648;

@font-face {
  font-family: 'icomoon';
  src:  url('./assets/fonts/icomoon.eot?7grlse');
  src:  url('./assets/fonts/icomoon.eot?7grlse#iefix') format('embedded-opentype'),
    url('./assets/fonts/icomoon.ttf?7grlse') format('truetype'),
    url('./assets/fonts/icomoon.woff?7grlse') format('woff');
  font-weight: normal;
  font-style: normal;
}

[class^="ti-icon-"], [class*=" ti-icon-"] {
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ti-icon-check:before {
  content: "\e902";
}

.ti-icon-close:before {
  content: "\e901";
}

.ti-icon-undo:before {
  content: "\e900";
}

ul {
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}

*, *:before, *:after {
  box-sizing: border-box;
}

input:focus {
  outline: none;
}

input[disabled] {
  border-color: #e4e7ed;
  cursor: not-allowed;
  background-color: #f5f7fa;
}

.vue-tags-input {
  max-width: 450px;
  position: relative;
  background-color: #fff;
}
.ti-disabled{

  .ti-input{
    border-color: #e4e7ed;
    cursor: not-allowed;
    background-color: #f5f7fa;
    input::placeholder{
      color: #f5f7fa;
    }
  }

}

div.vue-tags-input.disabled {
  opacity: 0.5;
  border-color: #e4e7ed;
  cursor: not-allowed;
  background-color: #f5f7fa;

  * {
    cursor: default;
  }
}

.ti-input {
  background-color: #fafcff;
  border-radius: 8px;
  border: solid 1px #c2d0e2;
  display: flex;
  padding: 4px;
  flex-wrap: wrap;
}

.ti-tags {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  line-height: 1em;
}

.ti-tag {
  background-color: $primary;
  color: #fff;
  border-radius: 2px;
  display: flex;
  padding: 3px 5px;
  margin: 2px;
  font-size: .85em;

  &:focus {
    outline: none;
  }

  .ti-content {
    display: flex;
    align-items: center;
  }

  .ti-tag-center {
    position: relative;
  }

  span {
    line-height: .85em;
  }

  span.ti-hidden {
    padding-left: 14px;
    visibility: hidden;
    height: 0px;
    white-space: pre;
  }

  .ti-actions {
    margin-left: 2px;
    display: flex;
    align-items: center;
    font-size: 1.15em;

    i {
      cursor: pointer;
    }
  }

  &:last-child {
    margin-right: 4px;
  }

  &.ti-invalid, &.ti-tag.ti-deletion-mark {
    background-color: $error;
  }
}

.ti-new-tag-input-wrapper {
  display: flex;
  flex: 1 0 auto;
  padding: 3px 5px;
  margin: 2px;
  font-size: .85em;

  input {
    flex: 1 0 auto;
    min-width: 100px;
    border: none;
    padding: 0px;
    margin: 0px;
  }
}

.ti-new-tag-input {
  line-height: initial;
}

.ti-autocomplete {
  border: 1px solid #ccc;
  border-top: none;
  position: absolute;
  width: 100%;
  background-color: #fff;
  z-index: 20;
}

.ti-item > div {
  cursor: pointer;
  padding: 3px 6px;
  width: 100%;
}

.ti-selected-item {
  background-color: $primary;
  color: #fff;
}
