<template>
  <div
    class="template-card"
    :class="[selected && 'is-selected', isChild && 'is-child']"
  >
    <p class="template-card__title">
      <titan-tooltip
        :content="templName"
        :disabled="!templName"
      >
        <span :class="{ title: true,childTitle: isChild }">{{ templName }}</span>
      </titan-tooltip>
      <svg-icon
        v-show="isChild"
        class="icon"
        :icon-class="platform[0]"
      />
    </p>
    <div
      v-if="!isChild"
      class="platforms text-overflow-ellipsis"
    >
      <template v-for="item in platform">
        <titan-tooltip
          :key="item"
          :content="allPlatformMap[item] ? allPlatformMap[item].name: ''"
        >
          <svg-icon
            :key="item"
            class="icon platform-icon"
            :icon-class="item"
          />
        </titan-tooltip>
      </template>
      <span v-if="platformShow && platformShow.length > 5">
        <svg-icon
          class="icon platform-icon"
          icon-class="omit"
        />
      </span>
    </div>
    <p
      v-if="isChild"
      class="template-card__create-info"
      v-text="calcCreateInfo(templ)"
    />
    <el-tag
      v-else
      :type="templ.is_system_template === 1 ? 'info' : 'primary'"
      class="template-card__tag"
      size="mini"
    >
      {{ templ.is_system_template === 1 ? $t('components. 系统模板') : $t('components. 自定义模板') }}
    </el-tag>
    <div class="actions">
      <aim-action
        :list="templStepList"
        :is-child="isChild"
      />
    </div>
    <el-button
      v-if="showPreviewBtn"
      plain
      type="primary"
      class="template-card__preview"
      size="mini"
      round
      @click.stop="preview(templ)"
    >
      {{ $t('components. 预览') }}
    </el-button>
    <div
      v-if="showCheckBox"
      v-permission="$qx['mission_center_manual_template_manage']"
      class="template-card__radio"
      @click.stop="handleChecked"
    />
    <titan-tooltip
      v-if="showCopy"
      :content="$t('components. 复制')"
    >
      <div
        class="fa fa-clone template-card__copy"
        @click.stop="handleCopy"
      />
    </titan-tooltip>
  </div>
</template>

<script>
import { formatTime } from '@/utils/time-utils';
import { PlatformMap } from '@/assets/libs/enum';
import { mapState } from 'vuex';
import AimAction from '@/views/mission/components/aimAction/index.vue';
export default {
  name: 'TemplCard',
  components: { AimAction },
  props: {
    type: {
      type: String,
      default: 'parent'
    },
    templ: {
      type: Object,
      default: () => ({})
    },
    showTag: {
      type: Boolean,
      default: false
    },
    showCheckBox: {
      type: Boolean,
      default: false
    },
    showCopy: {
      type: Boolean,
      default: true
    },
    showPreviewBtn: {
      type: Boolean,
      default: true
    },
    showDeleteBtn: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      key: '',
      chooseItem: null
    };
  },
  computed: {
    templName() {
      return this.templ.name;
    },
    templStepList() {
      if (this.templ.taskActions !== null) {
        return JSON.parse(this.templ.taskActions);
      } else {
        return [];
      }
    },
    ...mapState({ platformMap: state => state.platform }),
    allPlatformMap() {
      return this.platformMap.platforms;
    },
    PlatformMap() {
      return PlatformMap;
    },
    platform() {
      if (this.templ.platform === null) {
        return [];
      } else if (this.templ.platform.indexOf(' ') === -1) {
        const data = [];
        data.push(this.templ.platform);
        return data;
      } else {
        return this.templ.platform.split(' ', 5);
      }
    },
    platformShow() {
      if (this.templ.platform === null) {
        return [];
      } else if (this.templ.platform.indexOf(' ') === -1) {
        const data = [];
        data.push(this.templ.platform);
        return data;
      } else {
        return this.templ.platform.split(' ');
      }
    },
    showSystemTag() {
      return this.showTag && this.templ.is_system_template === 1;
    },
    isChild() {
      return this.type === 'child';
    }
    // platform() {
    //   if (this.templ.platform) { return this.templ.platform; }
    //   return '';
    // }
  },
  methods: {
    handleChecked() {
      this.$emit('handleChecked', this.templ);
    },
    // 复制
    handleCopy() {
      this.$emit('copy', this.templ.id);
    },
    handleDel() {
      this.$emit('handleDel', this.templ);
    },
    preview(item) {
      if (this.type === 'parent') {
        window.open(`/#/mission/template-detail/${item.id}?back=false`);
      } else {
        window.open(`/#/mission/template-sub-detail/${item.id}?back=false&id=${item.id}`);
      }
    },
    calcCreateInfo(temp) {
      return `${temp.creator.userName}${this.$t('components[" 创建于"]')}：${formatTime(temp.createTime, 'fmt:YD')}`;
    }
  }
};
</script>

<style lang="scss" scoped>

  .platforms {
    margin: 13px 0px;
    display: flex;
    min-height: 24px;
    .platform-icon {
      width: 24px;
      height: 24px;
      border-radius: 15px;
      &:not(:last-child) {
        margin-right: 10px;
      }
    }
  }
  .text-overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .actions {
    height: 50px;
  }
  .template-card {
    position: relative;
    width: 290px;
    height: 130px;
    padding: 7px 20px 18px 20px;
    font-size: 14px;
    color: #303133;
    box-sizing: border-box;
      background-color: rgba(255, 255, 255, 0.6);
      box-shadow: 0px 0px 6px 0px
        rgba(185, 201, 217, 0.4);
      border-radius: 8px;
      border: solid 1px #dce9f9;
    cursor: pointer;
    &.is-child {
      width: 250px !important;
      height: 140px;
      .template-card__create-info {
        color: #909399;
      }
      .template-card__preview {
        left: 20px;
        bottom: 14px;
      }
    }
    &:hover {
      border-color: #3569e7;
    }
    &.is-selected {
      box-shadow: 0 0px 3px 3px rgba(51, 129, 208, 0.3);
      border-color: #3569e7;
      .template-card__radio {
        text-align: center;
        background-color: #409eff;
        border-color: #409eff;
        &:after {
          color: #ffffff;
          content: "\e6da";
          font-family: element-icons !important;
          font-weight: 700;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          vertical-align: top;
          display: inline-block;
        }
      }
    }
    &__delete {
      position: absolute;
      top: 20px;
      right: 20px;
      color: #ff274b;
      font-size: 20px;
      &:hover {
        color: #ef9595
      }
    }
    &__title {
      display: flex;
      align-items: center;

      .title{
        max-width: calc(100% - 45px);
        height: 40px;
        margin-right: 3px;
        line-height: 20px;
        font-size: 16px;
        color: #606266;
        @include utils-ellipsis(2)
      }
      .childTitle{
        max-width: calc(100% - 45px);
        margin-right: 3px;
        height: 30px;
        line-height: 30px;
        font-size: 16px;
        color: #606266;
        @include utils-ellipsis(1)
      }
      .icon {
        width: 20px;
        height: 20px;
      }
    }
    &__desc {
      @include utils-ellipsis(2)
    }
    &__child-content {
      margin: 15px 0 0;
    }
    &__tag {
        min-width: 91px;
        height: 24px;
        background-color: #e5efff;
        border-radius: 6px;
        border: solid 1px #c4d6fb;
        text-align: center;
    }
    &__radio {
      position: absolute;
      top: 15px;
      right: 20px;
      width: 16px;
      height: 16px;
      border-radius: 2px;
      border: solid 1px #c4c4c4;
    }
    &__copy {
      position: absolute;
      bottom: 15px;
      right: 20px;
      font-size: 16px;
      border-radius: 2px;
      color: #c4c4c4;
      &:hover {
        color: #3569e7;
      }
    }
    &__preview {
      position: absolute;
      right: 13px;
      bottom: 16px;
      width: 48px;
      height: 22px;
      padding: 0;
      line-height: 17px;
      background-color: #ffffff;
      border-radius: 12px;
      border: solid 1px #3569e7;
    }
  }
</style>
