<template>
  <titan-dialog
    v-if="showDialog"
    :title="$t('dataCenter.查找策略目标库')"
    :visible.sync="showDialog"
    top="20px"
    width="85%"
    :config="{
      showCancel: true,
      showConfirm: true
    }"
    append-to-body
    @close="handleClose"
    @cancel="handleCancel"
    @confirm="handleConfirm"
  >
    <find-mission-target
      v-if="showDialog"
      :platform="platform"
      :selected-schema="selectedSchema"
      :selected-user="selectedUser"
      @choose="chooseList"
    />
  </titan-dialog>
</template>

<script>
import FindMissionTarget from '@/schema/custom/FindMissionTarget';

export default {
  name: 'TargetAccountDialog',
  components: { FindMissionTarget },
  props: {

    selectedUser: {
      type: Array,
      default: () => []
    },
    searchParams: {
      type: Object,
      default: () => ({})
    },
    platform: {
      type: String,
      default: ''
    },
    selectedSchema: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return { visible: false, checkList: [] };
  },
  computed: {
    showDialog: {
      set(val) {
        this.$emit('update:visible', val);
      },
      get() {
        return this.visible;
      }
    }
  },

  methods: {
    handleCancel() {
      this.visible = false;
      this.$emit('cancel');
    },
    handleClose() {
      this.visible = false;
      this.$emit('close');
    },
    chooseList(val) {
      this.checkList = val;
    },
    handleConfirm() {
      this.visible = false;
      this.$emit('confirm', this.checkList);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
