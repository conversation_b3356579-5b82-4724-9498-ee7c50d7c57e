import http from '@/api/request';
import URL from '../urls';

/**
 * 同屏内容
 * --------------------------------------------------
 */
export class sameScreenConnectionContentAPI {
  /**
   * 获取大模型
   */
  static getllms(params) {
    return http.get(URL.sameScreenConnection.getllms, { params });
  }

  /**
   * 智能交互
   */
  static llmQa(data) {
    return http.post(URL.sameScreenConnection.llmQa, data);
  }

  /**
   * 轮训获取任务
   */
  static getTask(params) {
    return http.get(URL.sameScreenConnection.getTask, { params });
  }
}
export class relationAPI {
  /**
   * 关系拓扑图
   */
  static getRelationList(data) {
    return http.post(URL.sameScreenConnection.getRelationList, data);
  }

  static associationSingleSave(data) {
    return http.post(URL.sameScreenConnection.associationSingleSave, data);
  }

  static mainPageSingleSave(data) {
    return http.post(URL.sameScreenConnection.mainPageSingleSave, data);
  }

  static informationAdd(data) {
    return http.post(URL.sameScreenConnection.informationAdd, data);
  }

  static googleHostList(data) {
    return http.post(URL.sameScreenConnection.googleHostList, data);
  }

  // 好友发展趋势
  static getFriendLineData(data) {
    return http.post(URL.sameScreenConnection.getFriendLineData, data);
  }
}
