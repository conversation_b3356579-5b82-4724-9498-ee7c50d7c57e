/**
 * 权限映射
 */
const PrivilegeMap = {
  "account_center": "account_center",
  "data_center": "data_center",
  "account_distribute_manage": "account_distribute_manage",
  "resource_center": "resource_center",
  "support_center": "support_center",
  "combat_center": "combat_center",
  "user_center": "user_center",
  "create_account_watch": "create_account_watch",
  "create_account_manage": "create_account_manage",
  "virtual_account": "virtual_account",
  "virtual_account_breed_watch": "virtual_account_breed_watch",
  "virtual_account_breed_manage": "virtual_account_breed_manage",
  "virtual_account_record": "virtual_account_record",
  "virtual_account_list_watch": "virtual_account_list_watch",
  "virtual_account_list_manage": "virtual_account_list_manage",
  "virtual_account_receive_list_watch": "virtual_account_receive_list_watch",
  "virtual_account_receive_list_manage": "virtual_account_receive_list_manage",
  'virtual_account_sensitive_info_mange':'virtual_account_sensitive_info_mange',
  "uncooperative_account_list_watch": "uncooperative_account_list_watch",
  "uncooperative_account_list_manage": "uncooperative_account_list_manage",
  "virtual_group_watch": "virtual_group_watch",
  "virtual_group_manage": "virtual_group_manage",
  "person_list_watch": "person_list_watch",
  "person_list_manage": "person_list_manage",
  "account_export_record_watch": "account_export_record_watch",
  "account_del_record_watch": "account_del_record_watch",
  "account_distribute_record_watch": "account_distribute_record_watch",
  "gather_report_watch": "gather_report_watch",
  "network_resource_watch": "network_resource_watch",
  "network_resource_manage": "network_resource_manage",
  "public_opinion_resource_watch": "public_opinion_resource_watch",
  "public_opinion_resource_manage": "public_opinion_resource_manage",
  "resource_account_watch": "resource_account_watch",
  "resource_account_manage": "resource_account_manage",
  "example_user_watch": "example_user_watch",
  "example_user_manage": "example_user_manage",
  "monitor_account_watch": "monitor_account_watch",
  "monitor_account_manage": "monitor_account_manage",
  "intelligent_analysis": "intelligent_analysis",
  "gather_task_watch": "gather_task_watch",
  "gather_task_manage": "gather_task_manage",
  "hot_events_watch": "hot_events_watch",
  "monitor_account_report_watch": "monitor_account_report_watch",
  "community_profile_watch": "community_profile_watch",
  'tags': 'tags',
  'tags_module_watch': 'tags_module_watch',
  'tags_module_manage': 'tags_module_manage',
  'tags_cross_module_watch': 'tags_cross_module_watch',
  'tags_cross_module_manage': 'tags_cross_module_manage',
  "change_password": "change_password",
  "version_record": "version_record",
  "examina_center": "examina_center",
  "system_test_page": "system_test_page",
  "version_record_watch": "version_record_watch",
  "version_record_manage": "version_record_manage",
  "examina_center_watch": "examina_center_watch",
  "examina_center_manage": "examina_center_manage",
  "mission_center_manual_watch": "mission_center_manual_watch",
  "mission_center_manual_watch_only_me": "mission_center_manual_watch_only_me",
  "mission_center_manual_manage": "mission_center_manual_manage",
  "mission_center_homepage_watch": "mission_center_homepage_watch",
  "mission_center_homepage_manage": "mission_center_homepage_manage",
  "mission_center_manual_template": "mission_center_manual_template",
  "mission_center_manual_template_watch": "mission_center_manual_template_watch",
  "mission_center_manual_template_manage": "mission_center_manual_template_manage",
  "mission_center_manual_actions_watch": "mission_center_manual_actions_watch",
  "mission_center_manual_actions_manage": "mission_center_manual_actions_manage",

  "system_support_center_report_watch": "system_support_center_report_watch",
  "cloud_server_watch": "cloud_server_watch",
  "cloud_server_manage": "cloud_server_manage",
  "system_set": "system_set",
  "system_manage_page": "system_manage_page",
  "system_setting_import_rule_watch": "system_setting_import_rule_watch",
  "system_setting_import_rule_manage": "system_setting_import_rule_manage",
  "system_manage_depart_and_person_watch": "system_manage_depart_and_person_watch",
  "system_manage_department_manage": "system_manage_department_manage",
  "system_manage_person_manage": "system_manage_person_manage",
  "system_manage_multi_department": "system_manage_multi_department",
  "system_manage_role_watch": "system_manage_role_watch",
  "system_manage_role_manage": "system_manage_role_manage",
  "resource_report_watch": "resource_report_watch",
  "proxy_list_watch": "proxy_list_watch",
  "proxy_list_manage": "proxy_list_manage",
  "phone_list_watch": "phone_list_watch",
  "phone_list_manage": "phone_list_manage",
  "email_list_watch": "email_list_watch",
  "email_list_manage": "email_list_manage",
  "system_setting_proxy_rule_watch":"system_setting_proxy_rule_watch",
  "system_setting_proxy_rule_manage":"system_setting_proxy_rule_manage",
  "system_setting_visible_platform_watch":"system_setting_visible_platform_watch",
  "system_setting_visible_platform_manage":"system_setting_visible_platform_manage",
  'notice_center_watch': 'notice_center_watch',
  "breed_policy":"breed_policy",
  "breed_policy_watch":"breed_policy_watch",
  "breed_policy_manage":"breed_policy_manage",
  "gather_data_examine_watch": "gather_data_examine_watch",
  "gather_data_examine_manage": "gather_data_examine_manage",
  "remote_control":"remote_control",
  "user_focus_events_watch": "user_focus_events_watch",
  "user_focus_events_manage": "user_focus_events_manage",
  "export_record_watch": "export_record_watch",
  "import_record_watch": "import_record_watch",
  "active_section_watch": "active_section_watch",
  "active_section_manage": "active_section_manage",
  "bt_msg_center": "bt_msg_center",
  "bt_msg_center_view_self":"bt_msg_center_view_self",
  "bt_msg_center_view_all":"bt_msg_center_view_all",
  'tag_resource_use_info_watch':'tag_resource_use_info_watch',
  'account_report_watch':'account_report_watch',
  'dept_can_register_config': 'dept_can_register_config',
  'virtual_character_can_delete':'virtual_character_can_delete',
  'sales_record_watch': 'sales_record_watch',
  'system_login_log': 'system_login_log',
  'tag_list_manage': 'tag_list_manage',
  'account_record_manage': 'account_record_manage',
  'net_resource_resource_manage': 'net_resource_resource_manage',
  'public_knowledgemultimedia_manage': 'public_knowledgemultimedia_manage',
  'net_resource_account_list_manage': 'net_resource_account_list_manage',
  'virtual_character_self_account_watch': 'virtual_character_self_account_watch',
  'mission_center_collect_watch': 'mission_center_collect_watch',
  'mission_center_copytask_manage': 'mission_center_copytask_manage',
  'screen_display_use_log': 'screen_display_use_log',
  'virtual_character_groups_watch': 'virtual_character_groups_watch',
  'virtual_character_groups_manage': 'virtual_character_groups_manage'
};

export default PrivilegeMap
