<template>
  <div class="toolbar__wrapper">
    <div class="page-name">
      <div class="title">
        <span v-if="!isDesktop">{{ pageTitle.join(" / ") }}</span>
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          class="inline-block bread"
        >
          <el-breadcrumb-item
            v-for="(item, index) in toolbarRouteAppend.data || []"
            :key="index"
          >
            {{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <i
          v-if="toolbarRouteAppend.showRoute"
          class="el-icon-close"
          style="cursor: pointer"
          @click="$router.push(toolbarRouteAppend.route)"
        />
      </div>
      <slot
        style="flex: 0 1 auto"
        name="left"
      />
    </div>
    <div
      class="slot-box"
      style="flex: 1 1 0%"
    >
      <slot name="center" />
    </div>
    <div
      class="slot-box"
      style="flex: 0 1 auto"
    >
      <slot name="right" />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'Toolbar',
  computed: {
    ...mapState(['toolbarRouteAppend']),
    isDesktop() {
      return this.$route.name === 'DESKTOP-HOME';
    },
    matched() {
      return this.$route.matched;
    },
    pageTitle() {
      const list = [];
      this.matched.forEach(item => {
        if ((item.meta || {}).title) {
          list.push(this.$t(item.meta.title));
        }
      });
      return list;
    }
  }
};
</script>

<style scoped lang="scss">
.toolbar__wrapper {
  height: 36px;
  min-height: 36px;
  max-height: 36px;
  // background-color: #edf4ff;
  width: 100%;
  padding-right: 32px;
  display: flex;
  .page-name {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex: 0 1 auto;

    &-icon {
      margin-right: 0.5rem;
    }
  }
  .title {
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    color: #8993a7;
    user-select: none;
    margin: 0 0.5rem;
    align-items: center;
  }
  .bread {
    margin: 0 1rem;
  }
  .inline-block {
    display: inline-block;
  }
  .slot-box {
    // background-color: #edf4ff;
    display: flex;
    align-items: center;
  }
}
</style>
