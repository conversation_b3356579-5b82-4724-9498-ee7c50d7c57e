import request from '@/api/request';
import URL from '../urls';

/**
 * 群体画像
 */
export class GroupPortraitAPI {
  // 群体列表
  static getGroupPortraits() {
    return request({
      url: URL.group.groupPortraits,
      method: 'post'
    });
  }

  // 群体的政治倾向
  static getGroupPoliticTrend(data) {
    return request({
      url: URL.group.politicTrend,
      method: 'post',
      data
    });
  }

  // 群体的情绪分析
  static getGroupEmotionTrend(data) {
    return request({
      url: URL.group.emotionTrend,
      method: 'post',
      data
    });
  }

  // 群体感兴趣的热词
  static getGroupTopics(data) {
    return request({
      url: URL.group.topics,
      method: 'post',
      data
    });
  }

  // 群体的平台占比
  static getGroupDistribute(data) {
    return request({
      url: URL.group.distribute,
      method: 'post',
      data
    });
  }

  // 群体的平台占比
  // static getGroupAccount(data) {
  //   return request({
  //     url: URL.group.distribute,
  //     method: 'post',
  //     data
  //   });
  // }

  // 群体的活跃用户
  static getGroupActiveUsers(data) {
    return request({
      url: URL.group.active_users,
      method: 'post',
      data
    });
  }

  // 群体的用户
  static getGroupUsers(data) {
    return request({
      url: URL.group.users,
      method: 'post',
      data
    });
  }

  // 群体的热门文章
  static getGroupPosts(data) {
    return request({
      url: URL.group.posts,
      method: 'post',
      data
    });
  }

  static addUser(data) {
    return request({
      url: URL.group.add_user,
      method: 'post',
      data
    });
  }

  static getGroupAccountReport(data) {
    return request({
      url: URL.group.getGroupAccountReport,
      method: 'post',
      data
    });
  }
}

