<template>
  <div
    class="selected-person-container"
    :style="{ 'height': height }"
  >
    <div class="list">
      <div
        v-if="list && list.length > 0"
        class="remove"
        @click="removeAll"
      >
        <i class="el-icon-delete" />{{ $t('components. 清空') }}
      </div>
      <div
        v-for="item in list"
        :key="item.id"
        class="selected-person"
      >
        <titan-avatar
          size="40px"
          :src="item.account_photo"
          :label="item.account"
          :config="{
            labelStyle: {
              fontSize: '18px'
            }
          }"
        />
        <span>{{ item.account }}</span>
        <i
          class="el-icon-circle-close"
          @click="handleCancel(item)"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SelectedPerson',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '30%'
    }
  },
  data() {
    return {};
  },
  methods: {
    handleCancel(item) {
      this.$emit('cancel', item);
    },
    removeAll() {
      this.$emit('removeAll');
    }
  }
};
</script>

<style scoped lang="scss">
  .selected-person-container{
    flex-grow: 0;
    .list{
      height: 100%;
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      position: relative;
      .remove{
        cursor: pointer;
        color: #F56C6C;
        position: absolute;
        right: 10px;
        top: 0;
        border: 1px solid #F56C6C;
        border-radius: 4px;
        padding: 2px 4px;
        font-size: 12px;
        i{
          margin-right: 4px;
        }
        &:hover{
          color: #FFFFFF;
          background: #F56C6C;
        }
      }
      .selected-person{
        display: flex;
        height: 50px;
        border: 1px solid #dddddd;
        padding: 0 10px;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        width: 250px;
        margin-bottom: 10px;
        margin-right: 10px;
        span{
          font-size: 16px;
          width: 130px;
          display: inline-block;
          @include utils-ellipsis();
        }
        i{
          cursor: pointer;
          color: #F56C6C;
          font-size: 20px;
        }
      }
    }
  }
</style>
