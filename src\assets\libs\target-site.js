import i18n from '@/lang';
export const TargetSite = {
  "polls": {
    "name": i18n.t('targetSite["趋势民意调查股份有限公司"]'),
    "url": "http://www.polls.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "fsr": {
    "name": i18n.t('targetSite["山水民意研究股份有限公司"]'),
    "url": "http://www.fsr.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "tisr": {
    "name": i18n.t('targetSite["台湾指标民调"]'),
    "url": "http://www.tisr.com.tw",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "taot": {
    "name": i18n.t('targetSite["台湾人权促进会"]'),
    "url": "http://www.tahr.org.tw",
    "type": [
      "keyword"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "tvbs": {
    "name": i18n.t('targetSite["TVBS新闻网"]'),
    "url": "http://news.tvbs.com.tw",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ogilvy": {
    "name": i18n.t('targetSite["奥美公共关系股份有限公司"]'),
    "url": "http://www.ogilvy.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ftv": {
    "name": i18n.t('targetSite["民视"]'),
    "url": "http://www.ftvnews.com.twnews",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "realsurvey": {
    "name": i18n.t('targetSite["全国公信力民意调查"]'),
    "url": "http://www.realsurvey.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "bcc": {
    "name": i18n.t('targetSite["中国广播公司"]'),
    "url": "http://www.bcc.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "rti": {
    "name": i18n.t('targetSite["中央广播电台(RTI)"]'),
    "url": "http://www.rti.org.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "chi": {
    "name": i18n.t('targetSite["台湾基督长老教会济南教会"]'),
    "url": "http://www.chi-nanchurch.tw",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "pot": {
    "name": i18n.t('targetSite["基督长老教会"]'),
    "url": "http://www.pct.org.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "tac": {
    "name": i18n.t('targetSite["苹果日报"]'),
    "url": "https://tw.appledaily.com",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "tfot": {
    "name": i18n.t('targetSite["台湾民主基金会"]'),
    "url": "http://www.tfd.org.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "jetgo": {
    "name": i18n.t('targetSite["战国策传播集团"]'),
    "url": "http://jetgo.com.tw",
    "type": [
      "hot",
      "keyword"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ect": {
    "name": i18n.t('targetSite["大纪元时报台湾版"]'),
    "url": "https://www.epochtimes.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "nent": {
    "name": i18n.t('targetSite["东升电视台"]'),
    "url": "https://www.ebc.net.tw",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "stm": {
    "name": i18n.t('targetSite["风传媒"]'),
    "url": "https://www.storm.mg",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "pts": {
    "name": i18n.t('targetSite["公视"]'),
    "url": "https://www.pts.org.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "vct": {
    "name": i18n.t('targetSite["汉声广播电台"]'),
    "url": "https://www.voh.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "cct": {
    "name": i18n.t('targetSite["华视"]'),
    "url": "https://www.cts.com.tw",
    "type": [
      "hot",
      "audio"
    ],
    "collectionResultTypes": [
      "article",
      "audio"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "udn": {
    "name": i18n.t('targetSite["联合新闻网"]'),
    "url": "https://udn.com",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "setn": {
    "name": i18n.t('targetSite["三立新闻网"]'),
    "url": "https://www.settv.com.tw/#!",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ttct": {
    "name": i18n.t('targetSite["台视"]'),
    "url": "https://www.ttv.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "tao": {
    "name": i18n.t('targetSite["台湾关怀中国人权联盟"]'),
    "url": "https://www.tachr.org",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ctc": {
    "name": i18n.t('targetSite["中时电子报"]'),
    "url": "https://www.chinatimes.com",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "nlct": {
    "name": i18n.t('targetSite["自由时报"]'),
    "url": "https://www.ltn.com.tw",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "mpp": {
    "name": i18n.t('targetSite["民众日报"]'),
    "url": "http://www.mypeople.tw",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "merit": {
    "name": i18n.t('targetSite["人间福报"]'),
    "url": "http://www.merit-times.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ustv": {
    "name": i18n.t('targetSite["USTV-非凡电视台"]'),
    "url": "https://www.ustv.com.tw",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "ectworld": {
    "label": "大纪元时报国际版",
    "name": i18n.t('targetSite["大纪元时报国际版"]'),
    "url": "https//www.epochtimes.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "can": {
    "label": "台湾中央通讯社",
    "name": i18n.t('targetSite["台湾中央通讯社"]'),
    "url": "https.//www.cna.com.tw/",
    "type": [
      "hot",
      "keyword"
    ],
    "location": i18n.t('targetSite["中国台湾"]')
  },
  "mjoo": {
    "name": i18n.t('targetSite["共同民主党"]'),
    "url": "https://theminjoo.kr",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["韩国"]')
  },
  "kr": {
    "name": i18n.t('targetSite["青瓦台"]'),
    "url": "http://www.korea.kr",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["韩国"]')
  },
  "kantei": {
    "name": i18n.t('targetSite["首相官邸"]'),
    "url": "https://www.kantei.go.jp",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["日本"]')
  },
  "mofa": {
    "name": i18n.t('targetSite["外务省"]'),
    "url": "https://www.mofa.go.jp",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["日本"]')
  },
  "dphk": {
    "name": i18n.t('targetSite["民主党"]'),
    "url": "https://www.dphk.org",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国香港"]')
  },
  "hku": {
    "name": i18n.t('targetSite["香港大学电子邮件服务器"]'),
    "url": "https://hku.hk",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国香港"]')
  },
  "tjp": {
    "name": i18n.t('targetSite["雅加达邮报"]'),
    "url": "https://www.thejakartapost.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["其他"]')
  },
  "rci": {
    "name": i18n.t('targetSite["印尼《共和国报》"]'),
    "url": "https://www.republika.co.id",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["其他"]')
  },
  "dsc": {
    "name": i18n.t('targetSite["越共电子报"]'),
    "url": "http://cn.dangcongsan.vn",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["越南"]')
  },
  "ttxvn": {
    "name": i18n.t('targetSite["越南通讯社"]'),
    "url": "https://zh.vietnamplus.vn",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["越南"]')
  },
  "vietnamnet": {
    "name": i18n.t('targetSite["Vietnamnet"]'),
    "url": "https://english.vietnamnet.vn",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["越南"]')
  },
  "zee": {
    "name": i18n.t('targetSite["ZEE电视台新闻频道"]'),
    "url": "https://zeenews.india.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["印度"]')
  },
  "india": {
    "name": i18n.t('targetSite["《今日印度》"]'),
    "url": "https://www.indiatoday.in/",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["印度"]')
  },
  "tianshan": {
    "name": i18n.t('targetSite["天山新闻网"]'),
    "url": "http://news.ts.cn",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国新疆"]')
  },
  "xizangzhiye": {
    "name": i18n.t('targetSite["藏人行政中央官方中文网"]'),
    "url": "https://xizang-zhiye.org",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国西藏"]')
  },
  "savetibet": {
    "name": i18n.t('targetSite["国际西藏运动"]'),
    "url": "https://savetibet.org",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中国西藏"]')
  },
  "usatoday": {
    "name": i18n.t('targetSite["今日美国"]'),
    "url": "https://www.usatoday.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "bbc": {
    "name": i18n.t('targetSite["BBC"]'),
    "url": "https://www.bbc.co.uk",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "voachina": {
    "name": i18n.t('targetSite["美国之音中文网"]'),
    "url": "https://www.voachinese.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "cn_nytimes": {
    "name": i18n.t('targetSite["纽约时报中文网"]'),
    "url": "https://cn.nytimes.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "astanatimes": {
    "name": i18n.t('targetSite["哈萨克斯坦新闻网"]'),
    "url": "https://astanatimes.com",
    "type": [
      "keyword",
      "hot"
    ],
    "location": i18n.t('targetSite["中亚"]')
  },
  "uza": {
    "name": i18n.t('targetSite["乌兹别克斯坦民族新闻社"]'),
    "url": "http://uza.uz",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中亚"]')
  },
  "aljazeera": {
    "label": "半岛电视台",
    "name": i18n.t('targetSite["半岛电视台"]'),
    "url": "https://www.aljazeera.com/",
    "type": [
      "hot",
      "keyword"
    ],
    "location": i18n.t('targetSite["中亚"]')
  },
  "tass": {
    "name": i18n.t('targetSite["塔斯社_tass"]'),
    "url": "http://tass.ru",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
  },
  "rt": {
    "name": i18n.t('targetSite["今日俄罗斯"]'),
    "url": "https://www.rt.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
  },
  "ria": {
    "name": i18n.t('targetSite["RIA Novosti 俄罗斯国际新闻通讯社"]'),
    "url": "https://ria.ru/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
  },
  "tasscom": {
    "name": i18n.t('targetSite["塔斯社_tasscom"]'),
    "url": "https://tass.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
  },
  "inquirer": {
    "name": i18n.t('targetSite["菲律宾每日问讯者报"]'),
    "url": "https://www.inquirer.net/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["菲律宾"]')
  },
  "swissinfo": {
    "label": "法新社",
    "location": i18n.t('targetSite["俄罗斯"]'),
    "name": i18n.t('targetSite["法新社"]'),
    "type": [
      "hot",
      "keyword"
    ],
    "url": "https://www.swissinfo.ch/chi/afp"
  },
  "philstar": {
    "name": i18n.t('targetSite["菲律宾星报"]'),
    "url": "https://www.philstar.com",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["菲律宾"]')
  },
  "gnlm": {
    "name": i18n.t('targetSite["新光报"]'),
    "url": "https://www.gnlm.com.mm/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["缅甸"]')
  },
  "mmgp": {
    "name": i18n.t('targetSite["金凤凰报"]'),
    "url": "http://www.mmgpmedia.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["缅甸"]')
  },
  "crph": {
    "name": i18n.t('targetSite["缅甸CRPH"]'),
    "url": "https://crphmyanmar.org/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["缅甸"]')
  },
  "mmnow": {
    "name": i18n.t('targetSite["缅甸现在"]'),
    "url": "https://www.myanmar-now.org/en",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["缅甸"]')
  },
  "iwdy": {
    "name": i18n.t('targetSite["伊洛瓦底新闻杂志"]'),
    "url": "https://www.irrawaddy.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["缅甸"]')
  },
  "apnews": {
    "name": i18n.t('targetSite["美联社"]'),
    "url": "https://apnews.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "reuters": {
    "name": i18n.t('targetSite["路透社"]'),
    "url": "https://www.reuters.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "yahoo_jp": {
    "name": i18n.t('targetSite["雅虎日本"]'),
    "url": "https://www.yahoo.co.jp",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["日本"]')
  },
  "thestandnews": {
    "name": i18n.t('targetSite["立场新闻"]'),
    "url": "https://www.thestandnews.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国香港"]')
  },
  "appledaily": {
    "name": i18n.t('targetSite["苹果日报"]'),
    "url": "https://www.appledaily.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国香港"]')
  },
  "hk01": {
    "name": i18n.t('targetSite["香港01"]'),
    "url": "https://www.hk01.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["中国香港"]')
  },
  "cnn": {
    "name": i18n.t('targetSite["CNN"]'),
    "url": "https://us.cnn.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "thehill": {
    "name": i18n.t('targetSite["国会山"]'),
    "url": "https://thehill.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "washingtonpost": {
    "name": i18n.t('targetSite["华盛顿邮报"]'),
    "url": "https://washingtonpost.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "foxnews": {
    "name": i18n.t('targetSite["福克斯新闻"]'),
    "url": "https://www.foxnews.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "nytimes": {
    "name": i18n.t('targetSite["纽约时报"]'),
    "url": "https://www.nytimes.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "kokangnet": {
    "name": i18n.t('targetSite["果敢新闻中心"]'),
    "url": "https://www.kokangnet.com/?cat=2/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["缅甸"]')
  },
  "absnews": {
    "name": i18n.t('targetSite["美国广播公司新闻"]'),
    "url": "https://abcnews.go.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "nbcnews": {
    "name": i18n.t('targetSite["全国广播公司新闻"]'),
    "url": "https://www.nbcnews.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "theguardian": {
    "name": i18n.t('targetSite["卫报"]'),
    "url": "https://www.theguardian.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "politico": {
    "name": i18n.t('targetSite["POLITICO"]'),
    "url": "https://www.politico.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "voa": {
    "name": i18n.t('targetSite["美国之音"]'),
    "url": "https://www.voanews.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "npr": {
    "name": i18n.t('targetSite["全国公共广播电台"]'),
    "url": "https://www.npr.org/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "bloomberg": {
    "name": i18n.t('targetSite["彭博社"]'),
    "url": "https://www.bloomberg.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "nationinterst": {
    "name": i18n.t('targetSite["国家利益"]'),
    "url": "https://www.nationinterst.org/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "newsweek": {
    "name": i18n.t('targetSite["新闻周刊"]'),
    "url": "https://www.newsweek.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "whitehouse": {
    "name": i18n.t('targetSite["白宫官网"]'),
    "url": "https://www.whitehouse.gov/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "stategov": {
    "name": i18n.t('targetSite["国务院官网"]'),
    "url": "https://www.stategov.gov/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "stripes": {
    "name": i18n.t('targetSite["星条旗报"]'),
    "url": "https://www.stripes.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "militarytimes": {
    "name": i18n.t('targetSite["军事时报"]'),
    "url": "https://www.militarytimes.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "military": {
    "name": i18n.t('targetSite["军事报"]'),
    "url": "https://www.military.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "defensenews": {
    "name": i18n.t('targetSite["防务新闻"]'),
    "url": "https://www.defensenews.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "defenseone": {
    "name": i18n.t('targetSite["防务一号"]'),
    "url": "https://www.defenseone.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "defenceblog": {
    "name": i18n.t('targetSite["防务博客"]'),
    "url": "https://www.defenceblog.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "pacom": {
    "name": i18n.t('targetSite["印太司令部官网"]'),
    "url": "https://www.pacom.mil/Media/News/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "navy": {
    "name": i18n.t('targetSite["海军官网"]'),
    "url": "https://www.navy.mil/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "usni": {
    "name": i18n.t('targetSite["海军研究所"]'),
    "url": "https://www.usni.org/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
  "defence": {
    "label": "美国国防部",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["美国国防部"]'),
    "type": [
      "hot"
    ],
    "url": "https://www.defence.gov/"
  },
  "rand": {
    "label": "兰德公司",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["兰德公司"]'),
    "type": [
      "hot"
    ],
    "url": "https://www.rand.org/"
  },
  "cfr": {
    "label": "美国战略与国际研究中心",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["美国战略与国际研究中心"]'),
    "type": [
      "hot"
    ],
    "url": "https://www.cfr.org/"
  },
  "diamil": {
    "label": "美国国防情报局",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["美国国防情报局"]'),
    "type": [
      "hot"
    ],
    "url": "https://www.dia.mil/"
  },
  "wsjus": {
    "label": "华尔街日报美版",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["华尔街日报美版"]'),
    "type": [
      "hot",
      "keyword"
    ],
    "url": "https://www.wsj.com/news/us"
  },
  "wsjworld": {
    "label": "华尔街日报国际版",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["华尔街日报国际版"]'),
    "type": [
      "hot",
      "keyword"
    ],
    "url": "https://cn.wsj.com/zh-hant/news/world"
  },
  "cbnc": {
    "label": "CNBC",
    "location": i18n.t('targetSite["欧美"]'),
    "name": i18n.t('targetSite["CNBC"]'),
    "type": [
      "hot",
      "keyword"
    ],
    "url": "https://www.cnbc.com"
  },
  "krtime": {
    "name": i18n.t('targetSite["韩国时报"]'),
    "url": "https://www.koreatimes.co.kr/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["韩国"]')
  },
  "krjoins": {
    "name": i18n.t('targetSite["中央日报"]'),
    "url": "https://www.joins.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["韩国"]')
  },
  "chosun": {
    "name": i18n.t('targetSite["朝鲜日报"]'),
    "url": "https://www.chosun.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["韩国"]')
  },
  "kyodonews": {
    "name": i18n.t('targetSite["日本共同网"]'),
    "url": "https://china.kyodonews.net/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["日本"]')
  },
  "tokyonp": {
    "name": i18n.t('targetSite["东京新闻"]'),
    "url": "https://www.tokyo-np.co.jp/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["日本"]')
  },
  "hokkaidonp": {
    "name": i18n.t('targetSite["北海道新闻"]'),
    "url": "https://www.hokkaido-np.co.jp/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["日本"]')
  },
  "singtaousa": {
    "name": i18n.t('targetSite["星岛日报"]'),
    "url": "https://www.singtaousa.com/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["中国香港"]')
  },
  "kompas": {
    "name": i18n.t('targetSite["罗盘报"]'),
    "url": "https://www.kompas.com/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["印尼"]')
  },
  "okezone": {
    "name": i18n.t('targetSite["印尼新闻与信息在线"]'),
    "url": "https://www.okezone.com/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["印尼"]')
  },
  "hepingribao": {
    "name": i18n.t('targetSite["印尼和平日报"]'),
    "url": "http://www.hepingribao.com/",
    "type": [
      "hot"
    ],
    location: i18n.t('targetSite["印尼"]')
  },
  "vnexpress": {
    "name": i18n.t('targetSite["越南快讯"]'),
    "url": "https://vnexpress.net/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["越南"]')
  },
  "uniindia": {
    "name": i18n.t('targetSite["印度联合新闻社"]'),
    "url": "http://www.uniindia.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["印度"]')
  },
  "ians": {
    "name": i18n.t('targetSite["印度亚洲新闻社"]'),
    "url": "https://ians.in/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["印度"]')
  },
  "timesofindia": {
    "name": i18n.t('targetSite["印度时报"]'),
    "url": "https://timesofindia.indiatimes.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["印度"]')
  },
  "shangbao": {
    "name": i18n.t('targetSite["菲律宾商报"]'),
    "url": "http://www.shangbao.com.ph/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["菲律宾"]')
  },
  "mbph": {
    "name": i18n.t('targetSite["马尼拉公报"]'),
    "url": "https://mb.com.ph/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["菲律宾"]')
  },
  "feitalks": {
    "name": i18n.t('targetSite["菲聊不可"]'),
    "url": "https://feitalks.com/",
    "type": [
      "hot"
    ],
    "location": i18n.t('targetSite["菲律宾"]')
  },
  "hotspot": {
    "name": i18n.t('targetSite["大马热点"]'),
    "url": "https://hotspot.com.my/article",
    "location": i18n.t('targetSite["马来西亚"]'),
    "type": [
      "hot"
    ]
  },
  "zaobao": {
    "label": "联合早报",
    "location": i18n.t('targetSite["新加坡"]'),
    "name": i18n.t('targetSite["联合早报"]'),
    "type": [
      "hot",
      "keyword"
    ],
    "url": "https://www.zaobao.com.sg/realtime/world"
  },
  "rfi": {
    "label": "法广网-中文",
    "location": i18n.t('targetSite["中国大陆"]'),
    "name": i18n.t('targetSite["法广网-中文"]'),
    "type": [
      "hot"
    ],
    "url": "https://www.rfi.fr/cn"
  },
  "rfa": {
    "label": "自由亚洲电台",
    "location": i18n.t('targetSite["中国大陆"]'),
    "name": i18n.t('targetSite["自由亚洲电台"]'),
    "type": [
      "hot"
    ],
    "url": "https://www.rfa.org/mandarin/"
  },
  "radiosputnik": {
    "name": i18n.t('targetSite["俄罗斯之声"]'),
    "url": "https://www.rus.ruvr.ru/",
    "type": [
        "keyword",
        "hot"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
  },
"lemonde": {
    "name": i18n.t('targetSite["世界报"]'),
    "url": "https://www.lemonde.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lefigaro": {
    "name": i18n.t('targetSite["费加罗报"]'),
    "url": "https://lefigaro.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"leparisien": {
    "name": i18n.t('targetSite["巴黎人报"]'),
    "url": "https://www.leparisien.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"20minutes": {
    "name": i18n.t('targetSite["20分钟"]'),
    "url": "https://www.20minutes.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"actu": {
    "name": i18n.t('targetSite["实况报"]'),
    "url": "https://actu.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"ladepeche": {
    "name": i18n.t('targetSite["拉德佩什报"]'),
    "url": "https://www.ladepeche.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"sudouest": {
    "name": i18n.t('targetSite["西南报"]'),
    "url": "https://www.sudouest.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lindependant": {
    "name": i18n.t('targetSite["独立报"]'),
    "url": "https://www.lindependant.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"midilibre": {
    "name": i18n.t('targetSite["中度自由"]'),
    "url": "https://www.midilibre.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"ledauphine": {
    "name": i18n.t('targetSite["自由多芬"]'),
    "url": "https://www.ledauphine.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"liberation": {
    "name": i18n.t('targetSite["自由报"]'),
    "url": "https://www.liberation.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"huffingtonpost": {
    "name": i18n.t('targetSite["赫芬顿邮报"]'),
    "url": "https://www.huffingtonpost.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"leprogres": {
    "name": i18n.t('targetSite["进步报"]'),
    "url": "https://www.leprogres.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lavoixdunord": {
    "name": i18n.t('targetSite["北方之声"]'),
    "url": "https://www.lavoixdunord.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"letelegramme": {
    "name": i18n.t('targetSite["电讯报"]'),
    "url": "https://www.letelegramme.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"nouvelobs": {
    "name": i18n.t('targetSite["新观察家"]'),
    "url": "https://www.nouvelobs.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"laprovence": {
    "name": i18n.t('targetSite["普罗旺斯报"]'),
    "url": "https://www.laprovence.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"nicematin": {
    "name": i18n.t('targetSite["尼斯晨报"]'),
    "url": "https://www.nicematin.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"estrepublicain": {
    "name": i18n.t('targetSite["共和国报"]'),
    "url": "https://www.estrepublicain.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lamontagne": {
    "name": i18n.t('targetSite["拉蒙塔尼报"]'),
    "url": "https://www.lamontagne.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"linternaute": {
    "name": i18n.t('targetSite["法国国际报"]'),
    "url": "https://www.linternaute.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lacroix": {
    "name": i18n.t('targetSite["十字报"]'),
    "url": "https://www.la-croix.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"agefi": {
    "name": i18n.t('targetSite["阿格非"]'),
    "url": "https://www.agefi.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lejdd": {
    "name": i18n.t('targetSite["星期日报"]'),
    "url": "https://www.lejdd.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"mondedip": {
    "name": i18n.t('targetSite["外交世界报"]'),
    "url": "https://www.monde-diplomatique.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"humanite": {
    "name": i18n.t('targetSite["人道报"]'),
    "url": "https://www.humanite.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"dnafr": {
    "name": i18n.t('targetSite["阿尔萨斯新时代"]'),
    "url": "https://www.dna.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"corsematin": {
    "name": i18n.t('targetSite["科西嘉晨报"]'),
    "url": "https://www.corsematin.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"parnor": {
    "name": i18n.t('targetSite["巴黎-诺曼底新闻"]'),
    "url": "https://www.paris-normandie.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"charentelibre": {
    "name": i18n.t('targetSite["自由夏朗德省"]'),
    "url": "https://charentelibre.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lepetitjournal": {
    "name": i18n.t('targetSite["小日报"]'),
    "url": "https://lepetitjournal.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"francesoir": {
    "name": i18n.t('targetSite["法国晚报"]'),
    "url": "https://www.francesoir.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lunion": {
    "name": i18n.t('targetSite["联合日报"]'),
    "url": "https://www.lunion.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lyoncapitale": {
    "name": i18n.t('targetSite["里昂报"]'),
    "url": "https://www.lyoncapitale.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
  },
"lalsace": {
    "name": i18n.t('targetSite["阿尔萨斯报"]'),
    "url": "https://www.lalsace.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"planet": {
    "name": i18n.t('targetSite["法国星球"]'),
    "url": "https://www.planet.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"theafricareport": {
    "name": i18n.t('targetSite["非洲报告"]'),
    "url": "https://www.theafricareport.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"larep": {
    "name": i18n.t('targetSite["中心共和国"]'),
    "url": "https://www.larep.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"larepubliquedespyrenees": {
    "name": i18n.t('targetSite["比利牛斯共和国"]'),
    "url": "https://www.larepubliquedespyrenees.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lamarseillaise": {
    "name": i18n.t('targetSite["马赛报"]'),
    "url": "https://www.lamarseillaise.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"fdesouche": {
    "name": i18n.t('targetSite["德苏什报"]'),
    "url": "https://www.fdesouche.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lepopulaire": {
    "name": i18n.t('targetSite["人民报"]'),
    "url": "https://www.lepopulaire.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lechorepublicain": {
    "name": i18n.t('targetSite["共和报"]'),
    "url": "https://www.lechorepublicain.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lejdc": {
    "name": i18n.t('targetSite["中央报"]'),
    "url": "https://www.lejdc.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lopinion": {
    "name": i18n.t('targetSite["意见报"]'),
    "url": "https://www.lopinion.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"leberry": {
    "name": i18n.t('targetSite["共和国浆果"]'),
    "url": "https://www.leberry.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"1jour1actu": {
    "name": i18n.t('targetSite["每日一事实"]'),
    "url": "https://www.1jour1actu.com/actus",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lest-eclair": {
    "name": i18n.t('targetSite["东方闪电"]'),
    "url": "https://www.lest-eclair.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"centre-presse": {
    "name": i18n.t('targetSite["维埃纳中央新闻"]'),
    "url": "https://www.centre-presse.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"leveil": {
    "name": i18n.t('targetSite["上卢瓦河之夜"]'),
    "url": "https://www.leveil.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"revolutionpermanente": {
    "name": i18n.t('targetSite["永久革命网"]'),
    "url": "https://revolutionpermanente.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lecho": {
    "name": i18n.t('targetSite["回声网"]'),
    "url": "https://www.l-echo.info/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"infodujour": {
    "name": i18n.t('targetSite["今日资讯"]'),
    "url": "https://infodujour.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"alpesetmidi": {
    "name": i18n.t('targetSite["阿尔卑斯中南部"]'),
    "url": "https://www.alpes-et-midi.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"libertehebdo": {
    "name": i18n.t('targetSite["法国自由周刊"]'),
    "url": "https://libertehebdo.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"france24": {
    "name": i18n.t('targetSite["法国24"]'),
    "url": "https://www.france24.com/en/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"parismatch": {
    "name": i18n.t('targetSite["巴黎竞赛"]'),
    "url": "https://www.parismatch.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lexpress": {
    "name": i18n.t('targetSite["快报"]'),
    "url": "https://www.lexpress.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"telerama": {
    "name": i18n.t('targetSite["特拉玛"]'),
    "url": "https://www.telerama.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"krmnd": {
    "name": i18n.t('targetSite["韩国国防部官网"]'),
    "url": "https://www.mnd.go.kr/mbshome/mbs/mndEN/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["韩国"]')
},
"krnavy": {
    "name": i18n.t('targetSite["韩国海军官网"]'),
    "url": "https://www.navy.mil.kr/mbshome/mbs/navy/index.do",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["韩国"]')
},
"sainiksamachar": {
    "name": i18n.t('targetSite["印度国防部军事视频"]'),
    "url": "https://sainiksamachar.nic.in/videos-list",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"aajtak": {
    "name": i18n.t('targetSite["印度aajtak新闻军事频道"]'),
    "url": "https://www.aajtak.in/topic/indian-army",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"legionetrangere": {
    "name": i18n.t('targetSite["法国外籍军团官网"]'),
    "url": "https://www.legion-etrangere.com/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"drsd": {
    "name": i18n.t('targetSite["武装部队部长的情报部门"]'),
    "url": "https://www.drsd.defense.gouv.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"armyuk": {
    "name": i18n.t('targetSite["英国国防部"]'),
    "url": "https://www.army.mod.uk/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"flickr": {
    "name": i18n.t('targetSite["Flickr"]'),
    "url": "http://www.flickr.com/photos/defenceimages",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"royalnavy": {
    "name": i18n.t('targetSite["英国海军"]'),
    "url": "https://www.royalnavy.mod.uk/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"rafuk": {
    "name": i18n.t('targetSite["英国空军"]'),
    "url": "https://www.raf.mod.uk/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"namac": {
    "name": i18n.t('targetSite["英国陆军军衔"]'),
    "url": "https://www.nam.ac.uk/explore/british-army-ranks",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"itv": {
    "name": i18n.t('targetSite["英国独立电视台"]'),
    "url": "https://www.itv.com/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"thetimes": {
    "name": i18n.t('targetSite["泰晤士报"]'),
    "url": "http://www.timesonline.co.uk/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"ft": {
    "name": i18n.t('targetSite["金融时报"]'),
    "url": "http://www.ft.com/",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"rutube": {
    "name": i18n.t('targetSite["俄罗斯联邦国防部"]'),
    "url": "https://t.co/NGzHxdHnP0",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
},
"vkontakte": {
    "name": i18n.t('targetSite["俄罗斯联邦国防部(账号主页)"]'),
    "url": "https://vk.com/mil",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["俄罗斯"]')
},
"idfil": {
    "name": i18n.t('targetSite["以色列国防部官网"]'),
    "url": "http://www.idf.il/894-en/Dover.aspx",
    "type": [
        "hot",
        "keyword"
    ],
    "location": i18n.t('targetSite["中亚"]')
},
"slate": {
    "name": i18n.t('targetSite["石板"]'),
    "url": "https://www.slate.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
},
"lepoint": {
    "name": i18n.t('targetSite["勒波因特"]'),
    "url": "https://www.lepoint.fr/",
    "type": [
        "hot"
    ],
    "location": i18n.t('targetSite["欧美"]')
}
}

/**
 * 全量目标网站
 */
export const TargetSiteList = Object.keys(TargetSite);

/**
 * 目标网站完整信息Options
 */
export const TargetSiteOptions = [];

/**
 * 目标网站名称映射表
 */
export const TargetSiteNameMap = {};

/**
 * 目标网站索引排序映射表
 */
export const TargetSiteIdxMap = {};

/**
 * 目标网站采集结果类型映射表
 */
export const TargetSiteResMap = {};

/**
 * 目标网站可选方向列表
 */
export const TargetSiteLocationList = [];
export const TargetSiteLocationListSecond = [];
//所有的新闻平台
export const TargetSiteAllList = [];
/**
 * 生成目标网站方向覆盖表
 */
export const TargetSiteLocationMap = {};

Object.keys(TargetSite).forEach(site => {
  const info = TargetSite[site];

  TargetSiteOptions.push({
    value: site,
    label: info.name,
    ...info
  })
  TargetSiteNameMap[site] = info.name;
  TargetSiteIdxMap[site] = info.sortIdx || 999;
  // 平台采集结果
  TargetSiteResMap[site] = info.collectionResultTypes || ["article"];

  // 网站方向列表
  if (!TargetSiteLocationMap[info.location]) {
    TargetSiteLocationMap[info.location] = [
      {
        label: info.name,
        value: [site]
      }
    ]
  } else {
    TargetSiteLocationMap[info.location].push({
      label: info.name,
      value: [site]
    })
  }
  if (!TargetSiteLocationList.includes(info.location)) {
    TargetSiteLocationList.push(info.location);
  }
  TargetSiteAllList.push(site);
  if (!TargetSiteLocationListSecond[info.location]) {
    TargetSiteLocationListSecond[info.location] = [site]
  } else {
    TargetSiteLocationListSecond[info.location].push(site)
  }
});

/**
 * 关键词-目标网站
 */
export const TargetSiteByKeyword = TargetSiteOptions.filter(site =>
  site.type.includes("keyword")
);

/**
 * 热点-目标网站
 */
export const TargetSiteByHot = TargetSiteOptions.filter(site =>
  site.type.includes("hot")
);

/**
 * 音频-目标网站
 */
export const TargetSiteByAudio = TargetSiteOptions.filter(site =>
  site.type.includes("audio")
);
