import i18n from '@/lang'

/**
 * 系统语言列表
 */
export const LanguageOptions = [
  { value: "ja", label:i18n.t('assets["日语"]') },
  { value: "ko", label: i18n.t('assets["韩语"]') },
  { value: "hi", label: i18n.t('assets["印地语"]') },
  { value: "vi", label: i18n.t('assets["越南语"]') },
  { value: "fil", label:i18n.t('assets["菲律宾语"]') },
  { value: "ru", label: i18n.t('assets["俄语"]') },
  { value: "kk", label: i18n.t('assets["哈萨克语"]') },
  { value: "en", label: i18n.t('assets["英语"]') },
  { value: "zt", label: i18n.t('assets["中文(繁体)"]') },
  { value: "ug", label: i18n.t('assets["维吾尔语"]') },
  { value: "bo", label: i18n.t('assets["藏语"]') },
  { value: "zs", label: i18n.t('assets["中文(简体)"]')},
  { value: "yue", label: i18n.t('assets["粤语"]')},
  { value: "my", label: i18n.t('assets["缅甸语"]') },
  { value: "other", label:i18n.t('assets["其他"]') },
  { value: "nk", label:i18n.t('assets["朝鲜语"]') },
  // { value: "uz", label: "乌兹别克语" }
];

/**
 * 系统语言Label映射
 */
export const LanguageNameMap = {};

LanguageOptions.forEach(item => {
  LanguageNameMap[item.value] = item.label;
})

/**
 * 翻译方向
 */
const _TranslateRuleList = [
  "ja-zs",
  "ko-zs",
  "ru-zs",
  "bo-zs",
  "yue-zs",
  "vi-zs",
  "hi-zs",
  "fil-zs",
  "kk-zs",
  "ug-zs",
  "en-zs",
  "en-ko",
  "en-ja",
  "zs-en",
  "zs-yue",
  "zt-zs",
  "zs-zt",
  "zs-ru",
  "zs-ja",
  "zs-nk",
];

/**
 * 语种映射数组 示例：[{label: '英语 >> 日语', value: 'en-ja'}]
 */
export const CustomTranslateOptions = _TranslateRuleList.map(rule => {
  const item = rule.split("-");
  return {
    label: LanguageNameMap[item[0]] + " >> " + LanguageNameMap[item[1]],
    value: rule
  };
});

/**
 * 获取源语言可翻译的目标语言列表
 * @param {string} sourceLanguage 源语言
 */
export function getTranslateTarget(sourceLanguage) {
  return (
    _TranslateRuleList.filter(item => item.startsWith(sourceLanguage)) || []
  ).map(item => {
    const targetLanguage = item.split("-")[1];
    return {
      value: targetLanguage,
      label: LanguageNameMap[targetLanguage]
    };
  });
}

/**
 * 支持翻译成简体中文的语言列表
 */
export const AvailableLanguageToChinese = [];

_TranslateRuleList.forEach(item => {
  if (item.endsWith("zs")) {
    AvailableLanguageToChinese.push(item.split("-")[0]);
  }
});
