import store from '@/store';

export const permission = {
  inserted(el, binding) {
    const { value } = binding;
    const permissions = store.getters && store.getters.privileges;
    let hasPermission = true;
    if (typeof value === 'string') {
      hasPermission = permissions.includes(value);
    } else if (_.isArray(value) && value.length > 0) {
      hasPermission = permissions.some(a => value.includes(a));
    }
    if (!hasPermission || !value) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  }
};
