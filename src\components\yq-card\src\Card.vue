<template>
  <titan-card
    v-bind="$attrs"
    class="yq-card"
    :class="cardClasses"
    :loading="loading"
    v-on="$listeners"
  >
    <template #title>
      <slot name="title" />
    </template>
    <template #header>
      <slot name="header" />
    </template>
    <template #empty>
      <slot name="empty">
        <titan-placement
          :empty="true"
          :loading="loading"
          v-bind="emptyConfig"
        />
      </slot>
    </template>
    <slot />
  </titan-card>
</template>

<script>
export default {
  name: 'YqCard',

  inheritAttrs: false,
  props: {
    type: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: undefined
    },
    roundCorners: {
      type: Array,
      default: () => [],
      validator: function(value) {
        const params = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        if (
          !Array.isArray(value) ||
          value.some(item => !params.includes(item))
        ) {
          console.error(`prop of borderRadius should be like ${params}`);
          return false;
        }
        return true;
      }
    },
    emptyConfig: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    cardClasses() {
      const res = [];
      if (this.roundCorners.length) {
        res.push(...this.roundCorners.map(item => `round-corner--${item}`));
      }
      if (this.type) {
        res.push(`yq-card--${this.type}`);
      }
      return res;
    }
  }
};
</script>

<style lang="scss">
.yq-card {
  &.round-corner {
    &--top-left {
      border-top-left-radius: 40px;
    }
    &--top-right {
      border-top-right-radius: 40px;
    }
    &--bottom-left {
      border-bottom-left-radius: 40px;
    }
    &--bottom-right {
      border-bottom-right-radius: 40px;
    }
  }

  &--mission {
    div.titan-card__header {
      height: 52px;
      line-height: 52px;
      padding: 0 15px;
      background: rgba($color: #fff, $alpha: 0.25);
      border-bottom: 1px solid #c2d0e2;
      box-sizing: border-box;
      &::before{
        content:'';
        width:3px;
        height: 20px;
        display: inline-block;
        position: absolute;
        left: 0;
        top: 16px;
        background-color: #3569e7;
      }
      .title {
        color: #4f5563;
        font-size: 12px;
        font-weight: bold;
        text-wrap:nowrap;
      }
      .el-tabs {
        .el-tabs__header {
          margin: 0;
          .el-tabs__item {
            font-size: 14px;
            color: #757d88;
            &:first-child {
              padding: 0 10px 0 0;
            }
            &:last-child {
              padding: 0;
            }
          }
          .is-active{
            color: #3569e7;
          }
          .el-tabs__active-bar,
          .el-tabs__nav-wrap::after {
            bottom: 0px;
            background-color: #3569e7;
          }
          .el-tabs__nav-wrap::after {
            background-color: transparent;
          }

        }
      }
    }
    div.titan-card__content {
      padding: 15px;
      background: rgba($color: #fff, $alpha: 0.25);
      height: calc(100% - 52px);
    }
  }

  &--tree {
    div.titan-card__header {
      position: relative;
      height: 58px;
      border-radius: 4px 4px 0 0;
      display: flex;
      align-items: center;
      padding: 0 15px;
      font-size: 16px;
      color: #303133;
      background: #f8f8f9;
      border-bottom: 1px solid #dee2e6;
      &:after {
        position: absolute;
        bottom: -5px;
        left: 20px;

        content: "";

        width: 9px;
        height: 9px;

        background: #f8f8f9;
        border: 1px solid #dee2e6;
        transform: rotate(45deg);
        border-left-color: transparent;
        border-top-color: transparent;

        z-index: 2;
      }

    }
    div.titan-card__content {
      height: calc(100% - 58px);
      padding: 0;
      // .group-item {
      //   cursor: pointer;
      //   height: 48px;
      //   display: flex;
      //   align-items: center;
      //   justify-content: space-between;
      //   padding: 0 16px;
      //   .group-info {
      //     flex: 1;
      //     @include utils-ellipsis(1);
      //   }
      //   .group-ctr {
      //     visibility: hidden;
      //     flex-shrink: 0;
      //     .ctr-icon {
      //       margin-right: 4px;
      //       &:hover {
      //         background-color: #d3e3f3;
      //         border-radius: 4px;
      //       }
      //     }
      //   }
      //   &:hover,
      //   &.is-active {
      //     background-color: #d2e5f8;
      //     color: #3569e7;
      //   }
      //   &:hover {
      //     .group-ctr {
      //       visibility: visible;
      //     }
      //   }
      // }
    }
  }
}
</style>
