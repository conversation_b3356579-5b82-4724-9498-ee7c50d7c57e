<template>
  <div
    class="step-card"
    :style="calcStyle"
  >
    <div class="step-card__icon">
      <i :class="iconClass" />
    </div>
    <titan-tooltip
      effect="light"
      :content="description"
      :disabled="!description"
      :open-delay="100"
    >
      <span class="content-text">
        {{ stepName }}
        {{ stepTime }}
      </span>
    </titan-tooltip>
    <titan-tooltip
      v-if="status === 'FAILURE' && errorStack"
      popper-class="errorStack"
      effect="light"
      :content="errorStack"
      :open-delay="100"
    >
      <i
        class="fa fa-exclamation-circle"
        aria-hidden="true"
      />
    </titan-tooltip>
    <div class="step-card__divider" />
  </div>
</template>

<script>
import { formatTime } from '@/utils/time-utils';

const STATUS_LIST = [
  {
    type: 'SUCCESS',
    icon: 'fa fa-check',
    color: '#0fd2be',
    textColor: '#606266',
    bgColor: '#edfbfa'
  },
  {
    type: 'FAILURE',
    icon: 'fa fa-close',
    color: '#ff274b',
    textColor: '#ff274b',
    bgColor: '#feeeee'
  },
  {
    type: 'CREATE',
    icon: 'fa fa-clock-o',
    color: '#909399',
    textColor: '#909399',
    bgColor: '#ffffff'
  },
  {
    type: 'PAUSED',
    icon: 'fa fa-exclamation',
    color: '#ffa600',
    textColor: '#ffa600',
    bgColor: '#fffaec'
  }
];
export default {
  name: 'StepCard',
  props: {
    step: {
      type: String,
      default: ''
    },
    stepName: {
      type: String,
      default: ''
    },
    time: {
      type: Number,
      default: undefined
    },
    status: {
      type: String,
      default: 'WAIT'
    },
    description: {
      type: String,
      default: ''
    },
    errorStack: {
      type: String,
      default: null
    }
  },
  computed: {
    stepTime() {
      return this.time ? formatTime(this.time, 'fmt:Hs') : '';
    },
    iconClass() {
      const obj = STATUS_LIST.find(o => o.type === this.status) || {};
      return obj['icon'] || 'fa fa-clock-o';
    },
    calcStyle() {
      const obj = STATUS_LIST.find(o => o.type === this.status) || {};
      return {
        '--color': obj['textColor'] || '#909399',
        '--bgColor': obj['bgColor'] || '#fff',
        '--iconColor': obj['color'] || '#909399'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.step-card {
  position: relative;
  height: 28px;
  line-height: 28px;
  white-space: nowrap;

  &__icon {
    position: absolute;
    left: 0;
    top: 5px;
    width: 18px;
    height: 18px;
    line-height: 18px;
    background: var(--iconColor);
    color: #fff;
    border-radius: 18px;
    text-align: center;
  }
  .content-text{
    margin-left: 22px;
    color: var(--color);
  }
  &__divider {
    display: inline-block;
    margin: 0 8px;
    height: 5px;
    width: 8px;
    border-top: 1px dashed #606266;
  }
  .fa-exclamation-circle {
    margin: 5px;
    color: #ff274b;
  }
  &:last-of-type {
    .step-card__divider {
      display: none;
    }
  }
}
</style>

<style>
.errorStack {
  max-height: 400px;
  overflow-y: auto;
}
</style>
