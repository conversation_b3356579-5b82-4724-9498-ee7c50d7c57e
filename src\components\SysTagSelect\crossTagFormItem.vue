<template>
  <div class="cross-tag-span-warp">
    <i
      v-if="showEdit && !isEdit"
      class="fa fa-pencil"
      @click="openEdit"
    />
    <cross-tag-span
      v-if="!isEdit"
      v-loading="loading"
      :list="selectedList"
      :tag-type="tagType"
      :disable-transitions="true"
    />
    <div
      v-else
      class="select-main"
      :class="{ 'have-select-btn': showBtn }"
    >
      <cross-tag-select
        ref="select"
        :tag-ids.sync="ids_select"
        :selected-list="selectedList"
        :allow-create="allowCreate"
        :visible-item="visibleItem"
        :multiple-limit="multipleLimit"
        :cross-tag.sync="crossTag"
        :collapse-tags="collapseTags"
        :placeholder="placeholder"
      />

      <template v-if="showBtn">
        <svg-icon
          icon-class="btn-error"
          @click="cancelTags"
        />
        <div
          class="svg-btn-warp"
          @click="submitTags"
        >
          <svg-icon icon-class="btn-ok" />
          <span>{{ $t('dataCenter.点击确定') }}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import crossTagSpan from './crossTagSpan';
import crossTagSelect from './crossTagSelect';
import { crossTagLabelDict } from '@/assets/libs/enum';
import { TagManagerAPI } from '@/api/modules/tag-manager';
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'CrossTagFormItem',
  components: { crossTagSpan, crossTagSelect },
  props: {
    tagIds: {
      type: Array,
      default: () => []
    },
    allowCreate: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    visibleItem: {
      type: String,
      default: ''
    },
    multipleLimit: {
      type: Number,
      default: () => 0
    },
    showEdit: {
      type: Boolean,
      default: true
    },
    showBtn: {
      type: Boolean,
      default: true
    },
    tagType: {
      type: String,
      default: ''
    },
    byOnline: {
      type: Boolean,
      default: false,
      info: '是否每次当tagIds改变时，都去刷新selectedList，同一视图会多次调用 crossTagFormItem组件时需要将其设置为true'
    },
    placeholder: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      crossTag: [], // 下拉滚动加载的, 属于系统里面已有的备注
      ids_spare: [], // ##SPARE 开头的标签id
      ids_select: [], // 已选择的标签id, 根据权限去掉了##SPARE
      selectedList: [], // 通过历史表单id, 查询出来的标签详情
      loading: false,
      isEditing: false // 当前是否正在编辑
    };
  },
  computed: {
    ...mapGetters(['canImport']),
    isEdit: {
      get() {
        return this.isEditing;
      },
      set(val) {
        this.isEditing = val;
      }
    }
  },
  watch: {
    tagIds: {
      handler(val) {
        if (_.isEmpty(val)) {
          this.crossTag = [];
          this.selectedList = [];
          this.ids_select = [];
          this.ids_spare = [];
          return;
        }

        if (this.byOnline) {
          this.getTagList(true);
          return;
        }

        if (_.isEmpty(this.selectedList)) {
          this.getTagList();
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapMutations('crossTag', ['ADD_TAG_DICT']),
    openEdit() {
      this.isEdit = true;
    },
    async submitTags(callFunc) {
      const dict = {};
      this.crossTag.forEach(item => {
        dict[item.id] = item;
      });
      // 下面是选择的标签
      const add = this.ids_select.filter(item => !dict[item]); // 新增的标签
      const other = this.ids_select.filter(item => dict[item]); // 系统里面已有的标签

      let choose_list = [];
      const visibleItems = crossTagLabelDict[this.visibleItem];
      if (!_.isEmpty(add)) {
        const q = add.map(item => ({
          name: item,
          visible_items: visibleItems,
          simple: true,
          main: '普通标签'
        }));
        const { data: addTagsResult } = await TagManagerAPI.addTag(q);
        // 过滤掉属于其它板块的备注
        const data = addTagsResult.filter(item => item.visible_items.some(a => visibleItems.includes(a)));
        choose_list = data;
        // 如果有其它板块的备注
        if (addTagsResult.length !== data.length) {
          this.$message.warning(this.$t('store.已存在于其它板块的备注不能被添加'));
        }

        // 增加新的备注
        this.ADD_TAG_DICT({ type: this.visibleItem, tagList: data });
      }
      const otherList = other.map(id => dict[id]);
      const allList = otherList.concat(choose_list);
      this.selectedList = _.uniqBy(allList, 'id'); // 给 cross-tag-span 赋值
      const selectIds = this.selectedList.map(a => a.id);
      // 如果有 canImport 权限不做处理, 如果没有, 则需要把##SPARE开头的标签id放回去, 因为不能对 ##SPARE开头的标签进行操作
      const endIds = this.canImport ? selectIds : _.union(this.ids_spare, selectIds);
      // 将得到的新id传递出去
      this.$emit('update:tagIds', endIds);
      this.$emit('updateTagIds', endIds);

      this.isEdit = false;

      // 如果有回调函数
      if (_.isFunction(callFunc)) {
        callFunc(endIds);
      }
    },
    cancelTags() {
      this.isEdit = false;
      // 重置 select的 v-model
      this.ids_select = _.isEmpty(this.selectedList) ? [] : this.selectedList.map(item => item.id);
    },
    async getTagList(online = false) {
      try {
        this.loading = true;
        const q = { id_list: this.tagIds };
        if (online) q.timeStamp = Date.now(); // 多次请求避免被cancel
        const { data } = await TagManagerAPI.getTagList(q);
        // 过滤出指定板块下面的备注
        const visibleItems = crossTagLabelDict[this.visibleItem]; // 当前所属板块的可见区域
        const list = _.isEmpty(data) ? [] : data.filter(item => item.visible_items.some(a => visibleItems.includes(a))); // 只要当前可见区域里面有一个这个备注可以显示的区域则显示出来
        const tags = this.canImport ? [...list] : list.filter(item => !item.name.startsWith('##SPARE'));
        this.crossTag = tags;
        this.selectedList = tags;
        this.ids_select = tags.map(item => item.id);
        this.ids_spare = list.filter(item => item.name.startsWith('##SPARE')).map(item => item.id);
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.cross-tag-span-warp{
  .fa-pencil{
    font-size: 16px;
    color: #3381D0;
    cursor: pointer;
    &:hover{
      color: #2c94fc;
    }
  }
  .svg-icon{
    font-size: 20px;
    cursor: pointer;
    margin-left: 10px;
    &:hover{
      font-size: 21px;
    }
  }
  .svg-btn-warp{
    width: 50px;
    line-height: 14px;
    text-align: center;
    display: inline-block;
    transform: translateY(3px);
    vertical-align: middle;
    cursor: pointer;
    .svg-icon{
      margin: 5px auto auto;
    }
    span{
      display: block;
      font-size: 12px;
      color: #3569e7;
      margin-top: 4px;
    }
    &:hover{
      .svg-icon{
        margin-top: 4px;
        font-size: 21px;
      }
    }
  }
  ::v-deep{
    .select-main{
      .el-select{
        width: 100%;
      }
      &.have-select-btn{
        .el-select{
          width: calc(100% - 82px);
        }
      }
    }
  }
}
</style>
