<template>
  <titan-dialog
    custom-class="post-message"
    :title="$t('components. 发布帖子')"
    :append-to-body="true"
    width="55%"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :config="{
      showConfirm: true,
      confirmText: $t('components. 发送审核'),
      footerAlign: 'center',
      confirmLoading: loading
    }"
    @confirm="sendAudit"
    @cancel="closeDialog"
  >
    <div v-if="routerpage === 'homepage'">
      <div class="textarea-box">
        <titan-form
          ref="schemaForm"
          :model="form"
          class="edit-article-form"
          :column="cols"
          label-position="top"
          label-width="100px"
        />
      </div>
    </div>
    <div
      v-else
      class="post-message__main"
    >
      <titan-form
        ref="form"
        :model="form"
        :column="cols"
        label-width="100px"
      />
      <div class="post-message__main__form">
        <el-form
          :model="formData"
          label-width="100px"
        >
          <!-- <el-form-item label="发送设置">
            <el-radio-group v-model="formData.send_type">
              <el-radio :label="0">审核通过后立即发送</el-radio>
              <el-radio :label="1">预约发送</el-radio>
            </el-radio-group>
          </el-form-item>-->
          <el-form-item
            v-show="formData.send_type === 1"
            :label="$t('components. 预约时间')"
          >
            <el-date-picker
              v-model="formData.appointment_time"
              value-format="timestamp"
              type="datetime"
              size="small"
              :placeholder="$t('components. 请选择预约时间')"
            />
          </el-form-item>
          <el-form-item
            v-show="formData.send_type === 1"
            label
          >
            <el-alert
              class="post-message__main__tip"
              :title="$t('components. 若审核后错过预约发送时间，请选择是否发送')"
              type="warning"
              show-icon
            />
          </el-form-item>
          <el-form-item
            v-show="formData.send_type === 1"
            label
          >
            <el-radio-group v-model="appointment_type">
              <el-radio :label="1">
                {{ $t('components. 审核通过后立即发送') }}
              </el-radio>
              <el-radio :label="0">
                {{ $t('components. 错过预约时间后不发送') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </titan-dialog>
</template>

<script>
import { mapState } from 'vuex';
import { OperationAPI } from '@/api/modules/operation';
// import IpConfig from '@/schema/common/IpConfig';
import getIpConfigForm from '@/schema/initial/common/ip-config';
import { SysAccountAPI } from '@/api/modules/character';
import { clearCloneForm } from '@/utils';
import { AccountStatusMap } from '@/assets/libs/account';
import URLS from '@/api/urls';
import ArticleUploadFile from '@/schema/custom/components/ArticleUploadFile.vue';
import { postInnerRule } from '@/utils/validate';

export default {
  name: 'PostMessage',
  provide() {
    return { postModel: this };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    disableSelectHomePage: {
      type: Boolean,
      default: false
    },
    routerpage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        send_type: 0,
        appointment_time: null
      },
      form: {
        $_hidden: false,
        content: '',
        accountInfo: '',
        type: 'image',
        image: [],
        image_json_list: [],
        video: '',
        video_json: {
          docId: '',
          url: ''
        },
        ...getIpConfigForm()
      },
      appointment_type: 1,
      is_draft: 0, // 保存为草稿
      formFetchData: {},
      uploadAction: URLS.missionManage.uploadFile,
      loading: false
    };
  },
  computed: {
    ...mapState({ acitveAccountInfo: state => state.operation.acitveAccountInfo }),
    ...mapState({ postInfo: state => state.autoTaskInfo.postInfo }),
    id() {
      return this.acitveAccountInfo ? this.acitveAccountInfo.id : '';
    },
    username() {
      return this.acitveAccountInfo ? this.acitveAccountInfo.account : '';
    },
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    cols() {
      return [
        {
          prop: 'accountInfo',
          render: (h, form) => (
            <titan-select
              v-model={form.accountInfo}
              disabled={this.disableSelectHomePage}
              placeholder={this.$t('components[" 请选择公共主页列表"]')}
              style={'margin-bottom:10px'}
              maxlength={500}
              onChange={this.handleChange}
              config={{
                method: SysAccountAPI.getList,
                scrollable: true,
                queryKey: 'public_page_name',
                requestParams: { public_page: [1] },
                itemLabel: 'public_page_name',
                itemValue: 'public_page_name',
                respFormatter: data => (data || []).filter(item => item.status === AccountStatusMap['normal'])
              }}
            />
          )
        },
        {
          label: this.$t('components[" 添加图片/视频"]'),
          show: () => this.routerpage === 'homepage',
          render: (h, form) => (
            <ArticleUploadFile action={this.uploadAction} form={this.form} platform={this.acitveAccountInfo.platform}/>
          )
        },
        {
          prop: 'content',
          rules: [
            {
              min: 1,
              max: 500,
              message: this.acitveAccountInfo.platform === 'twitter' ? this.$t('components[" 内容不得超过140个字符"]') : this.$t('components[" 内容不得超过500个字符"]'),
              trigger: 'change'
            },
            { validator: this.contentValidator, trigger: blur }
          ],
          render: (h, form) => (
            <titan-input
              v-model={form.content}
              class='post-message__main__textarea'
              type='textarea'
              placeholder={this.$t('components[" 请输入内容"]')}
              maxlength={this.acitveAccountInfo.platform === 'twitter' ? 140 : 500}
              show-word-limit
              resize={'none'}
            />
          )
        }
        // ...IpConfig.call(this, 'auto')
      ];
    }
  },
  watch: {
    acitveAccountInfo: {
      deep: true,
      immediate: true,
      handler() {
        this.form.accountInfo = this.acitveAccountInfo?.public_page_name || '';
      }
    },
    postInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        if (!this.disableSelectHomePage) {
          this.form.content = val.source_content || val.content;
        }
      }
    }
  },
  beforeDestroy() {
    this.$store.commit('autoTaskInfo/SET_HOME_PAGE_CONTENT', {});
  },
  methods: {
    setValue(key, val) {
      this.$set(this.formFetchData, key, val);
    },
    getValue(key) {
      return this.formFetchData[key];
    },
    saveDraft() {
      this.is_draft = 1;
      this.preSendMsg();
    },
    sendAudit() {
      this.is_draft = 0;
      if (!this.form.accountInfo) {
        this.$message.error(this.$t('components[" 请选择公共主页"]'));
        return false;
      }
      this.preSendMsg();
    },
    preSendMsg() {
      this.$refs.schemaForm.validate(async valid => {
        if (valid) {
          if (!this.id) return;
          if (!this.form.content && this.form.image.length === 0 && !this.form.video) {
            this.$message.error(this.$t('components[" 请输入帖子内容"]'));
            return false;
          }
          try {
            this.loading = true;
            const res = await OperationAPI.findTaskIdByPublicPageAccount(clearCloneForm({ accountId: this.id, platform: this.acitveAccountInfo.platform, account: this.acitveAccountInfo.account }));
            if (res.code !== 0 || !res.data) return;
            this.sendMsg(res.data);
          } catch (err) {
            this.loading = false;
            console.error(err);
          }
        }
      });
    },
    sendMsg(taskId) {
      // 对应策略任务发帖的param参数
      const param = {};
      if (this.form.$_showProxyFilter) {
        param.proxy_filter = this.form.proxy_filter; // ip分配
      }
      param.post_type = 'single'; // 单一发帖
      param.content = this.form.content;
      param.account_type = 0; // 发帖主体为账号
      param.article_filter = { count: 1 }; // 文章筛选条件
      param.image_json_list = this.form.image_json_list;
      param.video_json = this.form.video_json;
      param.account_filter = {
        // 账号筛选条件
        platform: this.acitveAccountInfo.platform,
        status: [0, 1, 5, 2],
        filter_type: 1,
        count: 1,
        $fe_all_count: false,
        id_list: [this.id]
      };
      // param.at_list = {
      //   type: 'disable',
      //   group_filter: { platform: 'facebook' }
      // };
      param.corps_filter = { platform: 'instagram', count: 10000 };

      const postData = {
        parentId: taskId,
        platform: this.acitveAccountInfo.platform,
        priority: 'MEDIUM',
        type: 'POST',
        taskActions: [
          {
            type: 'PUBLIC_PAGE_POST',
            param: param
          }
        ]
      };

      const params = {
        parentTaskId: taskId,
        pageContent: this.form.content,
        pageAccount: this.username,
        publicPageName: this.form.accountInfo, // 等同于 this.acitveAccountInfo.public_page_name
        // appointment_time: this.formData.appointment_time,    //预约时间为undefined, 表明立即执行
        // is_draft: this.is_draft,
        // send_type: this.formData.send_type,    //send_type 默认为0, 表面立即执行
        taskParams: JSON.stringify(clearCloneForm(postData))
      };
      if (this.formData.send_type === 1) {
        params.appointment_type = this.appointment_type;
        if (!this.formData.appointment_time) {
          this.$message.error(this.$t('components[" 请选择预约时间"]'));
          return false;
        }
      }
      OperationAPI.sendMsg(params)
        .then(res => {
          if (res.code !== 200) {
            this.$message.error(res.msg);
            return;
          }
          this.resetField();
          this.dialogVisible = false;
          this.loading = false;
          this.$message.success(this.$t('components[" 发送审核成功"]'));
          this.$emit('refetch');
        })
        .catch(err => {
          this.loading = false;
          console.error(err);
        });
    },
    resetField() {
      this.$refs.schemaForm.resetFields();
      this.formData = {
        send_type: 0,
        appointment_time: null
      };
      this.appointment_type = 1;
      this.is_draft = 0;
    },
    async closeDialog() {
      const isClose = await this.confirmClose();
      isClose && (this.dialogVisible = false);
      if (isClose) this.form.accountInfo = '';
    },
    async beforeClose(done) {
      const isClose = await this.confirmClose();
      isClose && done();
      if (isClose) this.form.accountInfo = '';
    },
    confirmClose() {
      if (this.form.content) {
        return this.$confirm(this.$t('components[" 是否放弃编辑？"]'), this.$t('components["提示"]'), { type: 'warning' })
          .then(() => Promise.resolve(true))
          .catch(() => Promise.resolve(false));
      } else {
        return Promise.resolve(true);
      }
    },
    async handleChange(val) {
      const query = clearCloneForm({
        page_index: 1,
        page_size: 2,
        public_page: [1],
        account: val,
        public_page_name: val
      });
      const { data } = await SysAccountAPI.getList(query);
      this.$store.commit('operation/SET_ACCOUNT', data[0]);
    },
    contentValidator(rule, value, callback) {
      if (value) {
        const pass = postInnerRule(value, callback);
        if (!pass) return;
      }
      callback();
    }
  }
};
</script>

<style lang="scss" scoped>
.textarea-box {
  overflow: scroll;

  height: 40vh;
  .edit-article-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
     position: relative;

   ::v-deep .el-form-item{
      width: calc(50% - 20px);
    }
    ::v-deep .el-form-item:nth-child(3){
     position:absolute;
     top: 70px;
    }
    ::v-deep .el-radio-button {
      margin-right: 10px;
      .el-radio-button__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: #f2f3f5 !important;
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        color: #3569e7;
        border: 1px solid #b4d1ed;
        background-color: #ebf2fa !important;
        box-shadow: none;
      }
    }
    ::v-deep .file-select-btn {
      display: flex;
      justify-content: flex-start;
      align-items: flex-end;
    }
    ::v-deep .file_list {
      .el-upload--picture-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        line-height: 10px;
      }
      & > div {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
      }
    }
    ::v-deep .hidden_file {
      .el-upload--picture-card {
        display: none;
      }
    }
  }
}
::v-deep {
  .post-message {
    ::v-deep .el-dialog__body {
      padding: 10px 20px 20px;
    }
    &__main {
      .el-form-item {
        margin-bottom: 0;
      }
      &__textarea {
        textarea {
          height: 200px;
        }
      }
      &__form {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #303133;
        .el-form {
          width: 100%;
          .el-form-item {
            margin-bottom: 10px;
            .el-form-item__label {
              text-align: left;
              line-height: 36px;
            }
            .el-form-item__content {
              line-height: 36px;
            }
          }
        }
      }
      &__tip {
        height: 36px;
        .el-alert__closebtn::before {
          display: none;
        }
      }
    }
    &__footer {
      text-align: center;
    }
  }
}
</style>
