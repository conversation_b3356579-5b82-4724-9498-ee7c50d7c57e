{"title": "Greece", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:2100"}}, "hc-transform": {"default": {"crs": "+proj=tmerc +lat_0=0 +lon_0=24 +k=0.9996 +x_0=500000 +y_0=0 +ellps=GRS80 +towgs84=-199.87,74.79,246.62,0,0,0,0 +units=m +no_defs", "scale": 0.000902877649925, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 104677.743447, "yoffset": 4624151.36112}}, "features": [{"type": "Feature", "id": "GR.AS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.94, "hc-middle-y": 0.67, "hc-key": "gr-as", "hc-a2": "AS", "labelrank": "6", "hasc": "GR.AS", "alt-name": "Aegean South", "woe-id": "12577886", "subregion": null, "fips": "GR47", "postal-code": "AS", "name": "<PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "25.4867", "woe-name": "<PERSON><PERSON>", "latitude": "37.0538", "woe-label": "Notio <PERSON>, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[8343, 52], [8353, 11], [8292, -2], [8204, -64], [8174, -63], [8153, -34], [8216, 48], [8248, 39], [8358, 73], [8343, 52]]], [[[9078, 1313], [9039, 1301], [8958, 1305], [8967, 1341], [9080, 1349], [9078, 1313]]], [[[9209, 1400], [9190, 1360], [9186, 1390], [9183, 1417], [9209, 1400]]], [[[6861, 1474], [6851, 1453], [6813, 1473], [6763, 1457], [6720, 1464], [6703, 1497], [6750, 1531], [6861, 1474]]], [[[8507, 1823], [8464, 1826], [8446, 1852], [8459, 1883], [8519, 1909], [8546, 1866], [8507, 1823]]], [[[5738, 1829], [5686, 1828], [5652, 1862], [5594, 1896], [5583, 1925], [5617, 1925], [5717, 1863], [5738, 1829]]], [[[5944, 1921], [5899, 1884], [5867, 1885], [5912, 1981], [5964, 2014], [6013, 2013], [6011, 1993], [5944, 1921]]], [[[5355, 2056], [5337, 2050], [5291, 2103], [5329, 2124], [5363, 2093], [5355, 2056]]], [[[5255, 2192], [5290, 2177], [5247, 2104], [5196, 2119], [5188, 2176], [5232, 2202], [5255, 2192]]], [[[6351, 2182], [6307, 2193], [6366, 2253], [6375, 2198], [6351, 2182]]], [[[6619, 2271], [6537, 2276], [6588, 2320], [6632, 2290], [6619, 2271]]], [[[8473, 2439], [8490, 2386], [8442, 2385], [8406, 2424], [8414, 2438], [8473, 2439]]], [[[5867, 2391], [5860, 2378], [5815, 2430], [5820, 2481], [5885, 2513], [5852, 2405], [5867, 2391]]], [[[5348, 2519], [5389, 2488], [5408, 2498], [5467, 2422], [5484, 2376], [5417, 2294], [5377, 2368], [5365, 2409], [5374, 2444], [5331, 2486], [5348, 2519]]], [[[8391, 2597], [8397, 2587], [8361, 2591], [8369, 2602], [8391, 2597]]], [[[6804, 2654], [6822, 2633], [6771, 2610], [6745, 2653], [6812, 2676], [6804, 2654]]], [[[8031, 2794], [8071, 2784], [8066, 2744], [8091, 2732], [8121, 2659], [8090, 2645], [8060, 2655], [8078, 2687], [8034, 2678], [8033, 2719], [7981, 2754], [7976, 2774], [8009, 2801], [8031, 2794]]], [[[5134, 2761], [5185, 2752], [5196, 2702], [5193, 2644], [5176, 2666], [5151, 2613], [5129, 2643], [5071, 2649], [5057, 2681], [5073, 2729], [5134, 2761]]], [[[8380, 2929], [8356, 2934], [8361, 2971], [8368, 2949], [8380, 2929]]], [[[7929, 2987], [7942, 2969], [7985, 2970], [7981, 2916], [7954, 2952], [7929, 2946], [7892, 2976], [7929, 2987]]], [[[7714, 3065], [7750, 3042], [7762, 3012], [7731, 3022], [7692, 2990], [7713, 2962], [7717, 2910], [7691, 2913], [7668, 2949], [7679, 3007], [7674, 3061], [7714, 3065]]], [[[7908, 3111], [7950, 3074], [7932, 3049], [7888, 3113], [7908, 3111]]], [[[8159, 3236], [8218, 3237], [8254, 3209], [8183, 3191], [8159, 3236]]], [[[7559, 3405], [7560, 3358], [7492, 3372], [7541, 3413], [7559, 3405]]], [[[5447, 3368], [5418, 3365], [5346, 3385], [5416, 3423], [5446, 3415], [5447, 3368]]], [[[7659, 3410], [7620, 3374], [7635, 3334], [7627, 3300], [7589, 3363], [7588, 3416], [7611, 3391], [7637, 3411], [7614, 3471], [7634, 3496], [7667, 3447], [7659, 3410]]], [[[8636, 136], [8603, 143], [8547, 98], [8544, 45], [8481, 67], [8460, 98], [8491, 147], [8477, 206], [8475, 269], [8421, 310], [8431, 352], [8479, 395], [8492, 475], [8524, 514], [8533, 559], [8524, 643], [8559, 645], [8603, 690], [8614, 658], [8595, 609], [8606, 529], [8571, 484], [8536, 411], [8535, 350], [8569, 325], [8617, 257], [8598, 221], [8635, 185], [8636, 136]]], [[[9648, 1175], [9693, 1157], [9669, 1113], [9687, 1082], [9621, 1099], [9566, 1087], [9491, 1027], [9409, 893], [9365, 848], [9307, 809], [9254, 843], [9217, 903], [9233, 926], [9265, 1037], [9266, 1110], [9203, 1174], [9208, 1194], [9170, 1221], [9278, 1287], [9287, 1341], [9319, 1407], [9355, 1419], [9430, 1514], [9504, 1543], [9699, 1662], [9781, 1660], [9817, 1710], [9851, 1676], [9835, 1653], [9851, 1583], [9820, 1558], [9815, 1503], [9780, 1452], [9768, 1369], [9750, 1350], [9730, 1286], [9696, 1285], [9648, 1175]]], [[[6309, 1631], [6387, 1561], [6401, 1488], [6371, 1433], [6348, 1423], [6241, 1453], [6301, 1465], [6335, 1508], [6315, 1601], [6247, 1624], [6264, 1645], [6309, 1631]]], [[[8683, 1669], [8739, 1705], [8755, 1695], [8782, 1615], [8855, 1593], [8835, 1562], [8772, 1555], [8720, 1625], [8700, 1598], [8668, 1622], [8670, 1693], [8683, 1669]]], [[[7464, 1807], [7493, 1839], [7541, 1840], [7522, 1881], [7487, 1914], [7526, 1913], [7610, 1853], [7556, 1818], [7520, 1819], [7474, 1763], [7476, 1724], [7415, 1719], [7370, 1772], [7369, 1846], [7404, 1848], [7464, 1807]]], [[[9240, 1874], [9309, 1967], [9362, 1989], [9365, 1975], [9315, 1951], [9333, 1919], [9375, 1954], [9388, 1825], [9329, 1803], [9354, 1835], [9281, 1861], [9300, 1876], [9240, 1874]]], [[[4940, 1899], [4940, 2006], [4974, 2059], [5025, 2031], [5040, 1987], [5107, 1975], [5117, 1999], [5077, 2018], [5042, 2059], [5056, 2086], [5094, 2058], [5124, 2049], [5197, 2082], [5210, 2043], [5214, 1959], [5137, 1922], [4987, 1918], [4940, 1899]]], [[[6215, 2059], [6280, 2030], [6293, 2006], [6268, 1978], [6276, 1954], [6249, 1915], [6204, 1937], [6122, 2016], [6096, 2026], [6108, 2090], [6163, 2133], [6215, 2059]]], [[[8178, 2055], [8187, 2132], [8226, 2158], [8261, 2137], [8303, 2202], [8352, 2252], [8478, 2336], [8622, 2367], [8636, 2343], [8699, 2330], [8718, 2301], [8678, 2277], [8543, 2234], [8515, 2215], [8441, 2133], [8415, 2128], [8338, 2151], [8250, 2095], [8264, 2063], [8239, 2040], [8249, 1999], [8204, 2019], [8178, 2055]]], [[[6746, 2161], [6779, 2153], [6835, 2165], [6856, 2208], [6848, 2231], [6990, 2307], [6963, 2338], [7031, 2371], [7069, 2332], [7118, 2310], [7033, 2297], [6929, 2217], [6878, 2145], [6778, 2108], [6724, 2103], [6721, 2132], [6746, 2161]]], [[[8195, 2560], [8172, 2550], [8121, 2612], [8141, 2615], [8248, 2574], [8232, 2563], [8274, 2524], [8318, 2513], [8320, 2449], [8299, 2414], [8261, 2425], [8250, 2389], [8202, 2382], [8160, 2411], [8183, 2445], [8175, 2467], [8205, 2530], [8195, 2560]]], [[[5944, 2594], [5961, 2620], [6035, 2673], [6071, 2686], [6053, 2658], [6097, 2643], [6105, 2686], [6139, 2687], [6122, 2643], [6102, 2568], [6124, 2536], [6090, 2483], [6031, 2432], [5967, 2428], [5905, 2468], [5916, 2545], [5969, 2588], [5944, 2594]]], [[[6252, 2507], [6200, 2571], [6238, 2590], [6283, 2645], [6364, 2705], [6388, 2748], [6436, 2767], [6487, 2733], [6511, 2678], [6515, 2611], [6499, 2484], [6465, 2407], [6433, 2392], [6390, 2349], [6339, 2327], [6304, 2402], [6261, 2432], [6252, 2507]]], [[[4995, 3122], [5071, 3197], [5080, 3175], [5064, 3134], [5113, 3105], [5123, 3048], [5092, 3037], [5081, 2984], [5042, 2944], [4988, 2921], [5009, 3002], [5030, 3017], [5023, 3064], [4987, 3080], [4995, 3122]]], [[[6173, 3170], [6155, 3209], [6199, 3241], [6236, 3178], [6258, 3220], [6342, 3184], [6335, 3146], [6274, 3131], [6268, 3102], [6150, 3091], [6173, 3170]]], [[[5699, 3122], [5725, 3070], [5624, 3009], [5632, 3063], [5598, 3063], [5643, 3142], [5639, 3238], [5681, 3234], [5716, 3160], [5699, 3122]]], [[[5910, 3318], [5824, 3406], [5763, 3437], [5747, 3500], [5831, 3501], [5840, 3469], [5869, 3457], [5917, 3460], [5951, 3428], [5969, 3452], [6070, 3419], [6087, 3366], [6035, 3277], [5978, 3285], [5910, 3318]]], [[[4927, 3463], [4950, 3506], [4994, 3506], [5028, 3459], [5020, 3411], [4918, 3276], [4872, 3266], [4875, 3407], [4909, 3467], [4927, 3463]]], [[[5720, 3511], [5661, 3563], [5631, 3638], [5598, 3646], [5537, 3723], [5517, 3711], [5509, 3754], [5440, 3828], [5415, 3838], [5372, 3891], [5394, 3953], [5461, 3991], [5507, 3987], [5508, 3961], [5540, 3951], [5575, 3865], [5608, 3852], [5705, 3853], [5717, 3838], [5693, 3755], [5744, 3713], [5712, 3659], [5757, 3617], [5712, 3543], [5720, 3511]]]]}}, {"type": "Feature", "id": "GR.II", "properties": {"hc-group": "admin1", "hc-middle-x": 0.78, "hc-middle-y": 0.76, "hc-key": "gr-ii", "hc-a2": "II", "labelrank": "6", "hasc": "GR.II", "alt-name": "Ionian Islands", "woe-id": "12577882", "subregion": null, "fips": "GR25", "postal-code": "II", "name": "<PERSON><PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "20.5554", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "38.2419", "woe-label": "<PERSON><PERSON>, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[760, 4909], [743, 4921], [792, 4991], [763, 4930], [760, 4909]]], [[[726, 4965], [717, 4995], [760, 5054], [811, 5068], [820, 5046], [751, 4997], [726, 4965]]], [[[621, 5103], [641, 5088], [574, 5035], [557, 5076], [569, 5093], [621, 5103]]], [[[-19, 5871], [5, 5845], [-9, 5841], [-43, 5878], [-19, 5871]]], [[[-73, 5957], [-60, 5921], [-100, 5927], [-147, 5992], [-131, 6020], [-73, 5957]]], [[[-818, 6859], [-836, 6859], [-851, 6887], [-846, 6898], [-818, 6859]]], [[[-937, 7031], [-966, 6993], [-999, 7001], [-996, 7029], [-937, 7031]]], [[[-755, 7042], [-775, 7061], [-764, 7075], [-741, 7059], [-755, 7042]]], [[[775, 3625], [701, 3642], [637, 3595], [638, 3520], [599, 3518], [554, 3574], [467, 3658], [465, 3683], [395, 3774], [367, 3793], [369, 3857], [422, 3927], [455, 3956], [483, 3948], [533, 3837], [563, 3818], [602, 3826], [652, 3804], [695, 3768], [732, 3691], [815, 3633], [825, 3585], [775, 3625]]], [[[500, 4417], [550, 4368], [562, 4326], [603, 4264], [614, 4214], [596, 4163], [502, 4167], [459, 4217], [378, 4254], [336, 4249], [315, 4211], [249, 4245], [233, 4267], [226, 4321], [203, 4374], [252, 4341], [223, 4396], [193, 4481], [167, 4510], [157, 4483], [164, 4407], [149, 4349], [92, 4315], [86, 4342], [51, 4364], [45, 4392], [68, 4435], [66, 4474], [99, 4505], [116, 4545], [129, 4644], [136, 4612], [163, 4641], [194, 4577], [235, 4560], [286, 4596], [316, 4669], [301, 4734], [304, 4808], [346, 4806], [405, 4614], [404, 4578], [369, 4539], [405, 4468], [420, 4461], [469, 4504], [500, 4417]]], [[[517, 4554], [457, 4643], [435, 4697], [428, 4761], [398, 4794], [424, 4802], [448, 4846], [471, 4819], [454, 4782], [512, 4758], [466, 4652], [516, 4641], [500, 4663], [545, 4660], [549, 4598], [572, 4564], [517, 4554]]], [[[320, 4995], [347, 5144], [401, 5283], [440, 5336], [507, 5386], [543, 5342], [555, 5278], [552, 5198], [512, 5125], [543, 5162], [537, 5055], [487, 5004], [404, 5011], [392, 5042], [371, 5029], [326, 4956], [320, 4995]]], [[[-383, 6563], [-365, 6399], [-258, 6337], [-187, 6343], [-149, 6222], [-164, 6213], [-251, 6264], [-290, 6273], [-342, 6316], [-394, 6325], [-373, 6351], [-447, 6387], [-465, 6452], [-470, 6518], [-505, 6578], [-576, 6629], [-608, 6713], [-643, 6717], [-660, 6748], [-661, 6795], [-684, 6782], [-685, 6824], [-713, 6836], [-649, 6906], [-502, 6910], [-438, 6941], [-347, 6887], [-325, 6883], [-345, 6807], [-446, 6764], [-466, 6737], [-441, 6683], [-458, 6675], [-361, 6628], [-367, 6594], [-395, 6596], [-383, 6563]]]]}}, {"type": "Feature", "id": "GR.AT", "properties": {"hc-group": "admin1", "hc-middle-x": 0.74, "hc-middle-y": 0.11, "hc-key": "gr-at", "hc-a2": "AT", "labelrank": "6", "hasc": "GR.AT", "alt-name": "Attica", "woe-id": "12577879", "subregion": null, "fips": "GR35", "postal-code": "AT", "name": "<PERSON><PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longitude": "23.7997", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "38.0626", "woe-label": "Attiki, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[3689, 625], [3641, 673], [3612, 742], [3686, 681], [3689, 625]]], [[[3867, 2296], [3855, 2297], [3845, 2332], [3853, 2330], [3867, 2296]]], [[[3492, 2824], [3453, 2823], [3408, 2872], [3450, 2893], [3487, 2866], [3492, 2824]]], [[[4460, 3159], [4440, 3165], [4405, 3208], [4450, 3183], [4460, 3159]]], [[[3921, 3238], [3858, 3238], [3834, 3262], [3883, 3281], [3945, 3252], [3921, 3238]]], [[[4472, 3437], [4452, 3443], [4456, 3460], [4474, 3460], [4472, 3437]]], [[[4664, 3444], [4655, 3464], [4687, 3594], [4708, 3609], [4718, 3580], [4664, 3444]]], [[[3908, 3503], [3862, 3517], [3864, 3565], [3834, 3581], [3823, 3635], [3972, 3644], [3993, 3634], [3954, 3567], [3946, 3516], [3908, 3503]]], [[[3397, 4091], [3516, 4120], [3570, 4157], [3520, 4222], [3579, 4232], [3567, 4245], [3516, 4276], [3627, 4267], [3643, 4285], [3653, 4356], [3694, 4386], [3739, 4399], [3770, 4359], [3754, 4320], [3772, 4304], [3835, 4292], [3882, 4224], [3982, 4184], [4060, 4250], [4071, 4397], [4097, 4439], [4139, 4478], [4208, 4500], [4246, 4523], [4265, 4526], [4424, 4456], [4471, 4444], [4521, 4387], [4616, 4310], [4630, 4250], [4597, 4218], [4518, 4183], [4542, 4056], [4571, 4005], [4546, 3941], [4546, 3876], [4585, 3863], [4583, 3838], [4546, 3824], [4597, 3803], [4614, 3740], [4597, 3717], [4626, 3699], [4639, 3659], [4632, 3608], [4605, 3579], [4621, 3557], [4605, 3526], [4620, 3510], [4572, 3451], [4469, 3492], [4461, 3569], [4436, 3560], [4411, 3654], [4343, 3710], [4301, 3728], [4294, 3696], [4241, 3707], [4244, 3743], [4214, 3801], [4138, 3909], [4074, 3889], [4052, 3935], [3982, 3963], [4037, 4026], [4017, 4066], [3931, 4057], [3911, 4039], [3856, 4028], [3788, 3976], [3805, 3955], [3708, 3965], [3581, 3956], [3498, 3905], [3439, 4033], [3397, 4091]]], [[[3191, 1487], [3221, 1490], [3290, 1372], [3371, 1313], [3404, 1261], [3357, 1245], [3342, 1209], [3338, 1118], [3275, 1122], [3209, 1151], [3158, 1215], [3169, 1338], [3149, 1405], [3191, 1487]]], [[[3867, 2997], [4020, 3017], [3971, 2984], [3918, 2985], [3907, 2938], [3783, 2913], [3755, 2923], [3867, 2997]]], [[[3868, 3921], [3821, 3933], [3880, 3964], [3890, 3993], [3939, 3993], [3956, 3964], [3973, 3920], [3970, 3883], [3939, 3837], [3918, 3848], [3875, 3815], [3819, 3829], [3796, 3859], [3842, 3901], [3878, 3889], [3914, 3943], [3868, 3921]]], [[[3527, 3389], [3580, 3348], [3589, 3360], [3635, 3315], [3694, 3277], [3741, 3293], [3739, 3334], [3717, 3375], [3682, 3402], [3726, 3428], [3789, 3433], [3801, 3414], [3778, 3329], [3758, 3306], [3817, 3273], [3783, 3242], [3834, 3231], [3887, 3203], [3944, 3155], [3944, 3123], [3840, 3085], [3826, 3144], [3755, 3132], [3671, 3143], [3635, 3180], [3577, 3212], [3516, 3232], [3490, 3275], [3527, 3389]]]]}}, {"type": "Feature", "id": "GR.PP", "properties": {"hc-group": "admin1", "hc-middle-x": 0.43, "hc-middle-y": 0.46, "hc-key": "gr-pp", "hc-a2": "PP", "labelrank": "6", "hasc": "GR.PP", "alt-name": "Peloponnese", "woe-id": "12577887", "subregion": null, "fips": "GR36", "postal-code": "PP", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longitude": "22.3501", "woe-name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "latitude": "37.3319", "woe-label": "<PERSON><PERSON><PERSON><PERSON><PERSON>os, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[3259, 1633], [3184, 1623], [3189, 1647], [3240, 1688], [3259, 1633]]], [[[1754, 2038], [1720, 2026], [1732, 2088], [1758, 2080], [1754, 2038]]], [[[1683, 2158], [1696, 2146], [1652, 2072], [1655, 2127], [1683, 2158]]], [[[1486, 2542], [1467, 2569], [1486, 2579], [1493, 2562], [1486, 2542]]], [[[3397, 4091], [3439, 4033], [3498, 3905], [3417, 3860], [3365, 3882], [3320, 3883], [3295, 3816], [3315, 3762], [3420, 3755], [3441, 3766], [3524, 3701], [3524, 3647], [3457, 3626], [3447, 3595], [3514, 3562], [3499, 3503], [3513, 3445], [3504, 3398], [3527, 3389], [3490, 3275], [3516, 3232], [3577, 3212], [3635, 3180], [3671, 3143], [3755, 3132], [3826, 3144], [3840, 3085], [3693, 3079], [3679, 3061], [3618, 3066], [3578, 3041], [3611, 3019], [3617, 2978], [3522, 2973], [3544, 2914], [3500, 2909], [3492, 2936], [3417, 3000], [3382, 2997], [3383, 3068], [3410, 3050], [3434, 3106], [3468, 3084], [3457, 3141], [3414, 3163], [3370, 3152], [3351, 3202], [3300, 3160], [3283, 3216], [3250, 3257], [3153, 3276], [3114, 3248], [3071, 3280], [3022, 3376], [2972, 3352], [2953, 3292], [2969, 3255], [2966, 3201], [3002, 3120], [2993, 3068], [3057, 2990], [3067, 2940], [3179, 2732], [3158, 2656], [3191, 2631], [3234, 2640], [3261, 2568], [3242, 2576], [3286, 2498], [3300, 2426], [3274, 2426], [3306, 2377], [3300, 2351], [3358, 2215], [3402, 2146], [3383, 2117], [3425, 2106], [3392, 2031], [3347, 2053], [3314, 2005], [3321, 1969], [3355, 1968], [3324, 1941], [3325, 1901], [3380, 1792], [3447, 1755], [3464, 1695], [3447, 1680], [3468, 1649], [3533, 1600], [3523, 1563], [3480, 1585], [3452, 1566], [3396, 1581], [3361, 1628], [3360, 1684], [3323, 1713], [3244, 1702], [3200, 1805], [3123, 1875], [3136, 1911], [3084, 1960], [3055, 1929], [3055, 1988], [3034, 2101], [2990, 2146], [2890, 2155], [2812, 2147], [2755, 2116], [2756, 2050], [2714, 2021], [2687, 1965], [2718, 1954], [2721, 1912], [2687, 1944], [2667, 1908], [2693, 1884], [2670, 1838], [2660, 1871], [2634, 1844], [2632, 1753], [2642, 1688], [2665, 1626], [2642, 1577], [2655, 1530], [2623, 1527], [2612, 1605], [2536, 1660], [2502, 1650], [2469, 1700], [2486, 1755], [2506, 1749], [2488, 1840], [2490, 1924], [2516, 2001], [2479, 1995], [2437, 2082], [2415, 2109], [2399, 2180], [2314, 2301], [2287, 2316], [2256, 2293], [2210, 2334], [2232, 2442], [2232, 2495], [2102, 2519], [1984, 2473], [1962, 2437], [1939, 2256], [1946, 2192], [1997, 2149], [1965, 2141], [1953, 2104], [1874, 2045], [1813, 2174], [1740, 2154], [1670, 2192], [1647, 2241], [1649, 2306], [1676, 2359], [1669, 2408], [1626, 2430], [1611, 2468], [1543, 2549], [1519, 2597], [1508, 2721], [1518, 2777], [1541, 2820], [1656, 2916], [1674, 2966], [1659, 3064], [1679, 3072], [1761, 3053], [1806, 3062], [1847, 3050], [1916, 3118], [1916, 3161], [1969, 3215], [1995, 3193], [2031, 3220], [1996, 3265], [1885, 3358], [1801, 3373], [1804, 3543], [1780, 3620], [1787, 3682], [1861, 3796], [1906, 3805], [1915, 3761], [2079, 3675], [2101, 3676], [2134, 3712], [2200, 3717], [2204, 3695], [2247, 3696], [2280, 3732], [2350, 3758], [2359, 3781], [2333, 3825], [2358, 3956], [2477, 4011], [2534, 4150], [2505, 4202], [2517, 4263], [2587, 4223], [2609, 4231], [2706, 4208], [2838, 4135], [2911, 4107], [3030, 4004], [3052, 3966], [3138, 3907], [3244, 3921], [3272, 3967], [3242, 4004], [3165, 4053], [3124, 4056], [3195, 4094], [3234, 4130], [3327, 4100], [3397, 4091]]]]}}, {"type": "Feature", "id": "GR.TS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.32, "hc-middle-y": 0.57, "hc-key": "gr-ts", "hc-a2": "TS", "labelrank": "6", "hasc": "GR.TS", "alt-name": "Thessaly", "woe-id": "12577889", "subregion": null, "fips": "GR21", "postal-code": "TS", "name": "Thessalia", "country": "Greece", "type-en": "Region", "region": null, "longitude": "22.1914", "woe-name": "Thessalia", "latitude": "39.5322", "woe-label": "Thessalia, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4669, 5666], [4665, 5654], [4651, 5689], [4673, 5706], [4669, 5666]]], [[[3942, 5824], [3914, 5809], [3879, 5752], [3850, 5774], [3800, 5775], [3804, 5802], [3842, 5821], [3892, 5879], [3942, 5824]]], [[[4405, 5797], [4367, 5769], [4330, 5773], [4378, 5825], [4398, 5883], [4459, 5980], [4498, 5968], [4491, 5935], [4455, 5889], [4405, 5797]]], [[[4615, 6094], [4646, 6082], [4630, 6019], [4608, 6007], [4581, 6059], [4602, 6106], [4615, 6094]]], [[[4929, 6095], [4918, 6060], [4908, 6066], [4920, 6119], [4929, 6095]]], [[[4745, 6141], [4716, 6108], [4703, 6122], [4744, 6204], [4745, 6141]]], [[[4132, 5719], [4081, 5775], [4032, 5869], [4051, 5870], [4065, 5869], [4127, 5810], [4205, 5773], [4201, 5750], [4256, 5747], [4280, 5729], [4218, 5670], [4188, 5666], [4127, 5690], [4132, 5719]]], [[[2989, 7009], [3024, 6915], [3096, 6860], [3142, 6802], [3163, 6755], [3178, 6666], [3213, 6577], [3240, 6484], [3273, 6438], [3335, 6396], [3396, 6370], [3450, 6323], [3479, 6256], [3580, 6139], [3595, 6106], [3652, 6050], [3677, 5966], [3736, 5847], [3738, 5827], [3686, 5784], [3575, 5754], [3600, 5733], [3568, 5712], [3498, 5708], [3461, 5688], [3384, 5703], [3410, 5744], [3404, 5766], [3460, 5798], [3443, 5713], [3529, 5760], [3534, 5791], [3563, 5785], [3579, 5845], [3535, 5914], [3541, 5939], [3507, 5993], [3443, 6036], [3350, 6045], [3330, 6099], [3248, 6113], [3243, 6038], [3256, 6024], [3134, 6002], [3114, 5977], [3113, 5898], [3146, 5801], [3179, 5833], [3204, 5832], [3226, 5748], [3274, 5737], [3318, 5660], [3318, 5629], [3279, 5606], [3276, 5566], [3339, 5603], [3409, 5617], [3348, 5565], [3315, 5495], [3278, 5512], [3230, 5491], [3160, 5487], [3124, 5524], [3060, 5527], [2973, 5547], [2873, 5609], [2780, 5600], [2734, 5605], [2706, 5640], [2709, 5746], [2630, 5739], [2611, 5755], [2603, 5825], [2543, 5870], [2459, 5903], [2401, 5939], [2291, 5819], [2252, 5732], [2227, 5711], [2158, 5689], [2125, 5653], [2072, 5628], [2006, 5623], [2006, 5709], [1989, 5733], [1908, 5782], [1886, 5782], [1816, 5735], [1772, 5730], [1745, 5770], [1749, 5833], [1710, 5857], [1705, 5922], [1678, 5961], [1574, 5938], [1520, 5883], [1483, 5916], [1416, 5881], [1358, 5824], [1342, 5831], [1308, 5905], [1375, 6025], [1330, 6106], [1245, 6113], [1203, 6146], [1188, 6201], [1132, 6249], [1129, 6301], [1105, 6352], [1125, 6391], [1124, 6477], [1074, 6533], [1062, 6625], [1103, 6656], [1190, 6636], [1172, 6771], [1194, 6814], [1253, 6855], [1286, 6845], [1322, 6829], [1397, 6858], [1426, 6896], [1497, 6930], [1674, 6901], [1793, 6867], [1835, 6863], [1925, 6881], [1988, 6869], [2008, 6904], [1994, 6992], [1956, 7016], [2039, 7086], [2087, 7112], [2125, 7194], [2183, 7252], [2261, 7383], [2269, 7360], [2306, 7392], [2368, 7376], [2351, 7289], [2380, 7258], [2451, 7335], [2502, 7351], [2549, 7304], [2544, 7236], [2557, 7191], [2593, 7151], [2672, 7093], [2698, 7046], [2734, 7009], [2778, 6992], [2874, 7017], [2913, 6997], [2989, 7009]]]]}}, {"type": "Feature", "id": "GR.AN", "properties": {"hc-group": "admin1", "hc-middle-x": 0.5, "hc-middle-y": 0.32, "hc-key": "gr-an", "hc-a2": "AN", "labelrank": "6", "hasc": "GR.AN", "alt-name": "Aegean North", "woe-id": "12577890", "subregion": null, "fips": "GR48", "postal-code": "AN", "name": "<PERSON><PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "25.2262", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "39.9531", "woe-label": "V<PERSON>io <PERSON>, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[7330, 4793], [7312, 4799], [7307, 4822], [7324, 4815], [7330, 4793]]], [[[7229, 4854], [7301, 4823], [7280, 4806], [7217, 4826], [7196, 4866], [7229, 4854]]], [[[6389, 4835], [6353, 4834], [6370, 4866], [6389, 4855], [6389, 4835]]], [[[6467, 4959], [6509, 4917], [6489, 4841], [6435, 4840], [6434, 4883], [6388, 4936], [6467, 4959]]], [[[5725, 6277], [5703, 6351], [5706, 6387], [5748, 6428], [5791, 6400], [5783, 6361], [5725, 6277]]], [[[6979, 3317], [7018, 3359], [7053, 3425], [7093, 3459], [7245, 3453], [7314, 3486], [7346, 3523], [7446, 3542], [7395, 3460], [7288, 3360], [7162, 3333], [7076, 3274], [6976, 3267], [6979, 3317]]], [[[8233, 3596], [8123, 3548], [8064, 3493], [7973, 3496], [7926, 3571], [7872, 3596], [7810, 3581], [7768, 3539], [7727, 3551], [7713, 3634], [7730, 3666], [7790, 3685], [7807, 3717], [7946, 3755], [8033, 3749], [8083, 3714], [8126, 3704], [8175, 3673], [8193, 3676], [8161, 3722], [8313, 3704], [8271, 3683], [8308, 3599], [8233, 3596]]], [[[7161, 4557], [7179, 4493], [7118, 4463], [7105, 4428], [7119, 4368], [7031, 4331], [7015, 4279], [6996, 4263], [6952, 4276], [6901, 4321], [6871, 4369], [6846, 4359], [6846, 4401], [6818, 4419], [6840, 4454], [6898, 4487], [6935, 4488], [6948, 4542], [6968, 4552], [6958, 4627], [6915, 4648], [6920, 4683], [6890, 4737], [6838, 4752], [6780, 4815], [6767, 4860], [6785, 4916], [6963, 4957], [7047, 4937], [7050, 4926], [7103, 4865], [7111, 4897], [7145, 4899], [7170, 4866], [7155, 4847], [7148, 4788], [7121, 4779], [7161, 4749], [7145, 4673], [7161, 4557]]], [[[7654, 5707], [7695, 5667], [7679, 5609], [7623, 5612], [7583, 5644], [7590, 5718], [7563, 5757], [7502, 5759], [7480, 5746], [7549, 5684], [7618, 5603], [7619, 5570], [7549, 5544], [7470, 5534], [7367, 5555], [7290, 5596], [7217, 5610], [7155, 5611], [7093, 5679], [7059, 5694], [7084, 5719], [7146, 5728], [7182, 5776], [7223, 5783], [7304, 5856], [7274, 5892], [7195, 5897], [7158, 5880], [7069, 5739], [7052, 5722], [7004, 5718], [6901, 5754], [6816, 5794], [6749, 5847], [6762, 5864], [6775, 5971], [6853, 6031], [6869, 6000], [6953, 6017], [6985, 6012], [7001, 6044], [7018, 6023], [7058, 6057], [7140, 6079], [7152, 6098], [7139, 6165], [7355, 6177], [7379, 6171], [7371, 6136], [7447, 6098], [7440, 6083], [7404, 6022], [7459, 5965], [7516, 5936], [7559, 5875], [7602, 5846], [7586, 5835], [7617, 5774], [7638, 5773], [7654, 5707]]], [[[6083, 6811], [6076, 6847], [6014, 6888], [6063, 6937], [6043, 6975], [6013, 6984], [5964, 6942], [5997, 6931], [5960, 6912], [5957, 6878], [5986, 6836], [5986, 6803], [5901, 6823], [5932, 6872], [5926, 6889], [5883, 6855], [5874, 6886], [5785, 6854], [5809, 6886], [5785, 6897], [5809, 6983], [5783, 7024], [5780, 7082], [5766, 7098], [5884, 7121], [5913, 7100], [5995, 7122], [6021, 7113], [6070, 7049], [6094, 7048], [6142, 7082], [6182, 7146], [6247, 7147], [6264, 7095], [6207, 7055], [6188, 7025], [6192, 6986], [6144, 6965], [6169, 6954], [6137, 6930], [6139, 6885], [6170, 6839], [6148, 6781], [6083, 6811]]]]}}, {"type": "Feature", "id": "GR.GC", "properties": {"hc-group": "admin1", "hc-middle-x": 0.25, "hc-middle-y": 0.36, "hc-key": "gr-gc", "hc-a2": "GC", "labelrank": "6", "hasc": "GR.GC", "alt-name": "Greece Central", "woe-id": "12577888", "subregion": null, "fips": "GR33", "postal-code": "GC", "name": "Stereá <PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "22.6102", "woe-name": "Stereá <PERSON>", "latitude": "38.6373", "woe-label": "Sterea Ellada, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4873, 3970], [4826, 3958], [4811, 3975], [4830, 4013], [4855, 4022], [4873, 3970]]], [[[4984, 5299], [4966, 5278], [4947, 5288], [4963, 5314], [4984, 5299]]], [[[5229, 5252], [5208, 5308], [5145, 5306], [5083, 5368], [5061, 5357], [5092, 5409], [5090, 5497], [5127, 5518], [5228, 5443], [5228, 5370], [5240, 5347], [5312, 5302], [5359, 5245], [5361, 5213], [5331, 5190], [5277, 5196], [5261, 5226], [5224, 5203], [5220, 5179], [5178, 5231], [5229, 5252]]], [[[4232, 5110], [4304, 5070], [4358, 5052], [4491, 5033], [4512, 5058], [4538, 5045], [4620, 5048], [4721, 5006], [4679, 4963], [4685, 4922], [4795, 4831], [4814, 4804], [4783, 4780], [4761, 4729], [4756, 4675], [4780, 4643], [4757, 4626], [4789, 4579], [4794, 4515], [4840, 4447], [4847, 4395], [4831, 4367], [4856, 4329], [4932, 4282], [4954, 4241], [5013, 4248], [5047, 4223], [5117, 4209], [5141, 4221], [5252, 4242], [5243, 4126], [5261, 4092], [5238, 4002], [5210, 3961], [5157, 3936], [5118, 3936], [5081, 4010], [5059, 4027], [5006, 3988], [5025, 3975], [4988, 3950], [4947, 3988], [4917, 4039], [4933, 4059], [4918, 4094], [4890, 4102], [4862, 4141], [4790, 4145], [4790, 4186], [4759, 4191], [4787, 4270], [4743, 4344], [4692, 4359], [4731, 4409], [4719, 4429], [4665, 4391], [4655, 4420], [4680, 4473], [4628, 4498], [4562, 4506], [4613, 4538], [4604, 4579], [4579, 4607], [4508, 4612], [4424, 4601], [4340, 4623], [4261, 4612], [4177, 4634], [4120, 4621], [4090, 4639], [4061, 4709], [4036, 4698], [4087, 4771], [4087, 4814], [4044, 4881], [3971, 4898], [3928, 4922], [3919, 4974], [3865, 5016], [3857, 5045], [3779, 5093], [3726, 5143], [3714, 5173], [3562, 5285], [3491, 5318], [3418, 5311], [3361, 5357], [3280, 5380], [3306, 5320], [3275, 5306], [3141, 5295], [3114, 5279], [3120, 5328], [3165, 5360], [3361, 5437], [3374, 5469], [3424, 5480], [3434, 5532], [3487, 5564], [3562, 5564], [3651, 5609], [3682, 5615], [3712, 5587], [3782, 5550], [3785, 5497], [3835, 5477], [3854, 5390], [3878, 5323], [3900, 5296], [3954, 5272], [4006, 5226], [4038, 5219], [4034, 5196], [4121, 5166], [4170, 5181], [4213, 5157], [4232, 5110]]], [[[3315, 5495], [3241, 5470], [3249, 5448], [3195, 5429], [3132, 5440], [3119, 5394], [3065, 5366], [2993, 5358], [2976, 5385], [2940, 5367], [2859, 5425], [2809, 5394], [2741, 5382], [2740, 5340], [2799, 5348], [2786, 5305], [2881, 5294], [2956, 5325], [2984, 5276], [3036, 5221], [3092, 5227], [3128, 5210], [3154, 5174], [3212, 5205], [3249, 5177], [3335, 5171], [3387, 5128], [3424, 5006], [3476, 4991], [3552, 5032], [3519, 5052], [3563, 5057], [3603, 5031], [3663, 5018], [3704, 4994], [3719, 4934], [3670, 4895], [3727, 4843], [3777, 4816], [3726, 4807], [3739, 4775], [3822, 4778], [3945, 4756], [3974, 4788], [4045, 4730], [4011, 4709], [4038, 4687], [4035, 4655], [4073, 4636], [4131, 4547], [4177, 4516], [4246, 4523], [4208, 4500], [4139, 4478], [4097, 4439], [4071, 4397], [4060, 4250], [3982, 4184], [3882, 4224], [3835, 4292], [3772, 4304], [3754, 4320], [3770, 4359], [3739, 4399], [3694, 4386], [3653, 4356], [3643, 4285], [3627, 4267], [3516, 4276], [3567, 4245], [3463, 4265], [3488, 4286], [3440, 4324], [3401, 4287], [3319, 4341], [3247, 4350], [3211, 4322], [3245, 4326], [3268, 4299], [3194, 4300], [3178, 4375], [3068, 4354], [3019, 4388], [3061, 4440], [2992, 4464], [2930, 4504], [2911, 4538], [2923, 4565], [2877, 4610], [2835, 4570], [2803, 4519], [2827, 4507], [2789, 4457], [2752, 4476], [2729, 4522], [2728, 4564], [2654, 4670], [2579, 4714], [2587, 4655], [2544, 4619], [2571, 4582], [2553, 4542], [2480, 4556], [2434, 4551], [2412, 4569], [2361, 4572], [2316, 4516], [2267, 4581], [2147, 4637], [2101, 4627], [2072, 4657], [2000, 4647], [1950, 4612], [1905, 4626], [1911, 4682], [1937, 4728], [1988, 4770], [2033, 4778], [2070, 4810], [2066, 4858], [2087, 4901], [2052, 4941], [2068, 4981], [2064, 5028], [2081, 5068], [2076, 5112], [2097, 5172], [2049, 5215], [1929, 5156], [1866, 5163], [1858, 5100], [1786, 5063], [1777, 5107], [1668, 5101], [1564, 5167], [1529, 5233], [1538, 5279], [1630, 5330], [1630, 5351], [1524, 5388], [1466, 5424], [1462, 5514], [1343, 5592], [1339, 5614], [1371, 5672], [1379, 5713], [1340, 5780], [1342, 5831], [1358, 5824], [1416, 5881], [1483, 5916], [1520, 5883], [1574, 5938], [1678, 5961], [1705, 5922], [1710, 5857], [1749, 5833], [1745, 5770], [1772, 5730], [1816, 5735], [1886, 5782], [1908, 5782], [1989, 5733], [2006, 5709], [2006, 5623], [2072, 5628], [2125, 5653], [2158, 5689], [2227, 5711], [2252, 5732], [2291, 5819], [2401, 5939], [2459, 5903], [2543, 5870], [2603, 5825], [2611, 5755], [2630, 5739], [2709, 5746], [2706, 5640], [2734, 5605], [2780, 5600], [2873, 5609], [2973, 5547], [3060, 5527], [3124, 5524], [3160, 5487], [3230, 5491], [3278, 5512], [3315, 5495]]]]}}, {"type": "Feature", "id": "GR.CR", "properties": {"hc-group": "admin1", "hc-middle-x": 0.52, "hc-middle-y": 0.58, "hc-key": "gr-cr", "hc-a2": "CR", "labelrank": "6", "hasc": "GR.CR", "alt-name": "Crete", "woe-id": "12577885", "subregion": null, "fips": "GR43", "postal-code": "CR", "name": "<PERSON><PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "25.0652", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "35.1921", "woe-label": "Kriti, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4704, -941], [4670, -949], [4591, -899], [4634, -868], [4696, -877], [4687, -919], [4704, -941]]], [[[6746, -833], [6718, -854], [6686, -849], [6690, -830], [6746, -833]]], [[[7267, -743], [7243, -735], [7250, -717], [7276, -723], [7267, -743]]], [[[6106, 24], [6105, 10], [6039, 29], [6084, 65], [6106, 24]]], [[[3918, -185], [3963, -164], [3945, -83], [3990, 11], [3974, 48], [3996, 114], [3981, 155], [3999, 230], [4030, 287], [4034, 193], [4077, 123], [4129, 111], [4174, 132], [4189, 196], [4167, 278], [4181, 293], [4165, 343], [4204, 402], [4251, 378], [4237, 351], [4251, 230], [4299, 166], [4532, 142], [4588, 154], [4604, 194], [4651, 196], [4635, 251], [4677, 269], [4735, 262], [4763, 245], [4789, 177], [4763, 132], [4712, 123], [4634, 135], [4623, 109], [4768, 37], [4852, 79], [4867, 8], [4866, -77], [4879, -98], [4964, -123], [5075, -90], [5121, -101], [5148, -85], [5286, -55], [5365, -14], [5447, 2], [5573, -19], [5600, -33], [5669, -1], [5748, -32], [5765, 0], [5826, -14], [5871, -41], [5873, -106], [5939, -125], [6054, -113], [6073, -131], [6123, -133], [6193, -121], [6244, -131], [6281, -120], [6345, -187], [6417, -189], [6615, -110], [6642, -105], [6703, -125], [6778, -124], [6726, -189], [6733, -235], [6749, -202], [6770, -219], [6722, -318], [6714, -362], [6735, -438], [6805, -461], [6849, -463], [6887, -409], [6931, -387], [6948, -355], [7052, -336], [7121, -288], [7164, -275], [7231, -302], [7217, -317], [7277, -328], [7331, -294], [7322, -283], [7407, -190], [7451, -184], [7450, -141], [7484, -141], [7474, -169], [7416, -228], [7441, -308], [7461, -343], [7489, -323], [7444, -397], [7456, -419], [7392, -572], [7274, -632], [7220, -637], [7189, -606], [7111, -606], [7065, -579], [7034, -595], [6988, -588], [6840, -635], [6576, -627], [6455, -674], [6339, -661], [6197, -677], [6074, -730], [6002, -725], [5934, -753], [5908, -743], [5834, -764], [5789, -753], [5475, -767], [5496, -726], [5492, -667], [5500, -596], [5479, -541], [5434, -512], [5321, -511], [5251, -498], [5210, -451], [5170, -426], [5123, -430], [5046, -392], [5044, -367], [4905, -381], [4809, -368], [4791, -345], [4764, -357], [4634, -363], [4571, -358], [4516, -302], [4406, -306], [4353, -298], [4311, -272], [4254, -300], [4164, -297], [4128, -313], [4110, -296], [4006, -292], [4002, -269], [3962, -249], [3918, -207], [3918, -185]]]]}}, {"type": "Feature", "id": "GR.MC", "properties": {"hc-group": "admin1", "hc-middle-x": 0.56, "hc-middle-y": 0.37, "hc-key": "gr-mc", "hc-a2": "MC", "labelrank": "6", "hasc": "GR.MC", "alt-name": "Macedonia Central", "woe-id": "12577884", "subregion": null, "fips": "GR05", "postal-code": "MC", "name": "<PERSON><PERSON><PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "22.8519", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "40.8836", "woe-label": "<PERSON><PERSON><PERSON>, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[4436, 7600], [4456, 7579], [4397, 7606], [4420, 7630], [4436, 7600]]], [[[4536, 7700], [4537, 7610], [4533, 7595], [4439, 7672], [4395, 7666], [4333, 7677], [4246, 7641], [4211, 7657], [4174, 7606], [4169, 7554], [4188, 7505], [4243, 7459], [4268, 7414], [4374, 7367], [4408, 7365], [4461, 7318], [4521, 7283], [4505, 7240], [4533, 7215], [4523, 7157], [4545, 7166], [4554, 7124], [4517, 7083], [4530, 7059], [4499, 7039], [4521, 7026], [4435, 7031], [4415, 7081], [4330, 7140], [4324, 7112], [4275, 7203], [4285, 7245], [4145, 7438], [4082, 7472], [3984, 7480], [3917, 7519], [3863, 7534], [3815, 7532], [3776, 7508], [3742, 7425], [3773, 7335], [3817, 7325], [3898, 7205], [3933, 7166], [4057, 7097], [4137, 7078], [4169, 7007], [4230, 6991], [4176, 6963], [4073, 6980], [3899, 7056], [3870, 7061], [3767, 7041], [3789, 7118], [3759, 7190], [3703, 7264], [3726, 7373], [3720, 7429], [3697, 7468], [3649, 7495], [3519, 7533], [3457, 7579], [3377, 7611], [3356, 7651], [3300, 7695], [3257, 7710], [3211, 7707], [3222, 7731], [3179, 7803], [3187, 7812], [3134, 7888], [3160, 7898], [3276, 7909], [3326, 7964], [3319, 7992], [3278, 8018], [3288, 8046], [3242, 8108], [3175, 8101], [3180, 8048], [3158, 8015], [3105, 7995], [3084, 8007], [3045, 7972], [3050, 7921], [3012, 7903], [2959, 7929], [2931, 7845], [2874, 7859], [2846, 7839], [2880, 7777], [2877, 7754], [2941, 7684], [2891, 7594], [2854, 7469], [2806, 7371], [2802, 7336], [2823, 7284], [2826, 7197], [2860, 7137], [2921, 7083], [2989, 7043], [2989, 7009], [2913, 6997], [2874, 7017], [2778, 6992], [2734, 7009], [2698, 7046], [2672, 7093], [2593, 7151], [2557, 7191], [2544, 7236], [2549, 7304], [2502, 7351], [2451, 7335], [2380, 7258], [2351, 7289], [2368, 7376], [2306, 7392], [2269, 7360], [2261, 7383], [2267, 7437], [2331, 7477], [2355, 7558], [2300, 7585], [2283, 7652], [2244, 7698], [2235, 7764], [2168, 7755], [2149, 7788], [2067, 7837], [2007, 7926], [2023, 7987], [1992, 8031], [1992, 8074], [2022, 8141], [1996, 8141], [1865, 8179], [1895, 8267], [1855, 8283], [1816, 8419], [1859, 8415], [1927, 8432], [1952, 8470], [1895, 8511], [1888, 8565], [1913, 8582], [1921, 8629], [1978, 8674], [2055, 8765], [2074, 8833], [2141, 8873], [2216, 8893], [2239, 8914], [2303, 8865], [2398, 8923], [2447, 8921], [2538, 8897], [2542, 8871], [2629, 8874], [2677, 8847], [2770, 8857], [2827, 8850], [2875, 8860], [2916, 8902], [2922, 8940], [2954, 8941], [3010, 8880], [3037, 8920], [3049, 8980], [3056, 9103], [3069, 9152], [3104, 9182], [3157, 9191], [3263, 9182], [3495, 9143], [3571, 9156], [3594, 9174], [3606, 9229], [3650, 9261], [3698, 9274], [3743, 9229], [3767, 9232], [3846, 9276], [3962, 9271], [4039, 9231], [4092, 9239], [4085, 9204], [4103, 9137], [4202, 9088], [4181, 9006], [4190, 8983], [4245, 8935], [4315, 8849], [4376, 8803], [4438, 8787], [4474, 8751], [4495, 8772], [4539, 8773], [4587, 8708], [4670, 8646], [4675, 8580], [4658, 8519], [4575, 8449], [4550, 8411], [4464, 8346], [4363, 8323], [4359, 8312], [4306, 8311], [4241, 8284], [4189, 8245], [4167, 8179], [4198, 8122], [4242, 8102], [4286, 8012], [4334, 7974], [4400, 7974], [4430, 7936], [4421, 7915], [4340, 7904], [4317, 7856], [4326, 7815], [4375, 7741], [4431, 7703], [4488, 7688], [4536, 7700]]]]}}, {"type": "Feature", "id": "GR.MA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.29, "hc-middle-y": 0.39, "hc-key": "gr-ma", "hc-a2": "MA", "labelrank": "6", "hasc": "GR.MA", "alt-name": "Mount Athos|Holy Mountain", "woe-id": "12577877", "subregion": null, "fips": "GR15", "postal-code": "MA", "name": "Ayion Oros", "country": "Greece", "type-en": "Autonomous Monastic State", "region": "Macedonia", "longitude": "24.1925", "woe-name": "Ayion Oros", "latitude": "40.2922", "woe-label": "Agio Oros, GR, Greece", "type": "Aftonomi Monastiki Politia"}, "geometry": {"type": "Polygon", "coordinates": [[[4533, 7595], [4537, 7610], [4536, 7700], [4545, 7708], [4521, 7794], [4545, 7796], [4595, 7708], [4661, 7654], [4684, 7666], [4745, 7645], [4774, 7592], [4809, 7591], [4848, 7548], [4913, 7453], [4988, 7378], [5001, 7336], [4962, 7305], [4889, 7298], [4872, 7348], [4815, 7389], [4732, 7526], [4684, 7549], [4588, 7566], [4533, 7595]]]}}, {"type": "Feature", "id": "GR.MT", "properties": {"hc-group": "admin1", "hc-middle-x": 0.34, "hc-middle-y": 0.47, "hc-key": "gr-mt", "hc-a2": "MT", "labelrank": "6", "hasc": "GR.MT", "alt-name": "Macedonia East and Thrace", "woe-id": "12577878", "subregion": null, "fips": "GR01", "postal-code": "MT", "name": "<PERSON><PERSON><PERSON><PERSON> kai <PERSON>ki", "country": "Greece", "type-en": "Region", "region": null, "longitude": "25.2076", "woe-name": "<PERSON><PERSON><PERSON><PERSON> kai <PERSON>ki", "latitude": "41.1043", "woe-label": "Ana<PERSON><PERSON><PERSON>, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[6265, 7861], [6334, 7903], [6400, 7915], [6496, 7888], [6514, 7875], [6545, 7834], [6529, 7775], [6419, 7734], [6351, 7759], [6271, 7821], [6244, 7861], [6265, 7861]]], [[[5162, 8216], [5201, 8274], [5289, 8350], [5328, 8331], [5395, 8319], [5410, 8282], [5447, 8266], [5437, 8208], [5461, 8171], [5437, 8143], [5457, 8066], [5339, 8026], [5292, 7988], [5218, 8072], [5143, 8093], [5137, 8136], [5162, 8216]]], [[[4092, 9239], [4125, 9271], [4244, 9275], [4271, 9319], [4377, 9344], [4408, 9373], [4473, 9332], [4532, 9373], [4585, 9360], [4593, 9384], [4587, 9469], [4621, 9485], [4702, 9470], [4721, 9448], [4743, 9487], [4781, 9516], [4824, 9528], [4866, 9492], [4866, 9457], [4945, 9459], [5044, 9473], [5067, 9507], [5127, 9526], [5151, 9505], [5173, 9408], [5210, 9369], [5228, 9322], [5285, 9319], [5278, 9304], [5328, 9301], [5372, 9270], [5438, 9197], [5461, 9196], [5471, 9266], [5568, 9280], [5604, 9258], [5796, 9179], [5911, 9118], [5961, 9050], [6039, 9035], [6235, 9102], [6295, 9103], [6349, 9159], [6452, 9153], [6513, 9135], [6543, 9160], [6597, 9169], [6652, 9204], [6736, 9148], [6782, 9168], [6827, 9167], [6899, 9210], [7014, 9238], [7073, 9377], [7039, 9426], [7059, 9488], [7050, 9526], [7016, 9552], [7016, 9588], [6987, 9652], [6941, 9668], [6917, 9751], [6930, 9780], [6985, 9813], [7080, 9826], [7105, 9851], [7131, 9844], [7179, 9798], [7278, 9800], [7331, 9766], [7416, 9743], [7403, 9701], [7537, 9615], [7558, 9649], [7568, 9548], [7588, 9426], [7610, 9351], [7617, 9287], [7602, 9227], [7566, 9209], [7505, 9242], [7447, 9177], [7339, 9093], [7265, 9083], [7271, 9044], [7255, 8980], [7275, 8926], [7254, 8878], [7280, 8867], [7261, 8826], [7293, 8753], [7324, 8738], [7309, 8693], [7281, 8684], [7309, 8635], [7235, 8573], [7231, 8541], [7195, 8563], [7171, 8508], [7156, 8529], [7132, 8465], [7141, 8448], [7079, 8392], [7052, 8321], [6991, 8270], [6947, 8274], [6925, 8363], [6899, 8385], [6963, 8402], [6937, 8420], [6899, 8395], [6834, 8438], [6795, 8448], [6769, 8435], [6658, 8451], [6575, 8442], [6423, 8477], [6380, 8459], [6345, 8467], [6284, 8507], [6205, 8520], [6111, 8574], [6062, 8589], [6023, 8549], [5922, 8572], [5867, 8603], [5874, 8635], [5908, 8655], [5871, 8676], [5754, 8669], [5755, 8643], [5699, 8610], [5697, 8569], [5655, 8567], [5549, 8510], [5477, 8429], [5434, 8447], [5390, 8427], [5313, 8468], [5281, 8435], [5209, 8540], [5199, 8579], [5126, 8594], [5078, 8562], [5053, 8573], [4993, 8554], [4970, 8519], [4900, 8466], [4923, 8440], [4897, 8420], [4903, 8392], [4933, 8391], [4661, 8229], [4618, 8219], [4515, 8236], [4383, 8307], [4359, 8312], [4363, 8323], [4464, 8346], [4550, 8411], [4575, 8449], [4658, 8519], [4675, 8580], [4670, 8646], [4587, 8708], [4539, 8773], [4495, 8772], [4474, 8751], [4438, 8787], [4376, 8803], [4315, 8849], [4245, 8935], [4190, 8983], [4181, 9006], [4202, 9088], [4103, 9137], [4085, 9204], [4092, 9239]]]]}}, {"type": "Feature", "id": "GR.GW", "properties": {"hc-group": "admin1", "hc-middle-x": 0.62, "hc-middle-y": 0.66, "hc-key": "gr-gw", "hc-a2": "GW", "labelrank": "6", "hasc": "GR.GW", "alt-name": "Greece West", "woe-id": "12577880", "subregion": null, "fips": "GR38", "postal-code": "GW", "name": "Dytiki <PERSON>", "country": "Greece", "type-en": "Region", "region": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longitude": "21.7273", "woe-name": "Dytiki <PERSON>", "latitude": "38.0299", "woe-label": "Dytiki <PERSON>, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[1659, 3064], [1651, 3090], [1571, 3253], [1503, 3351], [1373, 3443], [1324, 3502], [1252, 3526], [1228, 3519], [1212, 3478], [1197, 3532], [1216, 3615], [1197, 3686], [1168, 3721], [1115, 3751], [987, 3795], [969, 3858], [1006, 3963], [1044, 3936], [1098, 3960], [1186, 4043], [1203, 4037], [1246, 4073], [1238, 4095], [1195, 4065], [1301, 4226], [1310, 4262], [1314, 4381], [1355, 4326], [1353, 4361], [1436, 4339], [1496, 4291], [1592, 4266], [1681, 4307], [1715, 4346], [1766, 4445], [1795, 4485], [1901, 4553], [1982, 4535], [2031, 4543], [2109, 4505], [2130, 4456], [2192, 4423], [2222, 4432], [2258, 4402], [2279, 4346], [2358, 4302], [2396, 4301], [2463, 4279], [2486, 4289], [2517, 4263], [2505, 4202], [2534, 4150], [2477, 4011], [2358, 3956], [2333, 3825], [2359, 3781], [2350, 3758], [2280, 3732], [2247, 3696], [2204, 3695], [2200, 3717], [2134, 3712], [2101, 3676], [2079, 3675], [1915, 3761], [1906, 3805], [1861, 3796], [1787, 3682], [1780, 3620], [1804, 3543], [1801, 3373], [1885, 3358], [1996, 3265], [2031, 3220], [1995, 3193], [1969, 3215], [1916, 3161], [1916, 3118], [1847, 3050], [1806, 3062], [1761, 3053], [1679, 3072], [1659, 3064]]], [[[1905, 4626], [1881, 4640], [1820, 4590], [1794, 4536], [1739, 4583], [1667, 4585], [1613, 4566], [1596, 4576], [1516, 4520], [1501, 4492], [1443, 4510], [1386, 4550], [1406, 4554], [1460, 4515], [1446, 4612], [1379, 4602], [1373, 4649], [1317, 4672], [1308, 4765], [1264, 4811], [1250, 4796], [1298, 4711], [1276, 4654], [1203, 4618], [1196, 4592], [1146, 4555], [1094, 4575], [1106, 4546], [1051, 4518], [1033, 4570], [966, 4582], [1036, 4666], [971, 4686], [971, 4733], [997, 4709], [999, 4796], [966, 4806], [989, 4874], [978, 4882], [914, 4838], [893, 4899], [906, 4996], [881, 5025], [853, 5097], [806, 5100], [767, 5141], [742, 5267], [715, 5306], [607, 5243], [597, 5276], [556, 5311], [584, 5394], [646, 5403], [604, 5447], [598, 5522], [614, 5551], [627, 5513], [669, 5491], [732, 5549], [730, 5496], [775, 5497], [797, 5538], [816, 5521], [863, 5524], [925, 5500], [921, 5446], [992, 5391], [1010, 5423], [1002, 5453], [1038, 5461], [1099, 5402], [1110, 5420], [1064, 5492], [1066, 5541], [1089, 5559], [1089, 5596], [1002, 5700], [993, 5659], [971, 5664], [983, 5722], [1027, 5748], [1178, 5746], [1248, 5771], [1314, 5823], [1342, 5831], [1340, 5780], [1379, 5713], [1371, 5672], [1339, 5614], [1343, 5592], [1462, 5514], [1466, 5424], [1524, 5388], [1630, 5351], [1630, 5330], [1538, 5279], [1529, 5233], [1564, 5167], [1668, 5101], [1777, 5107], [1786, 5063], [1858, 5100], [1866, 5163], [1929, 5156], [2049, 5215], [2097, 5172], [2076, 5112], [2081, 5068], [2064, 5028], [2068, 4981], [2052, 4941], [2087, 4901], [2066, 4858], [2070, 4810], [2033, 4778], [1988, 4770], [1937, 4728], [1911, 4682], [1905, 4626]]]]}}, {"type": "Feature", "id": "GR.MW", "properties": {"hc-group": "admin1", "hc-middle-x": 0.53, "hc-middle-y": 0.51, "hc-key": "gr-mw", "hc-a2": "MW", "labelrank": "6", "hasc": "GR.MW", "alt-name": "Macedonia West", "woe-id": "12577881", "subregion": null, "fips": "GR08", "postal-code": "MW", "name": "Dytiki Makedonia", "country": "Greece", "type-en": "Region", "region": null, "longitude": "21.4551", "woe-name": "Dytiki Makedonia", "latitude": "40.3834", "woe-label": "Dytiki <PERSON>donia, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "Polygon", "coordinates": [[[654, 7650], [693, 7732], [695, 7810], [708, 7835], [744, 7845], [766, 7888], [814, 7898], [867, 7876], [897, 7896], [924, 7961], [992, 8011], [1024, 8151], [1009, 8226], [1086, 8267], [1141, 8338], [1124, 8414], [1090, 8410], [1048, 8368], [1088, 8329], [1078, 8294], [1002, 8237], [943, 8304], [921, 8350], [948, 8396], [1001, 8427], [1020, 8471], [1065, 8460], [1084, 8425], [1132, 8483], [1209, 8504], [1341, 8485], [1400, 8502], [1444, 8544], [1501, 8555], [1596, 8539], [1646, 8491], [1679, 8484], [1730, 8526], [1754, 8525], [1770, 8562], [1804, 8576], [1888, 8565], [1895, 8511], [1952, 8470], [1927, 8432], [1859, 8415], [1816, 8419], [1855, 8283], [1895, 8267], [1865, 8179], [1996, 8141], [2022, 8141], [1992, 8074], [1992, 8031], [2023, 7987], [2007, 7926], [2067, 7837], [2149, 7788], [2168, 7755], [2235, 7764], [2244, 7698], [2283, 7652], [2300, 7585], [2355, 7558], [2331, 7477], [2267, 7437], [2261, 7383], [2183, 7252], [2125, 7194], [2087, 7112], [2039, 7086], [1956, 7016], [1994, 6992], [2008, 6904], [1988, 6869], [1925, 6881], [1835, 6863], [1793, 6867], [1674, 6901], [1497, 6930], [1426, 6896], [1397, 6858], [1322, 6829], [1286, 6845], [1286, 6866], [1209, 6914], [1166, 6918], [1052, 6887], [1032, 6895], [1010, 6942], [997, 7030], [961, 7067], [994, 7129], [999, 7175], [981, 7191], [933, 7170], [903, 7214], [889, 7281], [871, 7297], [908, 7375], [895, 7442], [865, 7512], [816, 7576], [779, 7673], [734, 7664], [693, 7633], [654, 7650]]]}}, {"type": "Feature", "id": "GR.EP", "properties": {"hc-group": "admin1", "hc-middle-x": 0.5, "hc-middle-y": 0.46, "hc-key": "gr-ep", "hc-a2": "EP", "labelrank": "6", "hasc": "GR.EP", "alt-name": "E<PERSON><PERSON>", "woe-id": "12577883", "subregion": null, "fips": "GR17", "postal-code": "EP", "name": "Ipeiro<PERSON>", "country": "Greece", "type-en": "Region", "region": null, "longitude": "20.6925", "woe-name": "Ipeiro<PERSON>", "latitude": "39.6365", "woe-label": "Ipiros, GR, Greece", "type": "Diamerismata"}, "geometry": {"type": "Polygon", "coordinates": [[[971, 5664], [942, 5638], [967, 5617], [900, 5629], [856, 5668], [830, 5667], [816, 5709], [771, 5730], [728, 5677], [713, 5732], [696, 5732], [700, 5781], [665, 5786], [630, 5756], [648, 5739], [636, 5691], [602, 5639], [680, 5578], [675, 5554], [619, 5581], [572, 5539], [534, 5615], [552, 5641], [520, 5731], [413, 5815], [323, 5938], [277, 5978], [285, 6067], [244, 6079], [209, 6071], [179, 6093], [144, 6080], [96, 6107], [59, 6155], [59, 6204], [-27, 6325], [36, 6333], [22, 6367], [-23, 6410], [24, 6404], [36, 6429], [-37, 6465], [-120, 6468], [-101, 6531], [-67, 6539], [-49, 6581], [-55, 6613], [-95, 6635], [-164, 6693], [-257, 6729], [-249, 6741], [-163, 6708], [-76, 6645], [-33, 6636], [-26, 6667], [24, 6669], [29, 6703], [71, 6732], [93, 6768], [66, 6819], [65, 6875], [96, 6888], [183, 6852], [204, 6872], [212, 6930], [160, 7024], [127, 7063], [113, 7158], [122, 7174], [202, 7177], [234, 7256], [273, 7283], [313, 7277], [354, 7292], [447, 7282], [523, 7314], [559, 7387], [551, 7442], [597, 7506], [597, 7569], [614, 7619], [654, 7650], [693, 7633], [734, 7664], [779, 7673], [816, 7576], [865, 7512], [895, 7442], [908, 7375], [871, 7297], [889, 7281], [903, 7214], [933, 7170], [981, 7191], [999, 7175], [994, 7129], [961, 7067], [997, 7030], [1010, 6942], [1032, 6895], [1052, 6887], [1166, 6918], [1209, 6914], [1286, 6866], [1286, 6845], [1253, 6855], [1194, 6814], [1172, 6771], [1190, 6636], [1103, 6656], [1062, 6625], [1074, 6533], [1124, 6477], [1125, 6391], [1105, 6352], [1129, 6301], [1132, 6249], [1188, 6201], [1203, 6146], [1245, 6113], [1330, 6106], [1375, 6025], [1308, 5905], [1342, 5831], [1314, 5823], [1248, 5771], [1178, 5746], [1027, 5748], [983, 5722], [971, 5664]]]}}]}