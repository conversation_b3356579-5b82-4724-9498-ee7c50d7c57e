<template>
  <!-- 访问时长设置 -->
  <titan-dialog
    v-if="showDialog"
    :title="title"
    :visible.sync="showDialog"
    destroy-on-close
    width="30%"
    :config="{
      showCancel: true,
      showConfirm: true
    }"
    @confirm="handleConfirm"
    @cancel="handleClose"
  >
    <titan-form
      ref="form"
      :model="visitForm"
      :column="column"
      label-position="top"
      size="small"
    />
  </titan-dialog>
</template>

<script>
export default {
  name: 'VisitTimeSet',
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visitForm: {
        image_time: {
          min: 5,
          max: 10
        },
        video_time: {
          seconds: 10,
          min: 30,
          max: 70
        }
      }
    };
  },
  computed: {
    title() {
      return this.$t('schema.访问时长设置');
    },
    showDialog: {
      set(val) {
        this.$emit('update:visible', val);
      },
      get() {
        return this.visible;
      }
    },
    column() {
      return [
        {
          prop: 'image_time',
          label: this.$t('schema["图文类"]'),
          rules: [
            {
              validator: this.validateImageSeconds,
              trigger: 'change'
            }
          ],
          renderLabel: (h, form) => (
            <div class='visit-time-set'>
              <p class='label'>
                { this.$t('schema["图文类"]') }：
              </p>
              <p class='tip'>
                { this.$t('schema["最短1S, 最长60S"]') }
              </p>
            </div>
          ),
          render: (h, form) => (
            <div class='time-box flex'>
              <el-input-number
                v-model={form.image_time.min}
                min={1}
                max={59}
                controls-position='right'
                onChange={this.handleImageMinChange}
              />
              <p class='cut-line'>
                --
              </p>
              <el-input-number
                v-model={form.image_time.max}
                min={2}
                max={60}
                controls-position='right'
                onChange={this.handleImageMaxChange}
              />
              <p class='cut-line'>
                { this.$t('schema["秒"]') }
              </p>
            </div>
          )
        },
        {
          prop: 'video_time',
          label: this.$t('schema["视频类"]'),
          rules: [
            {
              validator: this.validateVideoPercent,
              trigger: 'change'
            }
          ],
          renderLabel: (h, form) => (
            <div class='visit-time-set'>
              <p class='label'>
                { this.$t('schema["视频类"]') }：
              </p>
              <p class='tip'>
                { this.$t('schema["最短10S, 最长占视频总长度的30%--100%"]') }
              </p>
            </div>
          ),
          render: (h, form) => (
            <div class='time-box'>
              <p class='video-tip'>
                { this.$t('schema["最短访问时长"]') }
              </p>
              <div class='flex'>
                <el-input-number
                  v-model={form.video_time.seconds}
                  min={10}
                  controls-position='right'
                />
                <p class='cut-line'>
                  { this.$t('schema["秒"]') }
                </p>
              </div>
              <p class='video-tip mt5'>
                { this.$t('schema["最长访问时长为视频总长度的"]') }
              </p>
              <div class='flex'>
                <el-input-number
                  v-model={form.video_time.min}
                  min={30}
                  max={99}
                  controls-position='right'
                  onChange={this.handleVideoMinChange}
                />
                <p class='cut-line'>
                  --
                </p>
                <el-input-number
                  v-model={form.video_time.max}
                  min={31}
                  max={100}
                  controls-position='right'
                  onChange={this.handleVideoMaxChange}
                />
                <p class='cut-line'>
                  %
                </p>
              </div>
            </div>
          )
        }
      ];
    }
  },
  created() {
    if (this.isEdit) {
      const timeList = this.form.readingTimeList;
      this.visitForm = {
        image_time: {
          min: timeList[0].min_time,
          max: timeList[0].max_time
        },
        video_time: {
          seconds: timeList[1].min_time,
          min: timeList[1].max_time_by_total_length_ratio[0],
          max: timeList[1].max_time_by_total_length_ratio[1]
        }
      };
    }
  },
  methods: {
    handleImageMinChange(val) {
      if (val < 1) {
        this.visitForm.image_min = 1;
      } else if (val > 60) {
        this.visitForm.image_min = 60;
      } else if (val >= this.visitForm.image_max) {
        this.visitForm.image_min = this.visitForm.image_max - 1;
      }
    },
    handleImageMaxChange(val) {
      if (val < 1) {
        this.visitForm.image_min = 1;
      } else if (val > 60) {
        this.visitForm.image_max = 60;
      } else if (val <= this.visitForm.image_min) {
        this.visitForm.image_max = this.visitForm.image_min + 1;
      }
    },
    handleVideoMinChange(val) {
      if (val < 31) {
        this.visitForm.video_time.min = 30;
      } else if (val > 100) {
        this.visitForm.video_time.min = 99;
      } else if (val >= this.visitForm.video_time.max) {
        this.$nextTick(() => {
          this.visitForm.video_time.min = this.visitForm.video_time.max - 1;
        });
      }
    },
    handleVideoMaxChange(val) {
      if (val > 100) {
        this.visitForm.video_time.max = 100;
      } else if (val <= this.visitForm.video_time.min) {
        this.$nextTick(() => {
          this.visitForm.video_time.max = this.visitForm.video_time.min + 1;
        });
      }
    },
    validateImageSeconds(rule, value, callback) {
      if (value.min && value.max && (value.min >= value.max || value.max <= value.min)) {
        callback(new Error(this.$t('accountCenter["请更改访问时长为有效范围"]')));
      }
      callback();
    },
    validateVideoPercent(rule, value, callback) {
      if (value.min && value.max && (value.min >= value.max || value.max <= value.min)) {
        callback(new Error(this.$t('accountCenter["请更改访问时长百分比为有效范围"]')));
      }
      callback();
    },
    handleClose() {
      this.showDialog = false;
    },
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const query = {};
          Object.assign(query, this.visitForm);
          if (!query.image_time.min) {
            query.image_time.min = 5;
          }
          if (!query.image_time.max) {
            query.image_time.max = 10;
          }
          if (!query.video_time.seconds) {
            query.video_time.seconds = 10;
          }
          if (!query.video_time.min) {
            query.video_time.min = 30;
          }
          if (!query.video_time.max) {
            query.video_time.max = 70;
          }
          const readingTimeList = [
            {
              'type': 'PIC_TEXT', // PIC-TEXT:图文
              'min_time': query.image_time.min, // 访问最短时间,
              'max_time': query.image_time.max, // 访问最长时间
              'actionTypes': ['VISIT']
            },
            {
              'type': 'VIDEO', // VIDEO:视频
              'min_time': query.video_time.seconds, // 访问最短时间,
              'max_time_by_total_length_ratio': [
                query.video_time.min,
                query.video_time.max
              ], // 最长时间为总时长的百分之多少
              'actionTypes': ['VISIT']
            }
          ];
          this.$emit('handleConfirm', readingTimeList);
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .flex {
  display: flex;
  align-items: center;
}
::v-deep .time-box {
  padding: 14px;
  border: 1px solid #c2d0e2;
  border-radius: 8px;
  .cut-line {
    margin: 0 20px;
  }
  .video-tip {
    color: #757d88;
    font-size: 14px;
    margin-bottom: 5px;
  }
  .mt5 {
    margin-top: 5px;
  }
}
.visit-time-set {
  margin-bottom: 20px;
  .label {
    color: #4f5563;
    font-size: 16px;
  }
  .tip {
    color: #8294b1;
    font-size: 14px;
    margin: 5px 0 10px 0;
  }
  .video-box {
    padding: 14px;
    border-radius: 8px;
    border: 1px solid #c2d0e2;
  }

}
</style>
