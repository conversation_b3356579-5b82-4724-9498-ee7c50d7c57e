import i18n from '@/lang';
export function formatBigNumber(value) {
  if (isNaN(value)) return '-';
  if (!value && value !== 0) return 0;
  if (value >= 1000 && value < 10000) {
    return '1K+';
  } else if (value >= 10000 && value < 100000) {
    return i18n.t('unit["1W+"]');
  } else if (value >= 100000 && value < 100000000) {
    return i18n.t('unit["10W+"]');
  } else if (value >= 100000000) {
    return i18n.t('unit["1亿+"]');
  }
  return value;
}

/**
 * 页尾：
 * 每3位用,号隔开，
 * 当数量级为万以下时，直接展示数字，如：3,333
 * 当数量级为万至千万时，在括号内增加文字说明192，837（约19.2w）
 * 当数量级为亿及以上时，在括号内增加文字说明937,563,732（约9.3亿）
 * 向下取整；
 */
export function formatNumberCut3(value) {
  if (isNaN(value) || !value) return '0';
  const intNum = parseInt(value, 10);

  // 添加千位分隔符逗号
  // eslint-disable-next-line require-unicode-regexp
  const formattedValue = Number(value).toLocaleString();

  if (intNum < 10000) {
    return formattedValue;
  } else if (intNum >= 10000 && intNum < 100000000) {
    if (i18n.locale === 'en') {
      const approxValue = Math.floor(value / 100) / 10; // 除以1000，得到以“K”为单位的近似值，保留一位小数
      return formattedValue + '(about ' + approxValue.toLocaleString() + 'K)';
    } else {
      const approxValue = Math.floor(value / 1000) / 10; // 除以10000，得到以“万”为单位的近似值，保留一位小数
      return formattedValue + '(约' + approxValue.toLocaleString() + '万)';
    }
  } else if (i18n.locale === 'en') {
    const approxValue = Math.floor(value / 100000) / 10; // 除以1000000，得到以“M”为单位的近似值，保留一位小数
    return formattedValue + '(about' + approxValue.toLocaleString() + 'M)';
  } else {
    const approxValue = Math.floor(value / 10000000) / 10; // 除以100000000，得到以“亿”为单位的近似值，保留一位小数
    return formattedValue + '(约' + approxValue.toLocaleString() + '亿)';
  }
}

/**
 * 所有页面，卡片内：
 * 每3位用,号隔开，
 * 当数量级为万以下时，直接展示数字，如：3,333
 * 保留小数点后两位，万级以上亿级以下使用w单位，超过则用单位亿；
 */
export function formatNumber2Decimals(value) {
  if (isNaN(value) || !value) return '0';
  const intNum = parseInt(value, 10);
  if (intNum < 10000) {
    return Number(value).toLocaleString();
  } else if (intNum >= 10000 && intNum < 100000000) {
    const wholePart = Math.floor(value / 10000);
    const decimalPart = Math.floor((value % 10000) / 1000);
    const formattedValue = decimalPart > 0 ? `${wholePart.toLocaleString()}.${decimalPart}w+` : `${wholePart.toLocaleString()}w+`;
    return formattedValue;
  } else {
    const wholePart = Math.floor(value / 100000000);
    const decimalPart = Math.floor((value % 100000000) / 10000000);
    const formattedValue = decimalPart > 0 ? `${wholePart.toLocaleString()}.${decimalPart}亿+` : `${wholePart.toLocaleString()}亿+`;
    return formattedValue;
  }
}

export function formatNumberThousands(value) {
  if (isNaN(value) || !value) return '0';
  const intNum = parseInt(value, 10);
  if (intNum < 1000) {
    return Number(value).toLocaleString();
  } else {
    const wholePart = Math.floor(value / 1000);
    const decimalPart = Math.floor((value % 1000) / 100);
    const formattedValue = decimalPart > 0 ? `${wholePart.toLocaleString()}.${decimalPart}K+` : `${wholePart.toLocaleString()}K+`;
    return formattedValue;
  }
}

export function formatAge(time, label = '') {
  if (!time) return '';
  const t = new Date();
  const b = new Date(time);
  const age = t.getFullYear() - b.getFullYear();
  const dw = i18n.locale === 'zh' ? '岁' : '';
  return label + (age && age) + dw;
}

export function formatThousandNumber(val) {
  if (val > 999) {
    return (val / 1000).toFixed(1) + 'k';
  }
  return val;
}

