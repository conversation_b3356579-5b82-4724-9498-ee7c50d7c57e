{"title": "Panama", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:5469"}}, "hc-transform": {"default": {"crs": "+proj=lcc +lat_1=8.416666666666666 +lat_0=8.416666666666666 +lon_0=-80 +k_0=0.99989909 +x_0=500000 +y_0=294865.303 +ellps=clrk66 +units=m +no_defs", "scale": 0.00107952874337, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 163696.523606, "yoffset": 429000.186696}}, "features": [{"type": "Feature", "id": "PA.CH", "properties": {"hc-group": "admin1", "hc-middle-x": 0.35, "hc-middle-y": 0.37, "hc-key": "pa-ch", "hc-a2": "CH", "labelrank": "7", "hasc": "PA.CH", "alt-name": null, "woe-id": "2346552", "subregion": null, "fips": "PM02", "postal-code": "CH", "name": "Chiriquí", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-82.0992", "woe-name": "Chiriquí", "latitude": "8.30824", "woe-label": "Chiriquí, PA, Panama", "type": "Provincia"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[299, 7012], [272, 7009], [308, 7077], [349, 7089], [353, 7033], [299, 7012]]], [[[535, 7240], [551, 7216], [486, 7230], [474, 7222], [487, 7164], [436, 7203], [373, 7217], [359, 7246], [376, 7263], [437, 7280], [535, 7240]]], [[[357, 7343], [310, 7330], [361, 7293], [324, 7283], [260, 7250], [207, 7253], [159, 7306], [147, 7331], [201, 7325], [297, 7392], [336, 7381], [357, 7343]]], [[[1560, 6837], [1510, 6943], [1497, 6881], [1477, 6900], [1476, 6969], [1510, 6982], [1535, 6969], [1472, 7084], [1414, 7163], [1397, 7198], [1382, 7157], [1409, 7135], [1357, 7071], [1312, 7097], [1152, 7165], [1049, 7187], [1030, 7199], [1068, 7276], [1021, 7254], [981, 7201], [917, 7250], [890, 7231], [830, 7235], [770, 7254], [728, 7278], [725, 7244], [678, 7143], [652, 7189], [677, 7240], [620, 7220], [579, 7221], [537, 7265], [564, 7279], [543, 7336], [552, 7393], [539, 7410], [558, 7462], [603, 7482], [527, 7475], [495, 7404], [431, 7420], [418, 7444], [426, 7483], [364, 7460], [299, 7470], [310, 7419], [281, 7415], [204, 7382], [144, 7413], [140, 7434], [173, 7447], [149, 7475], [148, 7509], [130, 7495], [118, 7448], [84, 7433], [109, 7370], [-1, 7362], [-77, 7383], [-205, 7435], [-272, 7449], [-345, 7450], [-418, 7441], [-549, 7401], [-605, 7370], [-648, 7325], [-676, 7260], [-681, 7184], [-669, 7106], [-626, 6981], [-678, 6899], [-716, 6920], [-694, 7044], [-790, 7257], [-798, 7316], [-843, 7352], [-904, 7355], [-931, 7392], [-999, 7441], [-997, 7464], [-896, 7523], [-814, 7600], [-660, 7666], [-619, 7715], [-602, 7770], [-596, 7835], [-606, 7964], [-620, 8020], [-673, 8112], [-730, 8176], [-755, 8227], [-753, 8255], [-683, 8304], [-665, 8349], [-688, 8367], [-649, 8406], [-458, 8482], [-403, 8515], [-377, 8556], [-327, 8507], [-202, 8522], [-97, 8516], [-48, 8488], [-28, 8447], [41, 8444], [126, 8416], [279, 8345], [428, 8286], [535, 8270], [571, 8276], [664, 8258], [689, 8148], [671, 7992], [657, 7938], [681, 7877], [621, 7739], [612, 7694], [641, 7677], [699, 7677], [807, 7711], [845, 7672], [903, 7589], [910, 7531], [948, 7474], [1036, 7451], [1207, 7448], [1255, 7433], [1286, 7375], [1339, 7332], [1436, 7328], [1486, 7388], [1514, 7392], [1531, 7355], [1576, 7364], [1617, 7392], [1662, 7485], [1694, 7530], [1776, 7555], [1823, 7543], [1844, 7361], [1839, 7306], [1728, 7167], [1682, 7123], [1667, 7091], [1619, 6907], [1599, 6848], [1560, 6837]]]]}}, {"type": "Feature", "id": "PA.SB", "properties": {"hc-group": "admin1", "hc-middle-x": 0.78, "hc-middle-y": 0.35, "hc-key": "pa-sb", "hc-a2": "SB", "labelrank": "9", "hasc": "PA.SB", "alt-name": null, "woe-id": "2346558", "subregion": null, "fips": "PM08", "postal-code": "SB", "name": "Panama", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-78.92829999999999", "woe-name": "Panama", "latitude": "9.08628", "woe-label": "Panama, PA, Panama", "type": "Provincia"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[6350, 7320], [6280, 7306], [6290, 7263], [6270, 7227], [6246, 7224], [6214, 7290], [6231, 7312], [6242, 7399], [6286, 7425], [6319, 7345], [6350, 7320]]], [[[6823, 7531], [6793, 7525], [6776, 7549], [6789, 7570], [6823, 7531]]], [[[6277, 7612], [6296, 7590], [6309, 7614], [6326, 7575], [6300, 7535], [6231, 7587], [6253, 7631], [6277, 7612]]], [[[6723, 7678], [6739, 7603], [6774, 7575], [6751, 7521], [6776, 7473], [6733, 7386], [6701, 7396], [6662, 7389], [6653, 7410], [6618, 7361], [6614, 7329], [6632, 7308], [6649, 7244], [6604, 7274], [6606, 7301], [6582, 7361], [6527, 7403], [6555, 7479], [6527, 7523], [6542, 7548], [6537, 7611], [6517, 7660], [6524, 7686], [6564, 7676], [6639, 7708], [6663, 7675], [6673, 7716], [6723, 7678]]], [[[6497, 7694], [6440, 7705], [6517, 7741], [6484, 7707], [6497, 7694]]], [[[5432, 8325], [5472, 8292], [5460, 8268], [5421, 8285], [5432, 8325]]], [[[4375, 8592], [4458, 8656], [4536, 8627], [4587, 8623], [4655, 8645], [4709, 8680], [4729, 8758], [4697, 8840], [4774, 9010], [4801, 9039], [4832, 9045], [4889, 9018], [4941, 9007], [4980, 8979], [5036, 8898], [5077, 8888], [5171, 8890], [5236, 8923], [5287, 8987], [5299, 9038], [5355, 9070], [5412, 9090], [5428, 9213], [5460, 9304], [5458, 9364], [5506, 9527], [5574, 9555], [5656, 9573], [5731, 9549], [5784, 9487], [5742, 9453], [5738, 9419], [5851, 9432], [5881, 9450], [5933, 9455], [6003, 9421], [5985, 9382], [6020, 9360], [6029, 9335], [6012, 9299], [5977, 9282], [5954, 9249], [5967, 9204], [6027, 9217], [6153, 9257], [6230, 9260], [6295, 9238], [6335, 9201], [6446, 9202], [6470, 9222], [6456, 9255], [6517, 9295], [6578, 9295], [6582, 9326], [6667, 9326], [6703, 9354], [6842, 9353], [6875, 9375], [6910, 9372], [6966, 9327], [6990, 9377], [7020, 9376], [7151, 9285], [7212, 9281], [7308, 9304], [7333, 9335], [7362, 9338], [7385, 9317], [7411, 9263], [7450, 9286], [7524, 9224], [7554, 9143], [7682, 9132], [7762, 9169], [7782, 9153], [7808, 9072], [7833, 9046], [7986, 9050], [8001, 9074], [8029, 9053], [8038, 9014], [8011, 8963], [8026, 8944], [8154, 8914], [8187, 8894], [8229, 8830], [8181, 8759], [8161, 8697], [8163, 8591], [8134, 8561], [8056, 8535], [8002, 8468], [7967, 8452], [7901, 8453], [7810, 8420], [7775, 8352], [7741, 8335], [7687, 8383], [7586, 8406], [7502, 8377], [7479, 8358], [7521, 8163], [7516, 8113], [7486, 8023], [7468, 7878], [7487, 7852], [7492, 7782], [7516, 7725], [7522, 7684], [7552, 7577], [7559, 7495], [7485, 7565], [7493, 7620], [7472, 7677], [7446, 7627], [7417, 7670], [7408, 7717], [7421, 7779], [7408, 7811], [7333, 7906], [7399, 7897], [7421, 7907], [7380, 7949], [7383, 8014], [7268, 8045], [7282, 8082], [7199, 8070], [7183, 8079], [7155, 8146], [7181, 8167], [7190, 8201], [7179, 8273], [7166, 8273], [7169, 8179], [7142, 8184], [7111, 8150], [7054, 8223], [7003, 8222], [6967, 8244], [6930, 8323], [6901, 8348], [6915, 8374], [6888, 8382], [6837, 8436], [6855, 8381], [6844, 8363], [6786, 8343], [6731, 8393], [6701, 8488], [6459, 8613], [6362, 8634], [6315, 8667], [6319, 8804], [6270, 8842], [6339, 8861], [6371, 8918], [6399, 8893], [6500, 8941], [6458, 8944], [6414, 8904], [6371, 8930], [6306, 8858], [6258, 8850], [6246, 8828], [6302, 8792], [6307, 8753], [6280, 8699], [6255, 8677], [6144, 8707], [6113, 8697], [5965, 8723], [5860, 8712], [5756, 8722], [5715, 8700], [5699, 8713], [5581, 8684], [5565, 8656], [5513, 8624], [5507, 8580], [5488, 8574], [5527, 8498], [5473, 8544], [5426, 8612], [5439, 8528], [5431, 8488], [5404, 8469], [5362, 8484], [5278, 8469], [5252, 8455], [5262, 8422], [5174, 8446], [5186, 8408], [5125, 8398], [5085, 8345], [5077, 8309], [5085, 8199], [5108, 8162], [5078, 8097], [5013, 8063], [5010, 8040], [4948, 8017], [4971, 7994], [5034, 7980], [5094, 8004], [5186, 8052], [5180, 8023], [5044, 7908], [4948, 7875], [4879, 7858], [4859, 7799], [4821, 7788], [4753, 7741], [4735, 7706], [4683, 7657], [4553, 7572], [4478, 7660], [4459, 7707], [4452, 7810], [4435, 7856], [4438, 7931], [4421, 8025], [4375, 8233], [4327, 8373], [4325, 8417], [4342, 8480], [4383, 8542], [4375, 8592]]]]}}, {"type": "Feature", "id": "PA.VR", "properties": {"hc-group": "admin1", "hc-middle-x": 0.58, "hc-middle-y": 0.35, "hc-key": "pa-vr", "hc-a2": "VR", "labelrank": "7", "hasc": "PA.VR", "alt-name": null, "woe-id": "2346560", "subregion": null, "fips": "PM10", "postal-code": "VR", "name": "Veraguas", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-81.1447", "woe-name": "Veraguas", "latitude": "8.225529999999999", "woe-label": "Veraguas, PA, Panama", "type": "Provincia"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[1289, 5532], [1364, 5524], [1341, 5506], [1300, 5435], [1300, 5411], [1267, 5527], [1289, 5532]]], [[[2699, 6013], [2684, 5996], [2629, 5976], [2591, 5998], [2543, 5997], [2517, 5965], [2466, 5938], [2429, 5902], [2379, 5913], [2316, 5896], [2370, 5953], [2459, 5972], [2506, 6027], [2564, 6051], [2620, 6064], [2721, 6053], [2699, 6013]]], [[[1418, 6185], [1479, 6017], [1478, 5997], [1534, 5976], [1543, 5936], [1473, 5905], [1453, 5830], [1498, 5777], [1529, 5716], [1632, 5720], [1694, 5611], [1620, 5587], [1581, 5587], [1516, 5624], [1402, 5638], [1338, 5704], [1303, 5725], [1226, 5790], [1184, 5879], [1165, 5901], [1151, 5950], [1197, 5951], [1237, 6005], [1249, 6059], [1274, 6085], [1308, 6092], [1378, 6140], [1418, 6185]]], [[[3361, 7082], [3260, 7077], [3188, 7000], [3054, 6948], [3083, 6867], [3080, 6838], [3046, 6781], [2954, 6693], [2907, 6681], [2888, 6655], [2881, 6446], [2888, 6404], [2931, 6415], [2972, 6381], [3005, 6274], [3147, 6062], [3202, 6030], [3251, 6015], [3299, 5966], [3295, 5915], [3307, 5836], [3337, 5746], [3384, 5682], [3396, 5639], [3394, 5584], [3476, 5536], [3507, 5489], [3512, 5424], [3310, 5387], [3162, 5382], [3111, 5392], [3092, 5378], [3061, 5392], [3035, 5368], [2985, 5429], [2910, 5462], [2936, 5502], [2935, 5557], [2973, 5595], [2981, 5654], [2974, 5684], [2926, 5713], [2947, 5804], [2938, 5828], [2843, 5931], [2865, 5944], [2881, 6016], [2820, 6077], [2834, 6103], [2745, 6191], [2706, 6212], [2717, 6246], [2772, 6256], [2700, 6314], [2734, 6369], [2670, 6383], [2641, 6403], [2625, 6458], [2671, 6492], [2732, 6499], [2746, 6534], [2693, 6510], [2680, 6560], [2684, 6674], [2644, 6600], [2616, 6575], [2595, 6611], [2595, 6577], [2621, 6547], [2537, 6547], [2497, 6568], [2428, 6586], [2414, 6642], [2382, 6615], [2419, 6573], [2456, 6553], [2445, 6513], [2479, 6448], [2461, 6400], [2380, 6333], [2380, 6384], [2329, 6384], [2380, 6320], [2328, 6320], [2355, 6282], [2418, 6306], [2426, 6285], [2422, 6171], [2379, 6116], [2290, 6159], [2267, 6185], [2202, 6193], [2049, 6236], [1999, 6232], [1979, 6272], [1950, 6295], [1907, 6285], [1853, 6289], [1812, 6307], [1809, 6335], [1880, 6377], [1808, 6398], [1785, 6360], [1721, 6398], [1734, 6427], [1698, 6480], [1709, 6530], [1736, 6575], [1709, 6587], [1673, 6575], [1677, 6616], [1634, 6638], [1653, 6715], [1685, 6703], [1647, 6753], [1660, 6816], [1624, 6808], [1635, 6745], [1600, 6758], [1592, 6792], [1560, 6837], [1599, 6848], [1619, 6907], [1667, 7091], [1682, 7123], [1728, 7167], [1839, 7306], [1844, 7361], [1823, 7543], [1815, 7635], [1823, 7693], [1866, 7796], [1883, 7869], [1887, 7945], [1933, 7921], [1990, 7907], [2030, 7874], [2117, 7867], [2181, 7872], [2297, 7786], [2448, 7744], [2499, 7756], [2470, 7784], [2410, 7885], [2392, 7992], [2398, 8104], [2377, 8241], [2390, 8304], [2626, 8335], [2657, 8331], [2689, 8360], [2833, 8407], [3006, 8476], [3041, 8475], [3045, 8417], [3062, 8378], [3128, 8322], [3226, 8279], [3265, 8235], [3288, 8188], [3262, 8115], [3295, 8032], [3267, 7975], [3245, 7922], [3194, 7861], [3136, 7852], [3114, 7832], [3154, 7797], [3191, 7746], [3234, 7670], [3305, 7482], [3350, 7409], [3343, 7350], [3316, 7257], [3267, 7171], [3275, 7139], [3361, 7082]]]]}}, {"type": "Feature", "id": "PA.BC", "properties": {"hc-group": "admin1", "hc-middle-x": 0.34, "hc-middle-y": 0.53, "hc-key": "pa-bc", "hc-a2": "BC", "labelrank": "9", "hasc": "PA.BC", "alt-name": null, "woe-id": "2346551", "subregion": null, "fips": "PM01", "postal-code": "BC", "name": "Bocas del Toro", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-82.6597", "woe-name": "Bocas del Toro", "latitude": "9.20739", "woe-label": "Bocas del Toro, PA, Panama", "type": "Provincia"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[914, 8950], [900, 8942], [858, 8966], [863, 9015], [897, 9009], [908, 8980], [934, 8969], [914, 8950]]], [[[616, 9337], [664, 9343], [726, 9294], [795, 9256], [814, 9230], [779, 9235], [741, 9221], [712, 9193], [696, 9151], [667, 9175], [675, 9228], [650, 9268], [675, 9256], [659, 9295], [563, 9332], [583, 9356], [616, 9337]]], [[[508, 9323], [500, 9309], [475, 9351], [436, 9392], [349, 9461], [398, 9498], [463, 9511], [514, 9436], [526, 9389], [500, 9358], [508, 9323]]], [[[279, 8345], [126, 8416], [41, 8444], [-28, 8447], [-48, 8488], [-97, 8516], [-202, 8522], [-327, 8507], [-377, 8556], [-430, 8656], [-455, 8672], [-538, 8701], [-622, 8764], [-674, 8789], [-694, 8829], [-724, 8839], [-779, 8816], [-783, 9515], [-762, 9577], [-728, 9588], [-629, 9601], [-599, 9631], [-627, 9651], [-639, 9702], [-661, 9742], [-637, 9788], [-602, 9817], [-569, 9820], [-462, 9777], [-386, 9712], [-354, 9712], [-293, 9624], [-242, 9605], [-184, 9603], [-172, 9626], [-164, 9697], [-151, 9717], [-80, 9698], [-100, 9768], [-78, 9754], [98, 9614], [206, 9505], [274, 9467], [323, 9499], [310, 9443], [259, 9356], [260, 9310], [312, 9264], [285, 9225], [239, 9229], [227, 9184], [265, 9170], [306, 9082], [371, 9042], [431, 9072], [410, 9106], [434, 9099], [460, 9041], [481, 9036], [511, 9093], [526, 9056], [547, 9061], [552, 9100], [586, 9105], [611, 9054], [636, 9067], [634, 8988], [623, 8966], [567, 9000], [491, 9016], [465, 8969], [484, 8902], [446, 8914], [456, 8887], [500, 8864], [510, 8845], [496, 8724], [502, 8706], [546, 8685], [590, 8681], [622, 8698], [688, 8676], [768, 8568], [824, 8568], [936, 8597], [919, 8546], [884, 8526], [836, 8515], [705, 8513], [670, 8496], [646, 8458], [635, 8416], [604, 8430], [587, 8496], [563, 8510], [560, 8545], [581, 8559], [577, 8614], [533, 8642], [467, 8656], [433, 8695], [357, 8716], [364, 8771], [392, 8792], [434, 8847], [369, 8882], [379, 8910], [355, 8935], [327, 8907], [303, 8859], [299, 8813], [226, 8748], [164, 8724], [140, 8700], [160, 8679], [267, 8640], [308, 8605], [332, 8560], [339, 8522], [325, 8494], [279, 8345]]]]}}, {"type": "Feature", "id": "PA.NB", "properties": {"hc-group": "admin1", "hc-middle-x": 0.41, "hc-middle-y": 0.51, "hc-key": "pa-nb", "hc-a2": "NB", "labelrank": "7", "hasc": "PA.NB", "alt-name": null, "woe-id": "28358287", "subregion": null, "fips": "PM01", "postal-code": "NB", "name": "Ngöbe Buglé", "country": "Panama", "type-en": "Indigenous Territory", "region": null, "longitude": "-81.75579999999999", "woe-name": "Ngöbe Buglé", "latitude": "8.68", "woe-label": "Ngöbe Buglé, PA, Panama", "type": "Comarca Indígena"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[1749, 8887], [1792, 8875], [1806, 8850], [1737, 8858], [1727, 8885], [1749, 8887]]], [[[1823, 7543], [1776, 7555], [1694, 7530], [1662, 7485], [1617, 7392], [1576, 7364], [1531, 7355], [1514, 7392], [1486, 7388], [1436, 7328], [1339, 7332], [1286, 7375], [1255, 7433], [1207, 7448], [1036, 7451], [948, 7474], [910, 7531], [903, 7589], [845, 7672], [807, 7711], [699, 7677], [641, 7677], [612, 7694], [621, 7739], [681, 7877], [657, 7938], [671, 7992], [689, 8148], [664, 8258], [571, 8276], [535, 8270], [428, 8286], [279, 8345], [325, 8494], [339, 8522], [332, 8560], [308, 8605], [267, 8640], [160, 8679], [140, 8700], [164, 8724], [226, 8748], [299, 8813], [303, 8859], [327, 8907], [355, 8935], [379, 8910], [369, 8882], [434, 8847], [392, 8792], [364, 8771], [357, 8716], [433, 8695], [467, 8656], [533, 8642], [577, 8614], [581, 8559], [560, 8545], [563, 8510], [587, 8496], [604, 8430], [635, 8416], [646, 8458], [670, 8496], [705, 8513], [836, 8515], [884, 8526], [919, 8546], [936, 8597], [1024, 8568], [1078, 8591], [1089, 8632], [1126, 8641], [1192, 8600], [1220, 8617], [1328, 8567], [1361, 8583], [1370, 8615], [1364, 8689], [1240, 8759], [1176, 8856], [1072, 8934], [1065, 8951], [1184, 8934], [1203, 8956], [1115, 9006], [1128, 9026], [1166, 9026], [1198, 9003], [1241, 8950], [1291, 8909], [1273, 8889], [1241, 8898], [1279, 8824], [1387, 8811], [1433, 8761], [1462, 8752], [1489, 8691], [1523, 8663], [1633, 8505], [1707, 8426], [1743, 8402], [1795, 8345], [1861, 8323], [1916, 8333], [2012, 8314], [2085, 8321], [2128, 8293], [2227, 8286], [2297, 8300], [2390, 8304], [2377, 8241], [2398, 8104], [2392, 7992], [2410, 7885], [2470, 7784], [2499, 7756], [2448, 7744], [2297, 7786], [2181, 7872], [2117, 7867], [2030, 7874], [1990, 7907], [1933, 7921], [1887, 7945], [1883, 7869], [1866, 7796], [1823, 7693], [1815, 7635], [1823, 7543]]]]}}, {"type": "Feature", "id": "PA.EM", "properties": {"hc-group": "admin1", "hc-middle-x": 0.68, "hc-middle-y": 0.31, "hc-key": "pa-em", "hc-a2": "EM", "labelrank": "7", "hasc": "PA.EM", "alt-name": "Emberá-W<PERSON>naan", "woe-id": "28358286", "subregion": null, "fips": "PM05", "postal-code": "EM", "name": "Emberá", "country": "Panama", "type-en": "Indigenous Territory", "region": null, "longitude": "-78.1396", "woe-name": "Emberá", "latitude": "7.86013", "woe-label": "Emberá, PA, Panama", "type": "Comarca Indígena"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[8112, 6819], [8148, 6794], [8165, 6692], [8253, 6754], [8288, 6718], [8346, 6541], [8354, 6482], [8390, 6465], [8415, 6405], [8401, 6363], [8423, 6201], [8414, 6140], [8338, 6087], [8286, 6138], [8251, 6118], [8232, 6086], [8187, 6107], [8171, 6187], [8065, 6247], [7989, 6224], [7957, 6247], [7868, 6345], [7822, 6421], [7794, 6508], [7758, 6583], [7726, 6598], [7708, 6644], [7710, 6768], [7772, 6779], [7843, 6835], [7901, 6814], [7945, 6845], [7924, 6922], [7897, 6936], [7901, 6971], [7978, 7016], [8008, 6995], [8099, 6874], [8112, 6819]]], [[[8876, 8277], [8902, 8234], [8999, 8156], [9029, 8115], [9105, 8034], [9124, 7972], [9147, 7942], [9240, 7905], [9290, 7846], [9270, 7824], [9255, 7769], [9255, 7727], [9272, 7716], [9327, 7722], [9377, 7696], [9435, 7580], [9451, 7453], [9468, 7387], [9489, 7355], [9560, 7336], [9615, 7233], [9663, 7163], [9453, 7105], [9376, 7087], [9326, 7038], [9264, 7022], [9202, 7022], [9167, 7001], [9142, 7007], [9089, 7070], [9051, 7081], [9033, 7110], [9040, 7140], [9006, 7218], [8935, 7275], [8809, 7326], [8803, 7380], [8763, 7395], [8735, 7448], [8695, 7492], [8682, 7541], [8650, 7589], [8665, 7652], [8654, 7690], [8607, 7738], [8607, 7769], [8569, 7782], [8541, 7819], [8548, 7875], [8499, 7926], [8480, 7996], [8501, 8033], [8537, 8054], [8638, 8203], [8697, 8217], [8735, 8258], [8775, 8279], [8820, 8285], [8876, 8277]]]]}}, {"type": "Feature", "id": "PA.CL", "properties": {"hc-group": "admin1", "hc-middle-x": 0.58, "hc-middle-y": 0.35, "hc-key": "pa-cl", "hc-a2": "CL", "labelrank": "9", "hasc": "PA.CL", "alt-name": null, "woe-id": "2346554", "subregion": null, "fips": "PM04", "postal-code": "CL", "name": "Colón", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-79.9683", "woe-name": "Colón", "latitude": "9.17018", "woe-label": "Colón, PA, Panama", "type": "Provincia"}, "geometry": {"type": "Polygon", "coordinates": [[[3041, 8475], [3115, 8485], [3207, 8601], [3291, 8682], [3427, 8752], [3456, 8783], [3563, 8836], [3696, 8892], [3877, 8950], [4090, 8966], [4196, 9018], [4261, 9025], [4329, 9043], [4443, 9097], [4508, 9172], [4627, 9279], [4667, 9358], [4713, 9377], [4732, 9359], [4732, 9270], [4758, 9249], [4795, 9257], [4822, 9346], [4858, 9321], [4846, 9384], [4871, 9384], [4848, 9416], [4865, 9434], [4921, 9428], [4962, 9410], [5009, 9333], [4991, 9427], [5036, 9485], [5122, 9524], [5153, 9574], [5197, 9604], [5221, 9696], [5268, 9722], [5212, 9731], [5245, 9766], [5307, 9812], [5370, 9818], [5453, 9851], [5486, 9842], [5506, 9788], [5486, 9777], [5512, 9740], [5550, 9766], [5613, 9740], [5627, 9790], [5649, 9767], [5676, 9778], [5745, 9761], [5848, 9750], [6025, 9697], [6110, 9686], [6178, 9715], [6252, 9709], [6329, 9728], [6328, 9534], [6278, 9513], [6223, 9456], [6205, 9387], [6178, 9366], [6112, 9340], [6027, 9280], [6012, 9299], [6029, 9335], [6020, 9360], [5985, 9382], [6003, 9421], [5933, 9455], [5881, 9450], [5851, 9432], [5738, 9419], [5742, 9453], [5784, 9487], [5731, 9549], [5656, 9573], [5574, 9555], [5506, 9527], [5458, 9364], [5460, 9304], [5428, 9213], [5412, 9090], [5355, 9070], [5299, 9038], [5287, 8987], [5236, 8923], [5171, 8890], [5077, 8888], [5036, 8898], [4980, 8979], [4941, 9007], [4889, 9018], [4832, 9045], [4801, 9039], [4774, 9010], [4697, 8840], [4729, 8758], [4709, 8680], [4655, 8645], [4587, 8623], [4536, 8627], [4458, 8656], [4375, 8592], [4326, 8623], [4315, 8642], [4300, 8726], [4275, 8774], [4256, 8737], [4246, 8683], [4220, 8646], [4175, 8619], [4029, 8480], [3974, 8449], [3871, 8473], [3847, 8488], [3679, 8551], [3651, 8533], [3628, 8448], [3644, 8414], [3648, 8356], [3630, 8300], [3549, 8261], [3468, 8269], [3429, 8256], [3381, 8203], [3349, 8120], [3348, 8024], [3310, 7973], [3267, 7975], [3295, 8032], [3262, 8115], [3288, 8188], [3265, 8235], [3226, 8279], [3128, 8322], [3062, 8378], [3045, 8417], [3041, 8475]]]}}, {"type": "Feature", "id": "PA.HE", "properties": {"hc-group": "admin1", "hc-middle-x": 0.43, "hc-middle-y": 0.43, "hc-key": "pa-he", "hc-a2": "HE", "labelrank": "7", "hasc": "PA.HE", "alt-name": null, "woe-id": "2346556", "subregion": null, "fips": "PM06", "postal-code": "HE", "name": "<PERSON>", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-80.6613", "woe-name": "<PERSON>", "latitude": "7.85632", "woe-label": "Herrera, PA, Panama", "type": "Provincia"}, "geometry": {"type": "Polygon", "coordinates": [[[3746, 7005], [3735, 6989], [3765, 6975], [3787, 6903], [3936, 6831], [3903, 6756], [3863, 6714], [3781, 6711], [3722, 6694], [3698, 6672], [3642, 6570], [3569, 6506], [3567, 6434], [3605, 6357], [3608, 6322], [3581, 6264], [3542, 6230], [3495, 6213], [3401, 6065], [3299, 5966], [3251, 6015], [3202, 6030], [3147, 6062], [3005, 6274], [2972, 6381], [2931, 6415], [2888, 6404], [2881, 6446], [2888, 6655], [2907, 6681], [2954, 6693], [3046, 6781], [3080, 6838], [3083, 6867], [3054, 6948], [3188, 7000], [3260, 7077], [3361, 7082], [3406, 7066], [3504, 7091], [3564, 7082], [3598, 7045], [3626, 7034], [3657, 7052], [3746, 7005]]]}}, {"type": "Feature", "id": "PA.LS", "properties": {"hc-group": "admin1", "hc-middle-x": 0.49, "hc-middle-y": 0.49, "hc-key": "pa-ls", "hc-a2": "LS", "labelrank": "7", "hasc": "PA.LS", "alt-name": null, "woe-id": "2346557", "subregion": null, "fips": "PM07", "postal-code": "LS", "name": "Los Santos", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-80.39270000000001", "woe-name": "Los Santos", "latitude": "7.58403", "woe-label": "Los Santos, PA, Panama", "type": "Provincia"}, "geometry": {"type": "Polygon", "coordinates": [[[3299, 5966], [3401, 6065], [3495, 6213], [3542, 6230], [3581, 6264], [3608, 6322], [3605, 6357], [3567, 6434], [3569, 6506], [3642, 6570], [3698, 6672], [3722, 6694], [3781, 6711], [3863, 6714], [3903, 6756], [3936, 6831], [3985, 6816], [4024, 6749], [4075, 6620], [4251, 6480], [4363, 6378], [4532, 6164], [4618, 6007], [4640, 5953], [4638, 5917], [4595, 5831], [4508, 5820], [4378, 5771], [4262, 5771], [4239, 5758], [4167, 5780], [4150, 5796], [4061, 5771], [4061, 5759], [4137, 5783], [4150, 5771], [4017, 5728], [3968, 5692], [3960, 5644], [3992, 5591], [3986, 5574], [3927, 5564], [3891, 5516], [3844, 5492], [3835, 5453], [3806, 5441], [3751, 5454], [3512, 5424], [3507, 5489], [3476, 5536], [3394, 5584], [3396, 5639], [3384, 5682], [3337, 5746], [3307, 5836], [3295, 5915], [3299, 5966]]]}}, {"type": "Feature", "id": "PA.DR", "properties": {"hc-group": "admin1", "hc-middle-x": 0.64, "hc-middle-y": 0.64, "hc-key": "pa-dr", "hc-a2": "DR", "labelrank": "7", "hasc": "PA.DR", "alt-name": null, "woe-id": "2346555", "subregion": null, "fips": "PM05", "postal-code": "DR", "name": "<PERSON><PERSON><PERSON>", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-77.7067", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "7.90194", "woe-label": "Darién, PA, Panama", "type": "Provincia"}, "geometry": {"type": "Polygon", "coordinates": [[[9663, 7163], [9734, 7036], [9791, 6821], [9847, 6771], [9851, 6734], [9783, 6735], [9665, 6702], [9609, 6672], [9570, 6632], [9524, 6526], [9470, 6457], [9464, 6434], [9489, 6380], [9527, 6345], [9539, 6311], [9485, 6261], [9227, 6099], [9144, 6014], [9097, 5977], [9056, 5972], [9036, 5993], [9013, 6086], [8947, 6179], [8921, 6198], [8929, 6219], [8819, 6317], [8799, 6327], [8755, 6303], [8744, 6235], [8753, 6156], [8770, 6099], [8812, 6018], [8818, 5979], [8802, 5931], [8739, 5875], [8699, 5869], [8654, 5878], [8517, 5431], [8475, 5443], [8421, 5490], [8341, 5589], [8297, 5626], [8291, 5646], [8305, 5704], [8230, 5782], [8202, 5767], [8157, 5818], [8106, 5855], [8086, 5899], [8056, 5915], [8034, 5970], [8015, 5942], [8000, 6058], [7948, 6042], [7958, 6096], [7944, 6126], [7882, 6195], [7819, 6220], [7815, 6256], [7781, 6272], [7808, 6333], [7793, 6388], [7737, 6472], [7711, 6552], [7590, 6689], [7551, 6805], [7551, 6867], [7517, 6926], [7522, 6984], [7565, 7031], [7587, 6971], [7613, 6960], [7665, 6982], [7717, 6955], [7788, 6992], [7851, 7060], [7878, 7128], [7864, 7189], [7827, 7223], [7751, 7312], [7801, 7307], [7840, 7324], [7814, 7363], [7840, 7388], [7906, 7346], [7947, 7351], [7977, 7395], [7977, 7465], [8016, 7503], [8027, 7573], [8077, 7586], [8091, 7503], [8110, 7488], [8220, 7441], [8269, 7325], [8310, 7284], [8386, 7286], [8430, 7242], [8471, 7251], [8417, 7272], [8384, 7315], [8476, 7298], [8525, 7280], [8557, 7245], [8611, 7208], [8619, 7161], [8649, 7139], [8669, 7150], [8706, 7114], [8725, 7061], [8784, 7063], [8801, 7089], [8749, 7088], [8681, 7170], [8649, 7164], [8554, 7283], [8508, 7311], [8344, 7340], [8304, 7356], [8250, 7418], [8242, 7460], [8202, 7540], [8190, 7590], [8218, 7617], [8175, 7648], [8140, 7694], [8131, 7724], [8150, 7793], [8102, 7820], [8085, 7873], [8072, 7866], [8083, 7802], [8117, 7784], [8120, 7701], [8148, 7622], [8129, 7604], [8093, 7663], [8053, 7706], [8024, 7706], [7983, 7661], [7965, 7594], [7938, 7572], [7870, 7553], [7848, 7573], [7861, 7616], [7915, 7642], [7940, 7629], [7942, 7689], [7932, 7746], [7901, 7846], [7875, 7819], [7914, 7769], [7888, 7688], [7902, 7654], [7876, 7642], [7839, 7679], [7870, 7741], [7847, 7745], [7800, 7667], [7770, 7663], [7737, 7743], [7696, 7668], [7637, 7627], [7620, 7658], [7600, 7768], [7560, 7856], [7553, 7810], [7587, 7718], [7606, 7630], [7640, 7602], [7648, 7571], [7598, 7463], [7559, 7495], [7552, 7577], [7522, 7684], [7516, 7725], [7492, 7782], [7487, 7852], [7468, 7878], [7486, 8023], [7516, 8113], [7521, 8163], [7479, 8358], [7502, 8377], [7586, 8406], [7687, 8383], [7741, 8335], [7775, 8352], [7810, 8420], [7901, 8453], [7967, 8452], [8002, 8468], [8056, 8535], [8134, 8561], [8163, 8591], [8161, 8697], [8181, 8759], [8229, 8830], [8279, 8831], [8470, 8691], [8572, 8588], [8615, 8513], [8766, 8420], [8860, 8329], [8876, 8277], [8820, 8285], [8775, 8279], [8735, 8258], [8697, 8217], [8638, 8203], [8537, 8054], [8501, 8033], [8480, 7996], [8499, 7926], [8548, 7875], [8541, 7819], [8569, 7782], [8607, 7769], [8607, 7738], [8654, 7690], [8665, 7652], [8650, 7589], [8682, 7541], [8695, 7492], [8735, 7448], [8763, 7395], [8803, 7380], [8809, 7326], [8935, 7275], [9006, 7218], [9040, 7140], [9033, 7110], [9051, 7081], [9089, 7070], [9142, 7007], [9167, 7001], [9202, 7022], [9264, 7022], [9326, 7038], [9376, 7087], [9453, 7105], [9663, 7163]], [[8112, 6819], [8099, 6874], [8008, 6995], [7978, 7016], [7901, 6971], [7897, 6936], [7924, 6922], [7945, 6845], [7901, 6814], [7843, 6835], [7772, 6779], [7710, 6768], [7708, 6644], [7726, 6598], [7758, 6583], [7794, 6508], [7822, 6421], [7868, 6345], [7957, 6247], [7989, 6224], [8065, 6247], [8171, 6187], [8187, 6107], [8232, 6086], [8251, 6118], [8286, 6138], [8338, 6087], [8414, 6140], [8423, 6201], [8401, 6363], [8415, 6405], [8390, 6465], [8354, 6482], [8346, 6541], [8288, 6718], [8253, 6754], [8165, 6692], [8148, 6794], [8112, 6819]]]}}, {"type": "Feature", "id": "PA.1119", "properties": {"hc-group": "admin1", "hc-middle-x": 0.76, "hc-middle-y": 0.58, "hc-key": "pa-1119", "hc-a2": "KY", "labelrank": "7", "hasc": "PA.SB", "alt-name": null, "woe-id": "2346559", "subregion": null, "fips": "PM09", "postal-code": null, "name": "<PERSON><PERSON>", "country": "Panama", "type-en": "Indigenous Territory", "region": null, "longitude": "-78.32729999999999", "woe-name": "<PERSON><PERSON>", "latitude": "9.282690000000001", "woe-label": "<PERSON><PERSON>, PA, Panama", "type": "Comarca Indígena"}, "geometry": {"type": "Polygon", "coordinates": [[[8876, 8277], [8860, 8329], [8766, 8420], [8615, 8513], [8572, 8588], [8470, 8691], [8279, 8831], [8229, 8830], [8187, 8894], [8154, 8914], [8026, 8944], [8011, 8963], [8038, 9014], [8029, 9053], [8001, 9074], [7986, 9050], [7833, 9046], [7808, 9072], [7782, 9153], [7762, 9169], [7682, 9132], [7554, 9143], [7524, 9224], [7450, 9286], [7411, 9263], [7385, 9317], [7362, 9338], [7333, 9335], [7308, 9304], [7212, 9281], [7151, 9285], [7020, 9376], [6990, 9377], [6966, 9327], [6910, 9372], [6875, 9375], [6842, 9353], [6703, 9354], [6667, 9326], [6582, 9326], [6578, 9295], [6517, 9295], [6456, 9255], [6470, 9222], [6446, 9202], [6335, 9201], [6295, 9238], [6230, 9260], [6153, 9257], [6027, 9217], [5967, 9204], [5954, 9249], [5977, 9282], [6012, 9299], [6027, 9280], [6112, 9340], [6178, 9366], [6205, 9387], [6223, 9456], [6278, 9513], [6328, 9534], [6329, 9728], [6519, 9749], [6544, 9714], [6512, 9696], [6365, 9672], [6345, 9639], [6359, 9535], [6440, 9523], [6508, 9534], [6553, 9530], [6605, 9511], [6644, 9471], [6737, 9473], [6747, 9491], [6801, 9487], [6853, 9497], [6868, 9515], [6921, 9512], [6965, 9532], [6984, 9507], [7099, 9479], [7140, 9484], [7194, 9461], [7272, 9501], [7361, 9461], [7422, 9461], [7452, 9440], [7500, 9440], [7540, 9403], [7609, 9369], [7683, 9351], [7748, 9310], [7836, 9280], [7923, 9231], [7980, 9231], [8044, 9208], [8096, 9164], [8203, 9145], [8261, 9114], [8268, 9090], [8307, 9048], [8364, 9030], [8425, 8960], [8509, 8839], [8573, 8797], [8534, 8888], [8532, 8917], [8564, 8901], [8603, 8838], [8653, 8805], [8660, 8763], [8730, 8705], [8779, 8630], [8761, 8597], [8797, 8556], [8842, 8479], [8867, 8490], [8869, 8464], [8905, 8443], [8984, 8361], [9020, 8345], [8987, 8384], [8992, 8411], [9039, 8358], [9094, 8316], [9087, 8283], [9121, 8255], [9132, 8287], [9166, 8252], [9149, 8172], [9230, 8112], [9274, 8090], [9335, 8080], [9392, 8085], [9426, 8107], [9465, 8082], [9443, 8043], [9384, 8033], [9354, 8014], [9363, 7948], [9341, 7901], [9290, 7846], [9240, 7905], [9147, 7942], [9124, 7972], [9105, 8034], [9029, 8115], [8999, 8156], [8902, 8234], [8876, 8277]]]}}, {"type": "Feature", "id": "PA.CC", "properties": {"hc-group": "admin1", "hc-middle-x": 0.48, "hc-middle-y": 0.53, "hc-key": "pa-cc", "hc-a2": "CC", "labelrank": "7", "hasc": "PA.CC", "alt-name": null, "woe-id": "2346553", "subregion": null, "fips": "PM03", "postal-code": "CC", "name": "Co<PERSON>lé", "country": "Panama", "type-en": "Province", "region": null, "longitude": "-80.4314", "woe-name": "Co<PERSON>lé", "latitude": "8.577080000000001", "woe-label": "Coclé, PA, Panama", "type": "Provincia"}, "geometry": {"type": "Polygon", "coordinates": [[[3746, 7005], [3657, 7052], [3626, 7034], [3598, 7045], [3564, 7082], [3504, 7091], [3406, 7066], [3361, 7082], [3275, 7139], [3267, 7171], [3316, 7257], [3343, 7350], [3350, 7409], [3305, 7482], [3234, 7670], [3191, 7746], [3154, 7797], [3114, 7832], [3136, 7852], [3194, 7861], [3245, 7922], [3267, 7975], [3310, 7973], [3348, 8024], [3349, 8120], [3381, 8203], [3429, 8256], [3468, 8269], [3549, 8261], [3630, 8300], [3648, 8356], [3644, 8414], [3628, 8448], [3651, 8533], [3679, 8551], [3847, 8488], [3871, 8473], [3974, 8449], [4029, 8480], [4175, 8619], [4220, 8646], [4246, 8683], [4256, 8737], [4275, 8774], [4300, 8726], [4315, 8642], [4326, 8623], [4375, 8592], [4383, 8542], [4342, 8480], [4325, 8417], [4327, 8373], [4375, 8233], [4421, 8025], [4438, 7931], [4435, 7856], [4452, 7810], [4459, 7707], [4478, 7660], [4553, 7572], [4479, 7524], [4268, 7415], [4214, 7393], [4126, 7381], [4075, 7393], [4012, 7369], [3962, 7381], [3917, 7412], [3912, 7381], [3878, 7360], [3761, 7244], [3747, 7211], [3753, 7033], [3746, 7005]]]}}]}