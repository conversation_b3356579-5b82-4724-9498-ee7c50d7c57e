<template>
  <div
    :class="{ hidden: hidden }"
    class="pagination-container"
  >
    <el-pagination
      :layout="layout"
      :current-page.sync="currentPage"
      :page-size="pageSize"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      v-on="$listeners"
    >
      <slot />
    </el-pagination>
  </div>
</template>

<script>
import { scrollTo } from '@/utils/scroll-to';

export default {
  name: 'Pagination',

  inheritAttrs: false,
  props: {
    layout: {
      type: String,
      default: 'prev, pager, next'
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 10
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit('update:page', val);
      }
    },
    pageSize: {
      get() {
        return this.limit;
      },
      set(val) {
        this.$emit('update:limit', val);
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val });
      if (this.autoScroll) {
        scrollTo(0, 800);
      }
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize });
      if (this.autoScroll) {
        scrollTo(0, 800);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.pagination-container {
  overflow: hidden;
  padding-left: 16px;
  padding-right: 16px;
  flex-direction: row-reverse;
  display: flex;
  align-items: center;
  width: 100%;
  height: 52px;
	background-color: #f7fbff;
	box-shadow: 0px 0px 4px 0px
		rgba(190, 211, 235, 0.6);
	border-radius: 4px;
  color: #79808d;
  &::v-deep .el-pagination {
    width: 100%;
    color: #757d88;
    .el-pagination__rightwrapper {
      display: flex;
    }
    .btn-prev,
    .btn-next {
      width: 28px;
      height: 28px;
      min-width:28px;
      padding: 0 !important;
      background-color: rgba($color: #fff, $alpha: 0.48);
      box-shadow: 0px -1px 6px 0px rgba(9, 24, 41, 0.16);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .btn-next{
      margin-right: 8px;
    }
    li {
      background: none;
      // width: 28px;
      height: 28px;
      border-radius: 8px;
      background-color: #fff;
      color: #79808d;
      min-width:28px;
      margin: 0 5px;
      padding: 0 3px;

    }
    .active{
      color: #fff;
      background-color: #3569e7;
    }
  }
  &.hidden {
    display: none;
  }
}
</style>
