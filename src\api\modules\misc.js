/*
 * @Author: your name
 * @Date: 2021-08-19 15:51:56
 * @LastEditTime: 2021-09-17 15:20:28
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \yq-admin-front\src\api\modules\misc.js
 */
import http from '@/api/request';
import URL from '../urls';

/**
 * 杂项接口
 */
export class MiscAPI {
  /**
   * 请求批量下载
   */
  static requestBatchDownload(data) {
    return http.post(URL.batchDownload.request, data);
  }

  /**
   * 下载签名
   */
  static getSignKey(data) {
    return http.get(URL.downloadKey.getSignKey, data);
  }

  /**
   * 刷新签名
   */
  static refreshKey(data) {
    return http.post(URL.downloadKey.refreshKey, data);
  }

  /**
   * 获取桶名和地区
  */
  static getBucketAndRegion() {
    return http.get(URL.downloadKey.getBucketAndRegion);
  }

  /**
   * 单个下载
   */
  static requestSingleDownload(data) {
    return http.post(URL.batchDownload.singleRequest, data);
  }
}
