{"title": "Belarus", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:32635"}}, "hc-transform": {"default": {"crs": "+proj=utm +zone=35 +datum=WGS84 +units=m +no_defs", "scale": 0.00109030149723, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 238519.314618, "yoffset": 6224087.58404}}, "features": [{"type": "Feature", "id": "BY.HM", "properties": {"hc-group": "admin1", "hc-middle-x": 0.5, "hc-middle-y": 0.51, "hc-key": "by-hm", "hc-a2": "HM", "labelrank": "7", "hasc": "BY.HM", "alt-name": "?????|??????? ???????|Minsk Oblast|Minskaya Voblasts'", "woe-id": "20069996", "subregion": null, "fips": "BO06", "postal-code": "HM", "name": "City of Minsk", "country": "Belarus", "type-en": "Region", "region": null, "longitude": "27.6424", "woe-name": "Minsk", "latitude": "53.9005", "woe-label": "Minsk, BY, Belarus", "type": "Voblasts'"}, "geometry": {"type": "Polygon", "coordinates": [[[4345, 5823], [4349, 5757], [4286, 5662], [4296, 5554], [4344, 5479], [4314, 5432], [4186, 5435], [4069, 5420], [4039, 5382], [3994, 5429], [3846, 5480], [3832, 5588], [3847, 5720], [3872, 5781], [3977, 5819], [4091, 5801], [4183, 5769], [4254, 5784], [4315, 5832], [4345, 5823]]]}}, {"type": "Feature", "id": "BY.BR", "properties": {"hc-group": "admin1", "hc-middle-x": 0.43, "hc-middle-y": 0.56, "hc-key": "by-br", "hc-a2": "BR", "labelrank": "7", "hasc": "BY.BR", "alt-name": "?????|Brestskaya Voblasts'|<PERSON><PERSON>ès<PERSON>", "woe-id": "2344830", "subregion": null, "fips": "BO01", "postal-code": "BR", "name": "Brest", "country": "Belarus", "type-en": "Region", "region": null, "longitude": "25.3826", "woe-name": "Brest", "latitude": "52.4289", "woe-label": "Brestskaya Voblasts', BY, Belarus", "type": "Voblasts'"}, "geometry": {"type": "Polygon", "coordinates": [[[4019, 1319], [3978, 1320], [3875, 1258], [3717, 1266], [3744, 1371], [3658, 1378], [3631, 1431], [3627, 1550], [3548, 1579], [3445, 1583], [3328, 1542], [3251, 1555], [3030, 1653], [2774, 1663], [2731, 1748], [2460, 1762], [2351, 1846], [2235, 1852], [1988, 1903], [1890, 1885], [1505, 1900], [1310, 1958], [1204, 1941], [1098, 1889], [770, 1846], [675, 1867], [385, 1854], [334, 1821], [270, 1723], [238, 1602], [203, 1557], [67, 1471], [-114, 1320], [-161, 1315], [-224, 1389], [-380, 1442], [-523, 1419], [-565, 1376], [-534, 1254], [-538, 1207], [-596, 1255], [-629, 1383], [-633, 1481], [-599, 1504], [-612, 1583], [-592, 1659], [-522, 1716], [-507, 1759], [-544, 1824], [-471, 2067], [-436, 2103], [-473, 2275], [-537, 2347], [-614, 2356], [-680, 2475], [-752, 2476], [-742, 2504], [-951, 2575], [-999, 2687], [-940, 2793], [-717, 3087], [-612, 3166], [-508, 3220], [-314, 3266], [-158, 3363], [-110, 3417], [-91, 3553], [81, 3538], [167, 3591], [203, 3558], [360, 3522], [443, 3536], [532, 3469], [628, 3479], [618, 3569], [674, 3658], [657, 3699], [751, 3776], [710, 3836], [793, 3872], [923, 3817], [980, 3823], [1010, 3874], [1069, 3865], [1097, 3758], [1151, 3729], [1221, 3758], [1204, 3637], [1238, 3621], [1301, 3650], [1362, 3645], [1439, 3676], [1511, 3628], [1485, 3779], [1520, 3810], [1609, 3817], [1676, 3924], [1748, 3969], [1836, 4160], [1782, 4268], [1841, 4319], [1806, 4381], [1824, 4431], [1883, 4459], [1898, 4544], [1982, 4660], [2153, 4646], [2181, 4622], [2340, 4607], [2388, 4614], [2544, 4592], [2602, 4569], [2719, 4594], [2713, 4509], [2736, 4469], [2732, 4383], [2668, 4375], [2595, 4279], [2600, 4243], [2662, 4229], [2721, 4130], [2813, 4119], [2872, 4005], [2769, 3990], [2708, 3892], [2786, 3700], [2746, 3654], [2782, 3586], [2886, 3625], [2897, 3678], [2940, 3693], [3012, 3656], [3024, 3604], [3108, 3600], [3192, 3479], [3370, 3416], [3407, 3451], [3444, 3426], [3444, 3367], [3481, 3306], [3599, 3285], [3636, 3239], [3621, 3170], [3486, 3164], [3465, 3107], [3494, 3074], [3467, 3037], [3584, 2914], [3600, 2868], [3567, 2808], [3732, 2704], [3749, 2645], [3846, 2618], [3877, 2557], [4024, 2473], [4044, 2444], [4022, 2373], [4030, 2164], [4115, 2063], [4099, 2010], [4030, 1920], [4038, 1841], [4006, 1735], [4068, 1663], [4067, 1559], [4015, 1474], [4019, 1319]]]}}, {"type": "Feature", "id": "BY.HO", "properties": {"hc-group": "admin1", "hc-middle-x": 0.57, "hc-middle-y": 0.49, "hc-key": "by-ho", "hc-a2": "HO", "labelrank": "7", "hasc": "BY.HO", "alt-name": "??????|Gomel|Gomel'|Homyel'skaya Voblasts'|Homje", "woe-id": "2344831", "subregion": null, "fips": "BO02", "postal-code": "HO", "name": "<PERSON><PERSON>", "country": "Belarus", "type-en": "Region", "region": null, "longitude": "29.5189", "woe-name": "<PERSON><PERSON><PERSON>'", "latitude": "52.1969", "woe-label": "<PERSON><PERSON><PERSON>'skaya Voblasts', BY, Belarus", "type": "Voblasts'"}, "geometry": {"type": "Polygon", "coordinates": [[[8352, 4281], [8347, 4210], [8309, 4196], [8234, 4075], [8324, 4010], [8408, 3847], [8483, 3794], [8546, 3782], [8617, 3670], [8635, 3555], [8537, 3468], [8662, 3278], [8723, 3226], [8644, 3207], [8641, 3134], [8684, 3100], [8688, 2950], [8720, 2895], [8681, 2777], [8760, 2694], [8840, 2674], [8821, 2606], [8847, 2550], [8925, 2488], [8933, 2396], [8802, 2380], [8597, 2407], [8491, 2400], [8402, 2357], [8348, 2254], [8292, 2249], [8208, 2306], [8005, 2291], [7960, 2259], [7947, 2147], [7899, 2065], [7837, 2027], [7848, 1988], [7779, 1949], [7685, 1793], [7698, 1740], [7639, 1682], [7673, 1671], [7640, 1572], [7589, 1564], [7580, 1419], [7533, 1380], [7568, 1342], [7560, 1286], [7620, 1268], [7597, 1228], [7667, 1115], [7635, 1053], [7677, 1026], [7708, 943], [7703, 884], [7637, 820], [7596, 689], [7505, 735], [7459, 789], [7372, 810], [7327, 877], [7334, 965], [7246, 1030], [7182, 1105], [7114, 1136], [6950, 1125], [6854, 1075], [6808, 1086], [6764, 1025], [6636, 1032], [6588, 1114], [6492, 1114], [6436, 1044], [6363, 1019], [6320, 919], [6245, 937], [6149, 876], [6122, 891], [6080, 1001], [6027, 1063], [6031, 1161], [5948, 1317], [5904, 1357], [5810, 1356], [5762, 1273], [5710, 1235], [5603, 1224], [5530, 1173], [5476, 1080], [5478, 977], [5452, 924], [5421, 1001], [5374, 984], [5335, 1039], [5299, 1207], [5163, 1239], [5101, 1227], [4999, 1148], [4955, 1240], [4835, 1383], [4784, 1347], [4725, 1238], [4674, 1203], [4516, 1219], [4419, 1308], [4371, 1283], [4343, 1111], [4277, 1025], [4199, 1076], [4246, 1218], [4212, 1268], [4146, 1269], [4019, 1319], [4015, 1474], [4067, 1559], [4068, 1663], [4006, 1735], [4038, 1841], [4030, 1920], [4099, 2010], [4115, 2063], [4030, 2164], [4022, 2373], [4044, 2444], [4024, 2473], [3877, 2557], [3846, 2618], [3749, 2645], [3732, 2704], [3818, 2841], [3804, 2915], [3859, 2936], [3874, 2986], [3937, 2970], [4032, 3040], [4064, 2983], [4109, 3025], [4110, 2932], [4231, 2852], [4359, 2848], [4487, 2861], [4533, 2896], [4515, 2972], [4553, 3009], [4651, 2989], [4722, 2913], [4959, 2943], [5031, 3009], [5028, 3230], [5098, 3321], [5226, 3322], [5320, 3340], [5501, 3400], [5516, 3444], [5465, 3481], [5653, 3557], [5660, 3683], [5789, 3706], [5884, 3834], [5969, 3851], [6138, 3802], [6150, 3855], [6214, 3809], [6308, 3874], [6429, 3877], [6475, 3855], [6524, 3928], [6407, 4004], [6436, 4060], [6398, 4153], [6430, 4207], [6528, 4201], [6507, 4245], [6542, 4318], [6512, 4355], [6567, 4387], [6518, 4527], [6571, 4632], [6696, 4579], [6775, 4620], [6964, 4520], [6999, 4435], [7084, 4431], [7228, 4488], [7218, 4543], [7261, 4582], [7346, 4599], [7469, 4572], [7520, 4535], [7579, 4531], [7609, 4573], [7659, 4553], [7783, 4551], [7818, 4607], [7913, 4602], [7964, 4479], [8049, 4418], [8061, 4336], [8039, 4300], [8113, 4298], [8186, 4244], [8352, 4281]]]}}, {"type": "Feature", "id": "BY.VI", "properties": {"hc-group": "admin1", "hc-middle-x": 0.52, "hc-middle-y": 0.48, "hc-key": "by-vi", "hc-a2": "VI", "labelrank": "7", "hasc": "BY.VI", "alt-name": "???????|Vicebsk|Vitebsk|Vitsyebskaya Voblasts'|Witebsk", "woe-id": "2344835", "subregion": null, "fips": "BO07", "postal-code": "VI", "name": "Vitebsk", "country": "Belarus", "type-en": "Region", "region": null, "longitude": "28.6747", "woe-name": "Vitsyebsk", "latitude": "55.3073", "woe-label": "Vitsyebskaya Voblasts', BY, Belarus", "type": "Voblasts'"}, "geometry": {"type": "Polygon", "coordinates": [[[2523, 7659], [2583, 7774], [2593, 7880], [2628, 7934], [2676, 7942], [2795, 7909], [2853, 7942], [2966, 7890], [3007, 7922], [3026, 8028], [3099, 8112], [3168, 8130], [3206, 8180], [3172, 8231], [2994, 8263], [2929, 8246], [2847, 8272], [2826, 8303], [2894, 8494], [2932, 8532], [2921, 8639], [2989, 8735], [3002, 8826], [2989, 8922], [3038, 8975], [3147, 8951], [3232, 8994], [3314, 9131], [3400, 9221], [3537, 9239], [3603, 9219], [3699, 9147], [3790, 9230], [3885, 9170], [4048, 9162], [4072, 9321], [4101, 9405], [4205, 9475], [4243, 9582], [4347, 9673], [4378, 9742], [4586, 9851], [4778, 9647], [4824, 9655], [4881, 9728], [5037, 9748], [5124, 9723], [5190, 9620], [5220, 9493], [5330, 9449], [5381, 9529], [5559, 9624], [5680, 9604], [5765, 9543], [5928, 9503], [5998, 9432], [5948, 9380], [5903, 9188], [5927, 9122], [6034, 9005], [6085, 9003], [6163, 9105], [6265, 9170], [6394, 9177], [6457, 9291], [6581, 9327], [6708, 9285], [6823, 9353], [6882, 9310], [7095, 9250], [7111, 9177], [7208, 9135], [7242, 9016], [7347, 8997], [7404, 8891], [7515, 8929], [7557, 8911], [7600, 8789], [7572, 8662], [7578, 8597], [7612, 8551], [7602, 8490], [7540, 8440], [7492, 8314], [7562, 8257], [7599, 8167], [7682, 8093], [7712, 8035], [7700, 7978], [7747, 7833], [7647, 7831], [7677, 7735], [7640, 7683], [7550, 7643], [7568, 7549], [7510, 7456], [7517, 7374], [7580, 7349], [7753, 7235], [7777, 7172], [7893, 7173], [7967, 7089], [7891, 6923], [7868, 6840], [7984, 6799], [7998, 6773], [7931, 6768], [7858, 6722], [7859, 6671], [7723, 6723], [7665, 6659], [7536, 6665], [7485, 6686], [7359, 6598], [7390, 6543], [7351, 6467], [7279, 6424], [7208, 6469], [7182, 6517], [7092, 6444], [6997, 6470], [7051, 6574], [6990, 6541], [6919, 6538], [6897, 6496], [6814, 6564], [6743, 6467], [6758, 6372], [6608, 6405], [6576, 6481], [6533, 6493], [6526, 6441], [6340, 6406], [6229, 6422], [6209, 6374], [6159, 6361], [6080, 6266], [6030, 6256], [6024, 6353], [6067, 6422], [6020, 6477], [6065, 6589], [6074, 6747], [6107, 6807], [6086, 6861], [6137, 6915], [6098, 6993], [6070, 6927], [5991, 6943], [5942, 6908], [5852, 6921], [5825, 7028], [5765, 7006], [5749, 7044], [5681, 6982], [5618, 6964], [5476, 6986], [5460, 6862], [5351, 6906], [5304, 6880], [5238, 6902], [5236, 6993], [5210, 7042], [5159, 7000], [5139, 7046], [5041, 7018], [4977, 6844], [4893, 6863], [4758, 7019], [4674, 7009], [4605, 6972], [4548, 6986], [4412, 6946], [4394, 7001], [4339, 6974], [4245, 6991], [4157, 7117], [4173, 7172], [4098, 7229], [3993, 7245], [3984, 7314], [3869, 7425], [3763, 7436], [3751, 7489], [3692, 7569], [3724, 7624], [3682, 7662], [3623, 7647], [3535, 7670], [3500, 7643], [3484, 7574], [3359, 7573], [3309, 7620], [3198, 7601], [3116, 7567], [3092, 7602], [3003, 7607], [2972, 7578], [2739, 7631], [2714, 7563], [2638, 7536], [2606, 7614], [2523, 7659]]]}}, {"type": "Feature", "id": "BY.HR", "properties": {"hc-group": "admin1", "hc-middle-x": 0.46, "hc-middle-y": 0.64, "hc-key": "by-hr", "hc-a2": "HR", "labelrank": "7", "hasc": "BY.HR", "alt-name": "??????|Grodno|Hrodzenskaya Voblasts'", "woe-id": "2344832", "subregion": null, "fips": "BO03", "postal-code": "HR", "name": "Grodno", "country": "Belarus", "type-en": "Region", "region": null, "longitude": "25.0839", "woe-name": "Hrodna", "latitude": "53.4855", "woe-label": "Haradzyenskaya Voblasts', BY, Belarus", "type": "Voblasts'"}, "geometry": {"type": "Polygon", "coordinates": [[[2719, 4594], [2602, 4569], [2544, 4592], [2388, 4614], [2340, 4607], [2181, 4622], [2153, 4646], [1982, 4660], [1898, 4544], [1883, 4459], [1824, 4431], [1806, 4381], [1841, 4319], [1782, 4268], [1836, 4160], [1748, 3969], [1676, 3924], [1609, 3817], [1520, 3810], [1485, 3779], [1511, 3628], [1439, 3676], [1362, 3645], [1301, 3650], [1238, 3621], [1204, 3637], [1221, 3758], [1151, 3729], [1097, 3758], [1069, 3865], [1010, 3874], [980, 3823], [923, 3817], [793, 3872], [710, 3836], [751, 3776], [657, 3699], [674, 3658], [618, 3569], [628, 3479], [532, 3469], [443, 3536], [360, 3522], [203, 3558], [167, 3591], [81, 3538], [-91, 3553], [-83, 4005], [-128, 4079], [-133, 4148], [-90, 4267], [-139, 4329], [-205, 4496], [-242, 4676], [-310, 4849], [-391, 5146], [-411, 5278], [-408, 5394], [-443, 5474], [-449, 5575], [-479, 5713], [-478, 5768], [-308, 5684], [-145, 5731], [-100, 5714], [4, 5762], [71, 5726], [142, 5724], [245, 5766], [316, 5757], [374, 5645], [508, 5627], [549, 5646], [680, 5755], [847, 5813], [897, 5757], [969, 5767], [1008, 5860], [969, 5999], [1053, 6097], [1155, 6093], [1241, 6056], [1291, 6063], [1387, 6142], [1431, 6269], [1465, 6296], [1534, 6269], [1625, 6272], [1698, 6353], [1739, 6362], [1827, 6237], [1769, 6220], [1790, 6156], [1750, 6133], [1811, 6060], [1909, 6041], [1975, 6078], [2029, 6073], [2064, 6121], [2084, 6233], [2053, 6303], [2007, 6335], [1987, 6402], [1841, 6372], [1803, 6406], [1805, 6453], [1899, 6594], [1909, 6718], [1939, 6778], [2007, 6817], [2044, 6867], [2044, 6941], [2014, 7069], [2031, 7144], [2033, 7265], [2093, 7311], [2100, 7432], [2170, 7500], [2184, 7550], [2238, 7577], [2317, 7566], [2449, 7591], [2523, 7659], [2606, 7614], [2638, 7536], [2714, 7563], [2680, 7526], [2701, 7413], [2750, 7355], [2796, 7340], [2877, 7221], [2921, 7193], [2924, 7104], [2978, 6983], [3066, 6978], [3073, 6884], [3041, 6865], [3024, 6785], [2975, 6813], [2992, 6648], [2953, 6603], [2871, 6379], [2835, 6312], [2766, 6242], [2644, 6219], [2557, 6224], [2492, 6168], [2442, 6166], [2401, 6042], [2454, 6018], [2474, 6057], [2513, 6034], [2502, 5916], [2538, 5855], [2608, 5865], [2633, 5903], [2683, 5897], [2720, 5798], [2786, 5795], [2776, 5698], [2722, 5676], [2692, 5607], [2551, 5538], [2548, 5470], [2644, 5419], [2598, 5374], [2588, 5290], [2681, 5233], [2702, 5142], [2819, 5094], [2876, 4947], [2852, 4854], [2899, 4855], [2925, 4661], [2719, 4594]]]}}, {"type": "Feature", "id": "BY.MA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.64, "hc-middle-y": 0.39, "hc-key": "by-ma", "hc-a2": "MA", "labelrank": "7", "hasc": "BY.MA", "alt-name": "???????|Mahiljow|Mogilev|Mahilyowskaya Voblasts'", "woe-id": "2344833", "subregion": null, "fips": "BO04", "postal-code": "MA", "name": "<PERSON><PERSON><PERSON>", "country": "Belarus", "type-en": "Region", "region": null, "longitude": "30.4041", "woe-name": "Mahilyow", "latitude": "53.8241", "woe-label": "Mahilyowskaya Voblasts', BY, Belarus", "type": "Voblasts'"}, "geometry": {"type": "Polygon", "coordinates": [[[6080, 6266], [6159, 6361], [6209, 6374], [6229, 6422], [6340, 6406], [6526, 6441], [6533, 6493], [6576, 6481], [6608, 6405], [6758, 6372], [6743, 6467], [6814, 6564], [6897, 6496], [6919, 6538], [6990, 6541], [7051, 6574], [6997, 6470], [7092, 6444], [7182, 6517], [7208, 6469], [7279, 6424], [7351, 6467], [7390, 6543], [7359, 6598], [7485, 6686], [7536, 6665], [7665, 6659], [7723, 6723], [7859, 6671], [7858, 6722], [7931, 6768], [7998, 6773], [8031, 6766], [8083, 6635], [8125, 6582], [8150, 6442], [8183, 6363], [8393, 6215], [8479, 6193], [8575, 6147], [8667, 6124], [8697, 6055], [8743, 6074], [8788, 5955], [8762, 5746], [8700, 5576], [8845, 5553], [9075, 5630], [9169, 5584], [9311, 5556], [9409, 5475], [9510, 5468], [9537, 5387], [9450, 5329], [9472, 5231], [9523, 5167], [9642, 5108], [9671, 5064], [9753, 5074], [9766, 5014], [9814, 5030], [9838, 4989], [9832, 4851], [9851, 4793], [9698, 4761], [9690, 4712], [9561, 4705], [9592, 4659], [9521, 4498], [9463, 4471], [9375, 4359], [9316, 4356], [9308, 4308], [9172, 4264], [9087, 4292], [8972, 4269], [8911, 4299], [8844, 4298], [8807, 4403], [8768, 4445], [8626, 4468], [8540, 4434], [8404, 4435], [8364, 4399], [8352, 4281], [8186, 4244], [8113, 4298], [8039, 4300], [8061, 4336], [8049, 4418], [7964, 4479], [7913, 4602], [7818, 4607], [7783, 4551], [7659, 4553], [7609, 4573], [7579, 4531], [7520, 4535], [7469, 4572], [7346, 4599], [7261, 4582], [7218, 4543], [7228, 4488], [7084, 4431], [6999, 4435], [6964, 4520], [6775, 4620], [6696, 4579], [6571, 4632], [6518, 4527], [6567, 4387], [6512, 4355], [6542, 4318], [6507, 4245], [6528, 4201], [6430, 4207], [6398, 4153], [6436, 4060], [6407, 4004], [6524, 3928], [6475, 3855], [6429, 3877], [6308, 3874], [6214, 3809], [6150, 3855], [6138, 3802], [5969, 3851], [5884, 3834], [5789, 3706], [5660, 3683], [5653, 3557], [5465, 3481], [5516, 3444], [5501, 3400], [5320, 3340], [5226, 3322], [5098, 3321], [5031, 3382], [4893, 3478], [5024, 3616], [5070, 3734], [5115, 3807], [5072, 3858], [5110, 3946], [5158, 3952], [5156, 4090], [5127, 4173], [5138, 4209], [5027, 4181], [4979, 4291], [4928, 4291], [4904, 4249], [4799, 4285], [4777, 4323], [4680, 4362], [4650, 4458], [4724, 4481], [4791, 4418], [4951, 4423], [5040, 4468], [5026, 4547], [4965, 4571], [4957, 4608], [4996, 4672], [4993, 4801], [5055, 4886], [5119, 4885], [5173, 4916], [5157, 4952], [5222, 4991], [5210, 5045], [5243, 5082], [5302, 5051], [5404, 5035], [5451, 5051], [5517, 5034], [5506, 4986], [5542, 4921], [5610, 4915], [5598, 5019], [5709, 5037], [5738, 5075], [5726, 5147], [5790, 5203], [5888, 5173], [5973, 5224], [6022, 5289], [6091, 5276], [6131, 5336], [6101, 5373], [6182, 5451], [6045, 5494], [6087, 5551], [6035, 5624], [6038, 5742], [6010, 5809], [6023, 5857], [6098, 5943], [6079, 6017], [6103, 6042], [6098, 6143], [6136, 6183], [6080, 6266]]]}}, {"type": "Feature", "id": "BY.MI", "properties": {"hc-group": "admin1", "hc-middle-x": 0.37, "hc-middle-y": 0.56, "hc-key": "by-mi", "hc-a2": "MI", "labelrank": "7", "hasc": "BY.MI", "alt-name": "?????|??????? ???????|Minsk Oblast|Minskaya Voblasts'", "woe-id": "2344834", "subregion": null, "fips": "BO06", "postal-code": "MI", "name": "Minsk", "country": "Belarus", "type-en": "Municipality", "region": null, "longitude": "27.4703", "woe-name": "Minsk", "latitude": "53.1945", "woe-label": "Minskaya Voblasts', BY, Belarus", "type": "<PERSON><PERSON>"}, "geometry": {"type": "Polygon", "coordinates": [[[6080, 6266], [6136, 6183], [6098, 6143], [6103, 6042], [6079, 6017], [6098, 5943], [6023, 5857], [6010, 5809], [6038, 5742], [6035, 5624], [6087, 5551], [6045, 5494], [6182, 5451], [6101, 5373], [6131, 5336], [6091, 5276], [6022, 5289], [5973, 5224], [5888, 5173], [5790, 5203], [5726, 5147], [5738, 5075], [5709, 5037], [5598, 5019], [5610, 4915], [5542, 4921], [5506, 4986], [5517, 5034], [5451, 5051], [5404, 5035], [5302, 5051], [5243, 5082], [5210, 5045], [5222, 4991], [5157, 4952], [5173, 4916], [5119, 4885], [5055, 4886], [4993, 4801], [4996, 4672], [4957, 4608], [4965, 4571], [5026, 4547], [5040, 4468], [4951, 4423], [4791, 4418], [4724, 4481], [4650, 4458], [4680, 4362], [4777, 4323], [4799, 4285], [4904, 4249], [4928, 4291], [4979, 4291], [5027, 4181], [5138, 4209], [5127, 4173], [5156, 4090], [5158, 3952], [5110, 3946], [5072, 3858], [5115, 3807], [5070, 3734], [5024, 3616], [4893, 3478], [5031, 3382], [5098, 3321], [5028, 3230], [5031, 3009], [4959, 2943], [4722, 2913], [4651, 2989], [4553, 3009], [4515, 2972], [4533, 2896], [4487, 2861], [4359, 2848], [4231, 2852], [4110, 2932], [4109, 3025], [4064, 2983], [4032, 3040], [3937, 2970], [3874, 2986], [3859, 2936], [3804, 2915], [3818, 2841], [3732, 2704], [3567, 2808], [3600, 2868], [3584, 2914], [3467, 3037], [3494, 3074], [3465, 3107], [3486, 3164], [3621, 3170], [3636, 3239], [3599, 3285], [3481, 3306], [3444, 3367], [3444, 3426], [3407, 3451], [3370, 3416], [3192, 3479], [3108, 3600], [3024, 3604], [3012, 3656], [2940, 3693], [2897, 3678], [2886, 3625], [2782, 3586], [2746, 3654], [2786, 3700], [2708, 3892], [2769, 3990], [2872, 4005], [2813, 4119], [2721, 4130], [2662, 4229], [2600, 4243], [2595, 4279], [2668, 4375], [2732, 4383], [2736, 4469], [2713, 4509], [2719, 4594], [2925, 4661], [2899, 4855], [2852, 4854], [2876, 4947], [2819, 5094], [2702, 5142], [2681, 5233], [2588, 5290], [2598, 5374], [2644, 5419], [2548, 5470], [2551, 5538], [2692, 5607], [2722, 5676], [2776, 5698], [2786, 5795], [2720, 5798], [2683, 5897], [2633, 5903], [2608, 5865], [2538, 5855], [2502, 5916], [2513, 6034], [2474, 6057], [2454, 6018], [2401, 6042], [2442, 6166], [2492, 6168], [2557, 6224], [2644, 6219], [2766, 6242], [2835, 6312], [2871, 6379], [2953, 6603], [2992, 6648], [2975, 6813], [3024, 6785], [3041, 6865], [3073, 6884], [3066, 6978], [2978, 6983], [2924, 7104], [2921, 7193], [2877, 7221], [2796, 7340], [2750, 7355], [2701, 7413], [2680, 7526], [2714, 7563], [2739, 7631], [2972, 7578], [3003, 7607], [3092, 7602], [3116, 7567], [3198, 7601], [3309, 7620], [3359, 7573], [3484, 7574], [3500, 7643], [3535, 7670], [3623, 7647], [3682, 7662], [3724, 7624], [3692, 7569], [3751, 7489], [3763, 7436], [3869, 7425], [3984, 7314], [3993, 7245], [4098, 7229], [4173, 7172], [4157, 7117], [4245, 6991], [4339, 6974], [4394, 7001], [4412, 6946], [4548, 6986], [4605, 6972], [4674, 7009], [4758, 7019], [4893, 6863], [4977, 6844], [5041, 7018], [5139, 7046], [5159, 7000], [5210, 7042], [5236, 6993], [5238, 6902], [5304, 6880], [5351, 6906], [5460, 6862], [5476, 6986], [5618, 6964], [5681, 6982], [5749, 7044], [5765, 7006], [5825, 7028], [5852, 6921], [5942, 6908], [5991, 6943], [6070, 6927], [6098, 6993], [6137, 6915], [6086, 6861], [6107, 6807], [6074, 6747], [6065, 6589], [6020, 6477], [6067, 6422], [6024, 6353], [6030, 6256], [6080, 6266]], [[4345, 5823], [4315, 5832], [4254, 5784], [4183, 5769], [4091, 5801], [3977, 5819], [3872, 5781], [3847, 5720], [3832, 5588], [3846, 5480], [3994, 5429], [4039, 5382], [4069, 5420], [4186, 5435], [4314, 5432], [4344, 5479], [4296, 5554], [4286, 5662], [4349, 5757], [4345, 5823]]]}}]}