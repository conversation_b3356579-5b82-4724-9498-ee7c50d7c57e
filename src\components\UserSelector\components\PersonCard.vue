<template>
  <div
    class="person-card"
    @click="handleClickPerson"
  >
    <div class="person-card__head">
      <div
        class="status"
        :style="calcStatusStyle"
      >
        {{ person.status | formatStatusLabel }}
      </div>
      <div class="my-tag-warp">
        <sys-tag-span
          :tags="person.tag"
          :is-el-tag="false"
        />
        <el-tooltip
          v-if="person.m_simple_tag && person.m_simple_tag.length > 0"
          :content="$t('accountCenter.备注：') + person.m_simple_tag_tooltip"
          effect="light"
          placement="top"
        >
          <cross-tag-span
            :list="person.m_simple_tag"
          />
        </el-tooltip>
      </div>

      <div
        v-if="person.redistribution&&person.redistribution.length > 0"
        class="status-icon"
      >
        <titan-tooltip :content="$t('components. 已分配至 :') + person.redistribution.join(',')">
          <svg-icon
            class="icon"
            icon-class="distri"
          />
        </titan-tooltip>
      </div>
      <div class="status-icon">
        <titan-tooltip
          v-if="person.status === 11"
          :content="$t('components. 已导出')"
        >
          <svg-icon
            class="icon"
            icon-class="export"
          />
        </titan-tooltip>
        <titan-tooltip
          v-if="person.exported"
          :content="$t('assets.已出库')"
        >
          <svg-icon
            class="icon"
            icon-class="exWarehouse"
          />
        </titan-tooltip>
        <titan-tooltip
          v-if="person.copy_id"
          :content="$t('components.克隆')"
        >
          <i class="icon-copy" />
        </titan-tooltip>
      </div>
      <div
        v-if="person.locked"
        class="lock-box"
      >
        <titan-tooltip :content="$t('components. 已锁定')">
          <svg-icon
            class="fa"
            icon-class="lock"
          />
        </titan-tooltip>
      </div>

      <div
        v-if="!isHaveFollowedCorps && person.status===0 && isCorpsManage && !isDelAccount"
        class="add-corps-box"
        @click.stop="handleClickAddCorp"
      >
        <titan-tooltip :content="$t('components. 未锁定')">
          <svg-icon icon-class="addCorps" />
        </titan-tooltip>
      </div>
    </div>
    <div class="person-card__main">
      <titan-avatar
        class="person-card__avatar"
        size="50px"
        :src="person.account_photo"
        :label="person.account"
        :config="{
          labelStyle: {
            fontSize: '22px'
          }
        }"
      />
      <div class="person-card__info">
        <div class="info-name">
          <div class="name">
            {{ person.account | formatEmptyPlacement }}
          </div>
          <svg-icon
            class="icon"
            :icon-class="person.platform"
          />
        </div>
        <div class="info-item">
          <div class="tel">
            {{ $t('components. 手机号：') }}{{ person | formatPhone | formatEmptyPlacement }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-item">
            {{ $t('components. 关注数: ') }}{{ team | formatEmptyPlacement }}
          </div>
          <div>
            {{ $t('components. 粉丝数: ') }}{{ person.breed_type | formatEmptyPlacement }}
            <titan-tooltip
              v-if="person.breed_type"
              :content="tip"
            >
              <i class="question-icon el-icon-question" />
            </titan-tooltip>
          </div>
        </div>
      </div>
    </div>
    <el-divider />
    <div
      class="person-card__footer"
      @click="clickBottom"
    >
      <div class="person-card__social">
        <titan-tooltip v-if="platformsCodeMap[person.platform] && platformsCodeMap[person.platform].following_count">
          <div slot="content">
            {{ $t('components. 关注数: ') }} {{ Number(person.following_count || 0).toLocaleString() }}
          </div>
          <span>
            <svg-icon
              class="icon"
              icon-class="following"
            />
            {{ person.following_count | formatNumber2Decimals }}
          </span>
        </titan-tooltip>
        <titan-tooltip v-if="platformsCodeMap[person.platform] && platformsCodeMap[person.platform].fans_count">
          <div slot="content">
            {{ $t('components. 粉丝数: ') }}{{ Number(person.fans_count || 0).toLocaleString() }}
          </div>
          <span>
            <svg-icon
              class="icon"
              icon-class="fans"
            />
            {{
              person.fans_count | formatNumber2Decimals
            }}
          </span>
        </titan-tooltip>
        <titan-tooltip v-if="platformsCodeMap[person.platform] && platformsCodeMap[person.platform].friend_count">
          <div slot="content">
            {{ $t('components. 好友数:') }} {{ Number(person.friends_count || 0).toLocaleString() }}
          </div>
          <span>
            <svg-icon
              class="icon"
              icon-class="friend"
            />
            {{ person.friends_count | formatNumber2Decimals }}
          </span>
        </titan-tooltip>
        <titan-tooltip v-if="platformsCodeMap[person.platform] && platformsCodeMap[person.platform].article_count">
          <div slot="content">
            {{ $t('components. 文章数: ') }}{{ Number(person.article_count || 0).toLocaleString() }}
          </div>
          <span>
            <svg-icon
              class="icon"
              icon-class="posting"
            />
            {{
              person.article_count | formatNumber2Decimals
            }}
          </span>
        </titan-tooltip>
        <titan-tooltip v-if="platformsCodeMap[person.platform] && platformsCodeMap[person.platform].score">
          <div slot="content">
            {{ person.platform==='reddit'?$t('components. karma值'):$t('components. 积分') }} :  {{ Number(person.score || 0).toLocaleString() }}
          </div>
          <span>
            <svg-icon
              class="icon"
              icon-class="karma_value"
            />
            {{ person.score | formatNumber2Decimals }}
          </span>
        </titan-tooltip>
        <titan-tooltip>
          <div slot="content">
            {{ $t('components. 活跃度:') }}{{ person.activity_level | formatActiveLabel }}
          </div>
          <span>
            <svg-icon
              class="icon"
              icon-class="active"
            />
            {{ person.activity_level | formatActiveLabel }}
          </span>
        </titan-tooltip>
      </div>
      <!-- v-permission="$qx['virtual_account_list_manage']" -->
      <el-checkbox
        v-if="batchBool"
        ref="checkBox"
        :value="selected"
        class="mx-2"
        @click.native.stop
        @change="handleCardChange"
      />
    </div>
  </div>
</template>

<script>
import { ActivityLevelOptions, AccountStatusOptions } from '@/assets/libs/enum';
import { mapGetters } from 'vuex';
import SysTagSpan from '@/components/SysTagSelect/sysTagSpan';
import CrossTagSpan from '@/components/SysTagSelect/crossTagSpan.vue';

export default {
  name: 'PersonCard',
  components: { CrossTagSpan, SysTagSpan },
  filters: {
    formatActiveLabel(val) {
      const active = ActivityLevelOptions.find(item => item.value === val);
      return active?.label || this.$t('components[" 一级"]');
    },
    formatStatusLabel(val) {
      const status = AccountStatusOptions.find(item => item.value === val);
      return status?.label ? status.label : '--';
    },
    formatPhone(val) {
      const prefix = val.phone_country_code || '';
      return prefix ? prefix + '-' + val.phone : val.phone;
    }
  },
  props: {
    isDelAccount: {
      type: Boolean,
      default: false
    },
    person: {
      type: Object,
      default: () => ({})
    },
    // 当前卡片是否选中
    selected: {
      type: Boolean,
      default: false
    },
    batchBool: {
      type: Boolean,
      default: false
    },
    platformsCodeMap: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    ...mapGetters('codeDict', ['accountBreedTypeList']),
    ...mapGetters(['canImport', 'isManageByPermission']),
    isCorpsManage() {
      return this.isManageByPermission(this.$qx['virtual_group_manage']);
    },
    calcStatusStyle() {
      const status = AccountStatusOptions.find(
        it => it.value === this.person.status
      );
      if (status) {
        return {
          backgroundColor: status.color,
          border: 'none'
        };
      }
      return {};
    },
    tip() {
      return (
        this.accountBreedTypeList.find(
          o => o.value === this.person.breed_type
        ) || {}
      ).tip;
    },
    team() {
      const followed_corps = this.person?.followed_corps;
      if (_.isArray(followed_corps) && followed_corps.length > 0) {
        return followed_corps.filter(it => it !== 'undefined').join('、');
      }
      return '--';
    },
    isHaveFollowedCorps() {
      const followed_corps = this.person?.followed_corps || [];
      return followed_corps.length > 0;
    }
  },
  methods: {
    // 点击卡片本身
    handleClickPerson() {
      this.handleCardChange(!this.selected);
    },

    // 选择checkbox
    handleCardChange(value) {
      this.$emit('change', value, {
        id: this.person.id,
        account: this.person.account,
        status: this.person.status,
        platform: this.person.platform,
        account_photo: this.person.account_photo,
        account_id: this.person.account_id
      });
    },

    // 点击底部
    clickBottom(event) {
      if (!this.batchBool) return;
      event.stopPropagation();
      this.handleCardChange(!this.$refs.checkBox.isChecked);
    },
    handleClickAddCorp() {
      this.$emit('addCorp', this.person);
    }
  }
};
</script>

<style lang="scss" scoped>

.person-card {
  position: relative;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 4px;
  cursor: pointer;
  border: solid 1px #dcdfe6;

  &:hover {
    border: 1px solid #3569e7;
    box-shadow: 0 0 9px 0px rgba(161, 161, 161, 0.25);
.add-corps-box{
  display: flex;
}
  }
  &__head {
    display: flex;
    font-size: 12px;
    margin-bottom: 8px;
    .status {
      width: 56px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      color: #ffffff;
      border-radius: 12px;
    }
    .distri {
      background-color: #DCF3FF;
      border-radius: 12px;
      color: #27B0F3;
      width: 56px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      margin-left: 8px;
    }
    .my-tag-warp{
      display: flex;
      width: calc(100% - 60px);
      ::v-deep .tag-txt{
        background-color: #e0f0fc;
        border-radius: 12px;
        border: solid 1px #96afec;
        color: #3569e7;
        width: 100px;
        padding: 0 5px;
        height: 24px;
        line-height: 22px;
        margin-left: 8px;
        @include utils-ellipsis(1);
      }
      ::v-deep .cross-tag-warp{
        background-color: #fff4ed;
        border-radius: 12px;
        border: solid 1px #ffd4ae;
        color: #EFBE62;
        width: 100px;
        padding: 0 5px;
        height: 24px;
        line-height: 22px;
        margin-left: 8px;
        @include utils-ellipsis(1);
        .el-tag{
          background: none;
          border: none;
          max-width: 100%;
          display: inline;
          margin: 0;
          padding: 0;
          @include utils-ellipsis(1);
          & + .el-tag{
            &::before{
              content: '、'
            }
          }
        }
      }
    }
    .status-icon {
      font-size: 24px;
      line-height: 24px;
      margin-left: 8px;
      svg + svg {
        margin-left: 8px;
      }
      .icon-copy{
        width: 24px;
        height: 24px;
        background: url("~@/assets/images/icon-clone.svg") no-repeat center #ffeddd;
        background-size: 50% 50%;
        display: inline-block;
        border-radius: 24px;
      }
    }
    .lock-box {
      font-size: 22px;
      position: absolute;
      right: 18px;
      top: 10px;
      color: #dcdfe6;
    }
    .add-corps-box{
      width: 48px;
      height: 40px;
      background-color: #e0f0fc;
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      position: absolute;
      right: 0;
      top: 90px;
      color: #dcdfe6;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
  }
  &__main {
    display: flex;
    width: 100%;
  }
  &__avatar {
    flex-shrink: 0;
    margin-right: 6px;
  }
  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: calc(100% - 60px);
    .info-name {
      color: #909399;
      position: relative;
      font-size: 20px;
      .name {
        max-width: calc(100% - 40px);
        color: #303133;
        @include utils-ellipsis(1);
      }
      .icon {
        margin-left: 3px;
        font-size: 26px;
        position: absolute;
        right: -1px;
        top: 1px;
        border-radius: 24px;
      }
      .status {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 12px;
        line-height: 20px;
        padding: 0 9px;
        border-radius: 3px;
        color: #fff;
      }
    }
    .info-item {
      display: inline-block;
      font-size: 14px;
      color: #909399;
      @include utils-ellipsis(1);
      margin-bottom: 6px;
      &:last-child {
        margin-bottom: auto;
      }
    }
  }
  .el-divider {
    margin: 5px 0;
    height: 2px !important;
  }
  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    .person-card__social {
      &>span{
        line-height: 20px;
      }
      .icon {
        position: relative;
        font-size: 14px;
        color: #606266;
        width: 40px;
        height: 20px;
        display: inline-block;
      }
    }
  }
}
</style>
