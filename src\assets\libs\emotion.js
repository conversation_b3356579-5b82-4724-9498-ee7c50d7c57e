import i18n from '@/lang';

/**
 * 政治倾向
 */
export const PoliticalOptions = [
  {
    label: i18n.t('dataCenter["中立"]'),
    value: 0,
    color: '#f7fbff',
    borderColor: '#fbe8ad',
    backgroundColor: '#ffa600'
  },
  {
    label: i18n.t('dataCenter["亲华"]'),
    value: 1,
    color: '#f7fbff',
    borderColor: '#bae9e4',
    backgroundColor: '#04c9b4'
  },
  {
    label: i18n.t('dataCenter["反华"]'),
    value: 2,
    color: '#f7fbff',
    borderColor: '#f9bdbd',
    backgroundColor: '#ff274b'
  },
  {
    label: i18n.t('dataCenter["无倾向"]'),
    value: 3,
    color: '#757d88',
    borderColor: '#dcdfe6',
    backgroundColor: '#f2f3f5'
  },
]

/* 政治倾向测试分析 */
export const PoliticalOptionsTest = [
  {
    label: i18n.t('dataCenter["中立"]'),
    value: 'Neutral',
    color: '#f7fbff',
    borderColor: '#fbe8ad',
    backgroundColor: '#ffa600'
  },
  {
    label: i18n.t('dataCenter["亲华"]'),
    value: 'Positive',
    color: '#f7fbff',
    borderColor: '#bae9e4',
    backgroundColor: '#04c9b4'
  },
  {
    label: i18n.t('dataCenter["反华"]'),
    value: 'Negative',
    color: '#f7fbff',
    borderColor: '#f9bdbd',
    backgroundColor: '#ff274b'
  },
  {
    label: i18n.t('dataCenter["无倾向"]'),
    value: 3,
    color: '#757d88',
    borderColor: '#dcdfe6',
    backgroundColor: '#f2f3f5'
  },
]


/**
 * 情绪分析
 */
export const EmotionOptions = [
  {
    prop: "positive",
    label: i18n.t('unit["正面"]'),
    type: "success",
    color: "#04c9b4",
    backgroundColor: '#e1f3f5',
    borderColor: '#bae9e4',
    field: "positive",
    value:1
  },
  {
    prop: "negative",
    label: i18n.t('unit["负面"]'),
    type: "danger",
    color: "#ff274b",
    backgroundColor: '#feeeee',
    borderColor: '#f9bdbd',
    field: "opposite",
    value:0
  },
  {
    prop: "neutral",
    label: i18n.t('unit["中立"]'),
    type: "info",
    color: "#ffa600",
    backgroundColor: '#fff4e0',
    borderColor: '#fbe8ad',
    field: "neutral",
    value:2
  }
];

export const EmotionSort = []
export const EmotionPropMap = {};
export const EmotionFieldMap = {};
EmotionOptions.forEach(item => {
  EmotionPropMap[item.prop] = item;
  EmotionFieldMap[item.field] = item;
  EmotionSort.push(item.prop)
})

/**
 * 情绪分析 越小越负面(已确认，文档有误)
 */
export const getEmotionInfo = val => {
  if (val >= 0.7) {
    return EmotionPropMap.positive;
  } else if (val > 0.3) {
    return EmotionPropMap.neutral;
  } else {
    return EmotionPropMap.negative;
  }
};
