// import { update } from 'lodash';

export default {
  // 文章管理（内容库管理）
  article: {
    materialBase: '/tbms/materialBase/aggs', // 素材库聚合
    detail: '/permission_proxy/cf/contentFile/detail',
    cultivate: '/tbms/generation/create',
    cultivate2: '/permission_proxy/v1/generate/text2',
    yq_head_portrait: '/intelligent/task/ai-generate-image',
    headPortartTask: id => `/intelligent/task/query-task?taskId=${id}`,
    language: '/tbms/aimBase/aggs',
    newLanguage: '/ums/code/language',
    tag: '/permission_proxy/cf/contentFile/tags',
    record: '/tbms/generation/record',
    del: '/permission_proxy/cf/contentFile/delete',
    add: '/permission_proxy/cf/contentFile/article',
    addTag: '/dcms/article/tag/add',
    edit: '/permission_proxy/cf/contentFile/edit',

    /* 系统资源导入文章模板下载 */
    download: '/permission_proxy/export/articleTemplate',

    /* 上传文章 */
    upload: '/permission_proxy/import/artical',

    /* 批量导出 */
    exportArticle: '/permission_proxy/export/article',
    topicList: '/ums/articleParam/listTopic',
    contentList: '/ums/articleParam/listContent',
    attitudeList: '/ums/articleParam/listAttitude'
  },

  // 评论管理
  comment: {
    // 批量导出评论
    exportComment: '/permission_proxy/export/comment',
    // 列表
    resource: '/permission_proxy/comment/frontend/list',
    // 标签搜索
    tag: '/permission_proxy/cf/contentFile/tags',
    // 表单使用：获取评论列表数据
    // list: '/permission_proxy/comment/list',
    list: '/permission_proxy/comment/frontend/list',
    language: '/permission_proxy/cf/contentFile/language',
    newLanguage: '/ums/code/language',
    del: '/permission_proxy/cf/contentFile/delete',
    add: '/permission_proxy/cf/contentFile/comment',
    addTag: '/dcms/comment/tag/add',
    // edit: id => `/permission_proxy/comment/${id}`,
    edit: '/permission_proxy/cf/contentFile/edit',
    download: '/permission_proxy/export/commentTemplate',
    update: '/permission_proxy/import/comment'// 批量导入评论
  },

  // 资源管理-手机号
  telphone: {
    resource: '/permission_proxy/phone/fuzzy/list',
    upload: '/permission_proxy/import/phone', // 导入手机号
    download: '/permission_proxy/export/template', // 导出模板
    areas: '/permission_proxy/phone/area_id',
    toggle: '/permission_proxy/phone/number',
    togglePhoneShareStatus: '/dcms/phone/share',
    detail: id => `/permission_proxy/phone/${id}`,
    shareResource: '/dcms/share_resource',
    addShare: '/dcms/phone/add/share',
    removeShare: '/dcms/phone/remove/share'
  },

  // 资源管理-邮箱
  email: {
    resource: '/permission_proxy/email/fuzzy/list',
    upload: '/permission_proxy/import/email',
    toggle: '/permission_proxy/email',
    download: '/permission_proxy/export/template',
    detail: id => `/permission_proxy/email/${id}`,
    addShare: '/dcms/email/add/share',
    removeShare: '/dcms/email/remove/share'
  },

  rule: {
    list: '/permission_proxy/rule/list',
    add: '/permission_proxy/misc/rule',
    detail: id => `/permission_proxy/rule/${id}`
  },

  // 系统设置-映射管理
  mapping: {
    list: '/permission_proxy/key_value/list',
    add: '/permission_proxy/key_value',
    detail: id => `/permission_proxy/key_value/${id}`
  },

  // 系统设置-注册账号分配
  assignment: {
    list: '/ums/mission/register/limit',
    primaryDepartmentList: '/ums/department/register/getAllMainDepartment'
  },

  // 系统设置-部门可见平台
  platformVisible: {
    getAllPlatformAndDepartment: '/ums/platform/getAllPlatformAndDepartment', // 获取所有平台和部门对应关系
    savePlatformRelation: '/ums/platform/savePlatformRelation', // 修改平台和部门对应关系
    getPlatformByDepartment: '/ums/platform/getPlatformByDepartment', // 根据部门获取可用平台
    getPlatformsGroupByAreaName: '/ums/code/getPlatformsGroupByAreaName', // 根据部门获取可用平台(按地区级联展示)
    getPlatformsByUsually: '/ums/code/getPlatformsOftenForReg'
  },

  // 系统设置-部门可注册账户数设置
  departmentRegisterAccounts: {
    getDepartmentRegisterAccounts: '/ums/platform/getAllPlatformAndDepartmentRegiester', // 列表
    updateRegisterAccounts: '/ums/platform/batchUpdatePlatformRelation' // 批量修改可注册数量
  },

  // 云服务管理
  cloudServer: {
    server: '/clouds/server',
    serverAction: (id, action) => `/clouds/server/${id}/${action}`,
    user: '/clouds/user',
    platform: '/clouds/platform',
    region: '/clouds/region',
    zone: '/clouds/zone',
    hardware: '/clouds/hardware',
    image: '/clouds/image',
    ip: '/clouds/elastic_ip',
    bindIp: id => `/clouds/elastic_ip/${id}`,
    disk: '/clouds/disk',
    bindDisk: id => `/clouds/disk/${id}`,
    serverVNC: id => `/clouds/server/${id}/vnc`
  },

  // 代理资源(IP池)管理
  proxy: {
    // 列表
    resource: '/pms/proxy/frontend/list',
    // 更新代理
    update: '/misc/update_proxy',
    // 代理类型枚举值
    types: '/pms/proxy/type',
    // 代理区域枚举值
    areas: '/pms/proxy/area',
    // 上传代理资源
    upload: '/pms/proxy/import/proxy',
    // 获取代理替换规则
    plan: '/permission_proxy/proxy/plan/area',
    // 修改、删除替换规则
    modifyPlan: id => `/permission_proxy/proxy/plan/${id}`,
    // 增加替换规则
    increasePlan: '/permission_proxy/proxy/plan',
    delete: '/pms/proxy/delete',
    tags: '/proxy/tag/name',
    exportResource: '/pms/proxy/frontend/list',
    download: '/permission_proxy/export/template',
    getProxyTags: '/pms/proxy/getProxyTags',
    getProxyTagsAndNum: '/pms/proxy/proxyCenter/getTagAvailableCounts',
    getProxyTagsAndCount: '/pms/proxy/getTagCounts',
    getProxyVPS: '/pms/proxyVps/allVps',
    migrate: '/permission_proxy/migrate',
    getAllMainDepartment: '/permission_proxy/getAllMainDepartment',
    netResourceUploadUrl: '/permission_proxy/cf/contentFile/addFile',
    searchProxy: '/pms/proxy/searchProxyByIp',
    changeAccountProxy: '/pms/proxyAccount/changeBreedProxyIpByManual',
    getProxyDetail: id => `/pms/proxy/byId/${id}`,
    getTreeProxyRegion: '/pms/proxy/frontend/treeProxyRegion',
    getTestProxyPlatform: '/pms/proxyInspectTask/getProxyInspectPlatforms',
    getTestProxyArea: '/pms/proxyInspectTask/groupArea',
    createInspectTask: '/pms/proxyInspectTask/createInspectTask',
    getProxyTaskInfo: '/pms/proxyInspectTask/getTaskInfo',
    getPageInspectRecord: '/pms/proxyInspectTask/pageInspectRecord',
    getLastInspectRecord: '/pms/proxyInspectTask/latestRecord'
  },

  // 数据处理
  dataHandle: {
    // 列表
    resource: '/handle_data_configs'
  },

  // 资源报表相关
  resourceDashboard: {
    platform: '/permission_proxy/account/statistics/time',
    account: '/task/daily/breedCount',
    report: '/permission_proxy/misc/resource/report',
    static: '/permission_proxy/misc/resource/communication',
    balance: '/misc/resource/balance',
    todayLockOrAdd: '/dcms/account/statistics/count',
    accountStatus: platform => `/permission_proxy/misc/resource/account?platform=${platform}`,
    ip: '/pms/proxy/top10',
    passCodeStatus: '/permission_proxy/task-center/dic/getCvsListByPlatform',

    media: '/permission_proxy/gatherweb/filePlatform/page',
    sysmedia: '/permission_proxy/cf/contentFile/page',
    delMedia: '/permission_proxy/resource/delete',
    addSysMedia: '/permission_proxy/cf/contentFile/addition',
    delSysMedia: '/permission_proxy/cf/contentFile/addition',
    list: '/permission_proxy/gatherweb/filePlatform/resource_platform',
    tagList: '/permission_proxy/cf/contentFile/tags',
    netTagList: '/permission_proxy/gatherweb/filePlatform/resource_tags',
    editMedia: '/permission_proxy/cf/contentFile/update',
    getSealedAccount: '/log/unlock/account/statistics',
    getAccountUnblockingTrend: '/log/unlock/account/dateAllStatistics',
    getAccountStatisticsToday: '/dcms/account/today/count',
    getCodeList: '/permission_proxy/gatherweb/userCount/codeList',
    getRegAndBanByPhone: '/permission_proxy/account/statistic',
    getRegAndBanByProxy: '/pms/proxyCount/statistic',
    getPhoneRegListByTag: '/permission_proxy/data-platform/phone/reg/list',
    getPhoneBanListByTag: '/permission_proxy/data-platform/phone/ban/list',
    getIpRegListByTag: '/pms/proxy/reg/list',
    getIpBanListByTag: '/pms/proxy/ban/list',
    getAimArticleList: '/tbms/aim/post/page',
    batchDeleteAimArticles: '/tbms/aim/post/batchDelete',
    batchDeleteArticles: '/tbms/material/post/batchDelete',
    getMaterialArticleList: '/tbms/material/post/page',
    getMaterialCommentList: '/tbms/material/comment/page',
    getMaterialMediaList: '/tbms/material/file/page',
    getAimAssociationList: '/tbms/aim/association/page',
    getAimAssociationListPage: '/tbms/aim/association/page2',
    updateAimPostTags: '/tbms/aim/post/edit',
    batchDeleteAimAssociation: '/tbms/aim/association/batchDelete',
    batchUpdateAimAssociation: '/tbms/aim/association/edit',
    aimAssociationTemplate: '/tbms/aim/association/template',
    batchDeleteComments: '/tbms/material/comment/batchDelete',
    batchUpdateComments: '/tbms/material/comment/edit',
    materialMediaDelete: '/tbms/material/file/batchDelete',
    materialMediaUpdate: '/tbms/material/file/edit',
    materialPostUpdate: '/tbms/material/post/edit',
    materialPostBatchUpdate: '/tbms/material/post/editBatch',
    materialPostDetail: '/tbms/material/post/detail',
    aimAddPost: '/tbms/aim/post/singleSave',
    aimPostDetail: '/tbms/aim/post/detail',
    materialAddPost: '/tbms/material/post/singleSave',
    materialAddComment: '/tbms/material/comment/singleSave',
    materialAddMedia: '/tbms/material/file/fileImport',
    upload2TbmsUseBucket: '/fldl/tbms/upload2TbmsUseBucket',
    materialPostBatchImport: '/tbms/material/post/fileImport',
    aimPostBatchImport: '/tbms/aim/post/fileImport',
    materialCommentImport: '/tbms/material/comment/fileImport',
    lockPlatform: '/tbms/material/file/lockPlatform'
  },

  // 虚拟作战人员枚举值相关
  accountEnums: {
    focusTopic: '/permission_proxy/focus_topic/fuzzy',
    school: '/permission_proxy/user/fuzzy/school',
    company: '/permission_proxy/user/fuzzy/company',
    profession: '/permission_proxy/user/fuzzy/profession',
    hobby: '/permission_proxy/hobby/fuzzy',
    familyRole: '/permission_proxy/family_role/fuzzy',
    location: '/permission_proxy/location/available_area_list',
    platform: '/permission_proxy/account/available/platform',
    phoneNumber: '/permission_proxy/phone/available/count',
    emailNumber: '/permission_proxy/email/available/count',
    availabelPhoneList: '/permission_proxy/phone/available/list',
    availabelEmailList: '/permission_proxy/email/available/list',
    availabelPhoneTagList: '/permission_proxy/phone/available/tag',
    availabelEmailTagList: '/permission_proxy/email/available/tag',
    availabelPhoneInfoList: '/permission_proxy/phone/status/list',
    availabelEmailInfoList: '/permission_proxy/email/status/list',
    phoneResourceOperation: (action, phone_id) => `/permission_proxy/data-platform/phone/${action}/${phone_id}`,
    emailResourceOperation: (action, email_id) => `/permission_proxy/data-platform/email/${action}/${email_id}`,
    setResourceForbidden: resourceKind => `/permission_proxy/data-platform/registration_error/${resourceKind}/update`,
    bindDeviceById: '/wsManage/ws/session/bindAccount'
  },
  // 培育 相关
  virtual: {
    focusOn: '/enumsAccount/v1/generate/breed',
    school: '/enumsAccount/v1/generate/school',
    post: '/enumsAccount/v1/generate/post',
    corporation: '/enumsAccount/v1/generate/corporation',
    hobby: '/enumsAccount/v1/generate/hobby'
  },
  // 系统自主创建账号
  account: {
    list: '/permission_proxy/account/list', // 获取自主创建账号列表
    underTaskList: '/task/getAccountInfoByTaskId', // 获取当前任务下的所注册的所有账号
    groupList: '/permission_proxy/group/list', // 获取团体列表
    noGroupAccount: '/permission_proxy/account/group/non_member', //  获取没有加入过团体的账号
    info: id => `/dcms/account/${id}`,
    SpareInfo: (id, env) => `/permission_proxy/spareAccount/${id}/${env}`,
    search: '/permission_proxy/account/list/fuzzy',
    tag: '/permission_proxy/account/tag',
    source: '/permission_proxy/account/source',
    count: '/account/property/list',
    upload: '/misc/account/json',
    logList: '/task/getTaskLogDetailsByAccountId', // 账号活动日志查询
    // breedLogList: '/task/getBreedTaskLogsByAccountId', // 培育记录日志查询
    breedLogList: '/task/getTaskLog',
    logCount: '/task/getTotalTaskInstanceLogsByAccountId',
    unsealLogList: '/log/unlock/account/search',
    addLabel: '/dcms/account/tag/add',
    timeList: '/permission_proxy/account/time/list',
    breedList: '/permission_proxy/account/breed/list',
    breedRuleList: '/permission_proxy/account/breed/rule/list', // 账号分级标准
    publicRecord: '/permission_proxy/account/public_page/record/list',
    clearPublic: '/permission_proxy/account/public_page/record',
    corpFilterList: id => `/permission_proxy/account/list/not_follower/${id}`,
    statusCount: '/permission_proxy/account/status/count',
    delete: '/permission_proxy/account/delete',
    export: '/permission_proxy/account/export/excel', // 自主
    importedExport: '/permission_proxy/imported_account/export', // 非合作
    importSpareAccount: '/permission_proxy/account',
    checkUser: '/permission_proxy/personalPortrait/findHasHistoryData',
    createPersonPortrait: '/permission_proxy/personalPortrait/addUserPortrayal',
    batchExWarehouse: '/task/account/outRepo',
    exWarehouseRecord: '/task/account/pageExportRecord',
    getExWarehouseAccountListbyId: id => `/task/account/listDetailAccountByRecordId/${id}`,
    putAccountsOperate: '/task/account/operate',
    putAccountsLock: '/dcms/account/lock',
    putAccountsUnLock: '/dcms/account/unlock',
    initConnect: '/wsManage/ws/session/action',
    getScreenStatusCallback: '/wsManage/ws/session/statusCallback', // 同屏成功/失败回调
    initControl: '/wsManage/ws/session/getControlFlag',
    getCurLiveScreen: '/wsManage/ws/operate/getCurLiveScreen', // 获取当前登录用户所有在线的同屏链接
    setCurLiveScreen: '/wsManage/ws/operate/addLiveScreen', // 设置当前登录用户所有在线的同屏链接
    setCurLiveScreenList: '/wsManage/ws/operate/batchAddLiveScreen',
    envCheckCallBack: '/wsManage/ws/operate/envCheckCallBack',
    loginSuccess: '/wsManage/ws/session/callbackLoginStatus',
    screenUploadFile: '/wsManage/ws/session/uploadFile',
    startWriteBackLog: '/task/breedDetail/operate',
    addManualBreedLog: '/task/breedDetail/addManualBreedLog',
    getProxyByIp: '/pms/proxy/getProxyByIp',
    getUnsealAccountList: '/log/unlock/account/getAccountLockInfo',
    getSpareAccountList: '/permission_proxy/spare/backend/list',
    changeAccountPhoto: '/task/uploadPhotoByTaskType',
    getAccountPhotoStatus: id => `/task/getAccountUploadPhotoStatus/${id}`,
    getAccountTagList: '/permission_proxy/account/tag/list',
    editAccountTag: id => `/permission_proxy/account/tag/${id}`,
    createAccountTag: '/permission_proxy/account/tag',
    delAccountTag: '/permission_proxy/account/tag',
    accountRecord: '/permission_proxy/account_record/record_list', // 分配记录列表
    redistribute: '/permission_proxy/account/redistribute', // 分配
    revoke: '/permission_proxy/account/revoke', // 撤回
    findTarget: '/permission_proxy/account_record/find_target', // 查询分配过的列表
    deleteRecord: '/permission_proxy/account_record/delete_record', // 删除分配记录
    accountRecordlist: id => `/permission_proxy/account/account_record/list/${id}`, // 分配记录根据id查账号
    accountRecordrevoke: id => `/permission_proxy/account/revoke/${id}`, // 分配记录根据id回收账号
    accountRedistribute: id => `/permission_proxy/account/redistribute/${id}`, // 分配记录根据id分配账号
    downloadUrlTemplate: file => `/permission_proxy/export/template?fileName=${file}`, // 下载批量导入的url模板文件
    resolveTargetUrlFile: '/permission_proxy/export/resolverExcel', // 上传目标Url 文件, 让后端进行解析
    createCopyTask: '/task/clone/createAccount', // 创建克隆任务
    changeAccountResource: id => `/permission_proxy/account/${id}`, // 更换手机号 邮箱
    getProxyChangeLogList: '/pms/proxy/getProxyChangeRecords',
    getResourceChangeLogList: '/permission_proxy/account_change_record/change_list',
    accountDeleteRecords: '/permission_proxy/account_change_record/list', // 账号删除记录列表
    accountDeleteRecordslist: '/permission_proxy/account_change_record/delete_list', // 删除记录根据id查账号
    fetchIceConfig: '/wsManage/ws/session/getIceConfig', // .根据账号的web_reg获取ice配置
    hangUp: '/wsManage/ws/session/hangUp', // 挂机
    putStatusAfterInit: '/wsManage/ws/session/putStatusAfterInit', // 初始化后推送状态
    accountBatchImport: '/dcms/account/excel/import', // 手机注册账号入库-批量导入，虚拟角色表
    accountBatchImportSpare: env => `/permission_proxy/spare/excel/import/${env}`, // 手机注册账号入库-批量导入，备用库
    importNewSpareAccount: '/permission_proxy/spare', /* 新的导入备用库 */
    importNewVirtualAccount: '/permission_proxy/account ', /* 新的导入虚拟角色表 */
    addCollect: '/dcms/account/collections/add', /* 添加收藏*/
    removeCollect: '/dcms/account/collections/remove', /* 删除收藏*/
    activeForumList: '/dcms/account-spot/list', /* 活跃版块列表 */
    addActiveForum: '/dcms/account-spot', /* 新建活跃版块 */
    editActiveForum: id => `/dcms/account-spot/${id}`, /* 更新活跃版块名称 */
    deleteActiveForum: '/dcms/account-spot', /* 删除活跃版块 */
    accountJoinActiveForum: '/dcms/account/spot/add', /* 批量添加热点板块 */
    getActiveforumAccountList: id => `/dcms/account/spot/list/${id}`, /* 活跃版块账号列表 */
    deleteActiveForumMember: '/dcms/account/spot/remove', /* 活跃版块移除成员 */
    setVncClipboard: '/wsManage/ws/session/vncClipboard',
    getVncClipboard: '/wsManage/ws/session/getVncClipboard',
    accountPreview: '/dcms/account/list/preview', // 账号信息预览
    previewList: '/dcms/account/field/export', // 账号预览字段
    accountPreviewList: '/dcms/account/user-item', // 当前账号所保存预览字段
    accountPreviewListSave: '/dcms/account/user-item/save', // 保存预览字段
    accountExcel: '/dcms/account/list/excel', // 导出账号信息
    accountExtraInfo: '/dcms/account/extra', // 查询账号群组/社群/频道
    fetchGroupList: '/dcms-migration/split-screen/list', // 获取团体数据
    fetchGroupAccountList: '/dcms-migration/split-screen/account-list', // 获取团体下面账号的数据
    getInfluenceGroup: '/tbms/aimBase/getInfluenceGroup', // 查询账号已加入相同标签群组/社群/频道
    getScreenRecordList: '/wsManage/screen-display-entity/findPage', // 查询同屏任务记录
    getScreenSaveAccount: '/wsManage/assist/saveAccount',
    getScreenCookie: '/wsManage/assist/getCookie',
    createAndStartConvertAccount: '/task/create/createAndStartConvertAccount', // 创建并启动更新转化账号任务
    createAndStartUpdateProfile: '/task/create/createAndStartUpdateProfile', // 创建并启动更新个人信息任务
    getAccountTaskLastProcess: accountId => `/task/account/back/getAccountTaskLastProcess/${accountId}`, // 账号后台最新任务进度
    listUserGroup: '/dcms-migration/split-screen/list-user-group', // 获取群操群控下人设分组
    userAccountCount: '/dcms-migration/split-screen/user-account-count', // 获取群操群控下人设分组离账号数量
    userAccountList: '/dcms-migration/split-screen/user-account-list', // 获取群操群控下人设分组下账号列表
    getCoreData: '/permission_proxy/gatherweb/user/coreData' // 获取任务情况
  },

  // 人设
  charactor: {
    phoneEmaillist: '/permission_proxy/user/phone_email',
    list: '/permission_proxy/user/list',
    detail: id => `/permission_proxy/user/${id}`,
    Sparedetail: (id, env) => `/permission_proxy/spareAccountUserInfo/${id}/${env}`,
    availableCount: '/permission_proxy/user/frontend/available/count', // 可用人设数量
    ipCount: '/proxy/count',
    language: '/permission_proxy/user/available/language',
    available_areas: '/permission_proxy/user/available_area', // 人设可以创建的地区
    fake_available_areas: '/static/fakeData/charactor/available_area.json', // [前端配置]人设可以创建的地区
    area: '/permission_proxy/user/available/area', // 可用人设地区
    country: '/permission_proxy/user/area', // 所有人设所在国家列表
    userTag: '/permission_proxy/user/in_use/user_tag', // 查询人设 认知倾向 & 人设标签
    addUserTag: '/dcms/user/tag/add',
    reset: '/permission_proxy/user/user_access_time/reset', // 重置人设
    unRegistePlatform: '/permission_proxy/rule/platform/list', // 人设-未注册的平台枚举
    delete: '/permission_proxy/user/delete',
    update: id => `/permission_proxy/user/${id}`,
    availableList: '/permission_proxy/user/available/list', // 注册任务创建时，获取可用人设
    importCharacter: '/permission_proxy/user',
    upload: '/permission_proxy/export/persons',
    download: '/permission_proxy/export/template',
    codeArea: '/permission_proxy/task-center/dic/getVerifyNations',
    cvsListByPlatform: '/permission_proxy/task-center/dic/getCvsListByPlatform',
    codeAvailableCount: '/permission_proxy/user/available/empty/count',
    getDeviceInfo: '/permission_proxy/device/search_by_userid',
    get3rdAvailableCount: '/permission_proxy/task-center/thirdTask/getInvalidCount', // 获取接码平台可注册账号数量
    getShowDateList: '/task/log/date/list',
    getDeleteList: '/task/ids',
    getUserTagList: '/permission_proxy/user/tag/list',
    editUserTag: id => `/permission_proxy/user/tag/${id}`,
    createUserTag: '/permission_proxy/user/tag',
    delUserTag: '/permission_proxy/user/tag',
    versionRecordList: '/permission_proxy/version/list',
    versionCreate: '/permission_proxy/version/batch',
    cascadeUserAreas: '/permission_proxy/user/area_info', // 人设列表, 地区级联下拉
    cascadeAccountAreas: '/dcms/account/area_info', // 账号列表, 地区级联下拉
    batchGenerateCharacter: '/permission_proxy/user/gen',
    setCharacterPlatformForbidden: '/permission_proxy/registration_error/update',
    politicalTendency: '/person-generate//v1/common/politicalTendency', // 认知倾向
    industry: '/person-generate/v1/occupationData/industry', // 认知倾向
    getAccountAllowedNum: '/task/resource/accountAllowed', // 获取可注册账号的数量
    getCharacterResources: '/dcms-migration/getByPersonId', // 根据人设id获取人设资源信息
    deleteCharacterResources: '/dcms-migration/del', // 删除人设资源
    setCharacterResourcesTop: '/dcms-migration/top', // 置顶人设资源
    uploadCharacterResources: personId => `/dcms-migration/upload/${personId}`, // 上传人设资源
    editCharacterResources: '/dcms-migration/update', // 编辑人设资源
    addCharacterGroup: '/dcms-migration/userGroup', // 创建人设分组
    getCharacterGroups: '/dcms-migration/userGroup/all', // 查询人设分组
    editCharacterGroup: id => `/dcms-migration/userGroup/${id}`, // 编辑人设分组名称
    deleteCharacterGroup: id => `/dcms-migration/userGroup/${id}`, // 删除人设分组
    addMemberCharacterGroup: id => `/dcms-migration/userGroup/${id}/addMember`, // 添加人设
    removeMemberCharacterGroup: id => `/dcms-migration/userGroup/${id}/removeMember` // 移除人设
  },

  // 非合作手段导入账号
  importedAccount: {
    list: '/permission_proxy/imported_account/list',
    detail: id => `/permission_proxy/imported_account/${id}`,
    tag: '/permission_proxy/imported_account/tag',
    source: '/permission_proxy/imported_account/source',
    upload: '/permission_proxy/import/nonAccount',
    platform: '/permission_proxy/imported_account/platform',
    delete: '/permission_proxy/imported_account/delete',
    update: id => `/permission_proxy/imported_account/${id}`,
    download: '/permission_proxy/export/template',
    getCharacterAccount: '/person-generate/v1/generate/attribute',
    importSpareAccount: '/permission_proxy/imported_account/import'
  },

  // 虚拟团体
  corps: {
    // list: '/account/list/follower',  // 查看团体成员 @deprecated
    list: '/permission_proxy/account/group/{groupId}', // 查看团体成员
    // corp: '/permission_proxy/corps', // 新增团体 @deprecated
    corp: '/dcms/group', // 新增团体
    // updateMember: '/permission_proxy/corps/follow' // 更新团体成员 @deprecated
    updateMember: '/permission_proxy/group/member/{groupId}', // 更新团体成员
    accountJoinCorp: '/permission_proxy/group/member/batchAdd', // 批量添加账号到团体
    updateGroup: '/permission_proxy/group/{groupId}', // 更新团体的信息
    addQuickCorp: '/permission_proxy/groupWithAccounts',
    deleteInvalidAccount: '/task/group/clean',
    getCorpsGroups: id => `/permission_proxy/group/${id}`,
    getAllCorpsGroupslist: '/permission_proxy/group/list/all',
    batchTagAdd: '/dcms-migration/group/tag/batchAdd',
    batchTagDelete: '/dcms-migration/group/tag/batchDelete'
  },

  // 主页数据
  dashboard: {
    events: '/events',
    eventDetail: '/event',
    articles: '/event/passages',
    articleDetail: '/event/passage_detail',
    download: '/permission_proxy/graph/download'
  },
  // 新主页数据
  new_dashboard: {
    createEvent: '/permission_proxy/hot_event/create', // 热点事件创建
    editEvent: '/permission_proxy/hot_event/edit', // 热点事件编辑
    deleteEvent: '/permission_proxy/hot_event/delete', // 热点事件删除
    followEvent: '/permission_proxy/hot_event/attention_add', // 热点事件关注
    events: '/gatherbd/statistics/get_hot_topics',
    new_events: '/permission_proxy/hot_event/get_hot_event_list',
    followEventDetail: '/permission_proxy/hot_event/detail', // 关注的热点事件详情
    eventDetail: '/gatherbd/statistics/get_hot_info',
    new_eventDetail: '/gatherbd/statistics/get_hot_event_info',
    articles: '/gatherbd/statistics/get_hot_list',
    new_articles: '/gatherbd/statistics/get_post_list',
    articleDetail: '/permission_proxy/gatherweb/bduser/post_details',
    topicType: '/permission_proxy/utils/text_type',
    topicLanguage: '/permission_proxy/utils/get_lang',
    hotForum: '/permission_proxy/utils/platforms',
    displayHotForum: typeSource => `/ums/system/hotPlatform/${typeSource}`,
    saveDisplayHotForum: typeSource => `/ums/system/saveHotPlatform/${typeSource}`,
    mapJSON: region => `${location.protocol + '//' + location.host}/static/js/map-json/${region}.json`,
    mapRankByRegion: '/event/get_post_stat_locations',
    uploadSource: '/permission_proxy/event/upload_event',
    updateEmotion: '/permission_proxy/hot_topic/update_emotion',
    getSearchCode: '/permission_proxy/gatherweb/dictionary/getDictionary',
    getPlatformCode: '/permission_proxy/param/gatherweb/getDictionary'
  },
  // 主页监控账户报表
  accout_report: {
    author: '/permission_proxy/key_target/authors',
    newsAuthor: '/permission_proxy/key_target/get_author_new',
    chart: '/gatherbd/author',
    time: '/permission_proxy/gatherweb/common/getFrequency',
    article: '/permission_proxy/gatherweb/bduser/get_post',
    articleDetail: '/permission_proxy/translate_post/post_details',
    speech: '/permission_proxy/gatherweb/bduser/main_speech',
    politicalData: '/gatherbd/politic_trend',
    relationData: '/permission_proxy/gatherweb/user/countRelation',
    testGroupMap: '/permission_proxy/utils/text_group'
  },
  // 知识图谱
  knowledge_graph: {
    enum: type => `/permission_proxy/graphs/hugegraph/graph/vertices?label=${type}&limit=-1`,
    subjectOrTopicEnum: (type, depth) => `/permission_proxy/graphs/hugegraph/traversers/kout?source="${type}"&direction=OUT&max_depth=${depth}`,
    getAllRegion: '/permission_proxy/graph/getAllRegion',
    getVertex: '/permission_proxy/graph/getVertex',
    getAdjacent: '/permission_proxy/graph/getAdjacent',
    downloadTemplate: '/permission_proxy/graph/download',
    uploadTemplate: '/permission_proxy/graph/upload_graph'
  },
  group: {
    groupPortraits: '/permission_proxy/group_portraits/list',
    politicTrend: '/permission_proxy/group_portraits/politic_trend',
    emotionTrend: '/permission_proxy/group_portraits/emotion',
    topics: '/permission_proxy/group_portraits/topics',
    distribute: '/permission_proxy/group_portraits/distribute',
    users: '/permission_proxy/group_portraits/list_users', // 群体用户
    active_users: '/permission_proxy/group_portraits/users', // 群体活跃用户
    posts: '/permission_proxy/group_portraits/posts',
    add_user: '/permission_proxy/group_portraits/add_users',
    getGroupAccountReport: '/permission_proxy/group_portraits/author'
  },

  // 消息中心
  message: {
    list: '/message-center/notification/search',
    type: '/message-center/notification/type',
    detail: '/task/dailyReport/id/',
    dailyDetail: '/task/dailyReport/query',
    read: '/message-center/notification/read',
    readAll: '/message-center/notification/readAll',
    getMonthlyReport: '/permission_proxy/monthlyAccount/query',
    getFaultMessage: '/task-auxiliary/message/getMessageList',
    judgeFileDownloaded: fileName => `/fldl/download/judgeFileDownloaded?fileName=${fileName}`,
    getSystemMessage: '/task-auxiliary/message/getSystemUpdateMessage'
  },

  // 系统设置 - 程序包管理
  sysProgram: {
    list: '/misc/file_grabbing',
    upload: '/misc/file_uploader',
    delete: '/misc/downloadfile',
    type: '/misc/file_types',
    platform: '/misc/file_platforms'
  },

  // 系统管理
  system: {
    login: '/auth/token',
    logout: '/auth/user/logout',
    getCurUser: '/ums/user/getCurUser', // 获取当前登录的用户
    verificationCode: '/auth/getCaptchaById', // 获取登录验证码
    testCaptcha: '/auth/createCaptcha', // 获取系统测试验证码
    addDepartment: '/ums/department/add', // 新增部门
    deleteDepartment: '/ums/department/del', // 删除部门
    editDepartment: '/ums/department/edit', // 编辑部门
    role: '/ums/role/getRoleByDepId', // 通过部门id获取角色
    addRole: '/ums/role/add', // 添加角色
    editRole: '/ums/role/edit', // 编辑角色
    deleteRole: '/ums/role/del', // 删除角色
    roleWithAuth: '/system/roles/privileges',
    auth: '/ums/menu/getOwnMenuTree', // 获取当前登录用户的权限列表
    addUser: '/ums/user/add', // 添加用户
    editUser: '/ums/user/edit', // 编辑用户
    deleteUser: '/ums/user/del', // 删除用户
    depart: '/ums/department/treeOwn', // 获取当前用户能够看到的部门列表
    mainDepart: '/ums/department/getMainDepartment', // 获取当前用户能够看到的一级部门列表
    editPassword: '/auth/changePassword', // 修改密码
    departUser: '/ums/user/page', // 根据部门获取用户列表
    getMenuTreeByRoleId: '/ums/menu/getMenuTreeByRoleId', // 获取对应角色的权限
    coolDown: '/permission_proxy/user/cooldown/platform',
    switchDepartList: '/ums/department/acrossDep/getAllMainDepartment', // 获取可切换的部门列表
    getNewToken: '/auth/getNewToken', // 切换部门时获取新的token,
    getUserListByDepIds: '/ums/user/getUsersByDepartments', // 根据选择的部门ids，来获取用户（不分页）
    getRoleByDepId: '/ums/role/getRoleByDepIds', // 根据选择的部门ids，来获取角色（不分页）
    getTaskCreatorListByDepId: '/task/groupTaskUser', // 根据当前部门来获取所有任务的创建人列表
    getUserLoginLog: '/auth/userLoginLog/getAll', // 获取所有用户登录日志
    allSystemUserList: '/wsManage/screen-display-entity/findTaskUser' // 获取所有用户包含猫池的账号
  },

  // 自动任务
  dailyMission: {
    ManualList: '/task/daily/getCollectTask',
    list: '/task/daily/getDailyTask', // 获取注册、培育、采集表格数据(abandon)
    pageList: '/task/daily/getPageDailyTask', // 获取注册、培育、采集表格数据
    breedMemberList: '/task/breed/detail',
    getTaskStatistical: '/task/daily/getTaskStatistical', // 任务数量统计
    breedSummary: '/task/breed/summary' // 通过taskId查培育账号统计
  },
  // 注册任务
  registerMission: {
    mission: '/index_mission',
    getParentId: '/index_mission/id',
    subtask: '/index_mission/detail',
    statistic: '/index_mission/statistic',
    start: '/index_mission/start',
    resume: '/index_mission/resume',
    pause: '/index_mission/suspend',
    haveCodePlatform: '/permission_proxy/task-center/dic/getPlatformList'
  },
  // 任务管理
  missionManage: {
    createTwitterHomePage: '/task/save/tw-pages', // 创建twitter公共主页
    createParentTask: '/task/save', // 创建任务
    createQuickChildTask: '/task/createTaskWithParent', // 创建快速子任务
    createTaskByTemplate: '/task/save/from-template', // 通过模板创建任务
    departmentUser: '/system/department/ids',
    report: '/mission/report',
    tag: '/task/tag/findByName', // 通过标签名搜索标签
    tagAll: '/task/tag/findAll', // 查询当前用户能查看的所有任务标签
    search: '/task/search',
    updateFrequency: '/task/update/frequency', // 更新任务执行周期
    updateTaskTop: '/task/top', // 任务置顶
    mission: '/mission',
    missionGraph: '/mission/graph',
    star: '/mission/star',
    unstar: '/mission/unstar',
    dashboard: '/task/manual/list',
    timeline: '/mission/timeline',
    taskId: '/task/{id}', // 获取任务详情和更新任务都会用到
    reportSummary: '/task/report', // 舆论总结上报
    execute: '/task/execute', // 执行任务的各种操作(启动、暂停、停止、重跑、恢复等)
    result: '/mission/result',

    // clone: '/mission/clone',
    logs: '/task/log/list',
    stepLogs: '/task/log/listPage',
    getStepLogs: '/task/log/listPageNew',
    clone: '/task/clone', // 复制任务
    addTag: '/task/tag/add', // 任务添加标签
    deleteTag: '/task/tag/delete', // 任务删除标签
    updateTag: '/task/tag/addOrUpdate', // 任务更新标签
    subtask: '/mission/task',
    summarize: '/mission/summarize', // 任务概述
    missionStatistics: '/permission_proxy/situation/getStatistics', // 主任务详情-舆论态势
    getPublicOpinionTrend: '/task/statistics/getPublicOpinion',
    getEventTime: '/task/statistics/getEventTime',
    getPublicOpinion: '/task/statistics/getUserOpinionReport',
    getUserOpinionReportUser: '/task/statistics/getUserOpinionReportUser',
    getEQuery: '/task/statistics/getEQuery',
    netOpinion: '/permission_proxy/situation/getOpinion',
    corpsStatistic: '/task/opinion/total',
    listContent: '/task/opinion/list',
    missionStats: 'mission/target',
    history: '/message-center/notification/findTaskInfo', // 上报历史
    collectionMission: '/collect_mission',
    getTagList: '/permission_proxy/gatherweb/taskTag/list',
    groupMission: {
      start: '/group_mission/start',
      pause: '/group_mission/suspend',
      resume: '/group_mission/resume',
      log: '/task/log/detail',
      graph: '/group_mission/graph',
      update: id => `/task/${id}`,
      updateMissuonCycle: '/task/update/frequency',
      createMission: '/task/save',
      delMission: id => `/task/${id}`

    },
    collectionRecommend: id => `/task/daily/findMissions/${id}`,
    operationGraph: '/operation/graph',
    uploadFile: '/fldl/file/uploadLocalAndCloudWithExpiration', // 上传视频和上传图片 用于策略任务 主页运营
    downloadFile: '/fldl/download/downloadFromUrlBackFile', // 从公/私有云上面下载文件 返回二进制流数据
    uploadPersonAvatarUploadLocale: '/fldl/file/personAvatarUploadLocal', // 上传人设头像和背景图
    uploadAccountAvatar: '/fldl/file/uploadFile2PhotoBucketBackHttpUrl', // 上传账号的头像和账号的背景
    screenUploadFile: '/fldl/file/uploadCloudWithExpirationMap', // 上传账号的头像和账号的背景
    childList: '/task/children', // 分页查询子任务列表
    allTaskCreator: '/task/creator', // 查询所有创建过任务的用户
    allAssignPerson: '/task/assignee', // 查询所有协作过任务的用户
    getCodePlatformList: '/permission_proxy/param/createGatherTaskGetPlatforms', // 获取平台列表
    getCodeOldPlatformList: '/task/codePlatform/list', // 获取平台列表
    getCollectionRegionsList: '/task/codePlatform/getCollectionRegions', // 获取地区方向
    getDataTypeByPlatformCodeList: '/task/codePlatform/getDataTypeByPlatformCode',
    getCollectTypesList: '/task/codePlatform/getCollectTypes',
    getCommonCodeDict: '/task/codePlatform/getCommonCodeObj', // 获取通用配置的码表
    getReportReasonList: '/task/report/list', // 举报任务获取级联举报理由
    getReportReasonListLabel: '/task/report/id',
    getBreedTaskHistoryList: '/task/history/page',
    checkCropsAccount: '/task/account/check',
    getRegisterTypeList: '/task/code/device',
    getReportUser: '/ums/user/getReportUser',
    getTaskAllExecutionStatus: '/task/getTaskAllExecutionStatus',
    getTaskGroupList: '/task/group/list',
    createTaskGroup: '/task/group/add',
    delTaskGroup: '/task/group/delete',
    updateTaskGroup: '/task/group/update',
    getCommentGifCodeList: '/task/commentGifCode/list',
    getTaskChildrenPlatformList: '/task/searchTaskPlatform',
    getBreedStrategy: '/task/breed/strategy/getByPlatform',
    getAllDeviceType: '/task/code/device/getAllDeviceType',
    getAllTaskUser: '/task/daily/getAllTaskUserByBeptId',
    parentExecutionResult: '/task/statistics/parentExecutionResult', // 父任务执行情况统计
    childrenExecutionResult: '/task/statistics/childrenExecutionResult', // 子任务执行情况统计
    getBreedSupportAction: '/task/codePlatform/getBreedSupportAction', // 获取平台动作交集
    addBreedStrategy: '/precision/strategy/add', // 新增培育策略
    fetchStarategyList: '/precision/strategy/list',
    deleteBreedStrategy: '/precision/strategy/batchDelete',
    getActiEnestimate: '/precision/strategy/actiEnestimate',
    deleteBreedPlan: '/task/instance/batchDelete',
    updateActionDetail: '/task/instance/batchAddOrUpdate',
    getPlatformRegisterMode: '/task/code/device/getPlatformRegisterMode',
    getAllTargetErrorType: '/task/errorCode/config/getAllTargetErrorType',
    createCollectTask: '/task/createGatherTask', // 舆论采集接口
    batchCreateRegisterTask: '/task/create/batchCreateRegister',
    createAndStartPostTask: '/task/create/createAndStartPostTask'
  },

  // 模板管理
  template: {
    // templ: '/template',
    // graph: '/template/graph',
    // save: '/template/save',
    // intro: '/template/summarize',
    // sys: '/template/system',
    // copy: '/template/clone',
    taskTemplate: '/task/template', // 任务模板
    searchTemplateList: '/task/template/search', // 搜索模板列表
    findByIdtempl: id => `/task/template/${id}`, // 新增模板
    delTempl: '/task/template', // 删除模板
    copy: '/task/template/clone', // 克隆模板
    updateTempl: id => `/task/template/${id}`, // 修改模板
    searchTemplate: '/task/template/search', // 根据分页获取模板数据
    createTemplateByTask: '/task/save/to-template', // 转存为策略任务模板
    getTaskDetail: id => `/task/template/detail/${id}`, // 获取任务模板详情
    getChildTempl: '/task/template/children', // 获取任务模板详情
    updateSubTaskInfo: '/task/template/{id}', // 更新子任务模板
    createdPlatformList: '/task/template/getPlatform' // 策略任务模板 > 行为组 聚合出来的所有包含 “已创建过行为组” 的平台
  },

  // upload 地址管理
  upload: {
    ip: '/misc/proxy/json',
    account: '/misc/account/json',
    notcpt: '/misc/account/notcpt'
  },

  // 运营中心 主页审核
  audit: {
    list: '/examine/publicPage/page',
    editors: '/examine/publicPage/getExamineeNamesGroup',
    pass: '/examine/publicPage/pass', // 审核通过
    refuse: '/examine/publicPage/refuse', // 拒绝审核
    Detail: '/examine/publicPage/getById'
  },

  // 运营中心
  operation: {
    addPost: '/examine/publicPage/add', // 发帖(发送审核)
    deleteList: '/examine/publicPage/delete', // 删除帖子
    promotion: '/task/history-count-detail/{id}',
    history: '/operate_post/history',
    reCreate: '/operate_open/publicpage',
    createPagesTask: '/task/save/of-pages', // 创建公共主页
    getByParentTaskId: '/examine/publicPage/getByParentTaskId', // 查看指定虚拟用户下的帖子
    detailGeneralizeHistory: '/task/grandchildren', // 在子任务详情里面查询推广记录,
    findTaskIdByPublicPageAccount: '/task/find/page-task-of-account',
    fetchTypeList: '/task/publicPage/getLabel'
  },

  // 网络资源
  netResource: {
    // 真实账号
    account: {
      getNewList: '/permission_proxy/gatherweb/common/reportUser/list',
      list: '/permission_proxy/gatherweb/user/page',
      check: '/permission_proxy/gatherweb/user/checkUser', // 改成内部接口 不用科大接口
      platform: '/permission_proxy/gatherweb/user/platform',
      breedPlatform: '/ums/codeBreedPlatform/list',
      exist: '/permission_proxy/monitor/monitor/exist',
      delete: '/net/account',
      monitorPlatform: '/ums/codeBreedPlatform/list4EnableAddExample',
      chatList: '/permission_proxy/gatherweb/message/group',
      chatDetailList: '/permission_proxy/gatherweb/message/list',
      followerUserList: '/permission_proxy/gatherweb/user/follower_user',
      allPlatform: '/task/all/platform',
      addTask: '/permission_proxy/cf/contentFile/addTask',
      exportAccount: '/permission_proxy/gatherweb/user/export', // 导出账号
      accountAddition: '/tbms/aim/user/resourceImport', // 账号添加至培育目标库或策略目标库
      homePageAddition: '/tbms/aim/mainPage/resourceImport', // 主页添加至培育目标库或策略目标库
      articleAddition: '/tbms/aim/post/resourceImport', // 文章添加至培育目标库或策略目标库
      articleAdditionMaterial: '/tbms/material/post/resourceImport', // 文章添加至素材库
      commentAdditionMaterial: '/tbms/material/comment/resourceImport', // 评论添加至素材库
      fileAdditionMaterial: '/tbms/material/file/resourceImport', // 文件添加至素材库
      groupAdditionMaterial: '/tbms/aim/association/resourceImport', // 社群添加至培育目标库或策略目标库
      boardAdditionMaterial: '/tbms/aim/group/resourceImport', // 群组添加至培育目标库或策略目标库
      topicAdditionMaterial: '/tbms/aim/topic/resourceImport', // 话题添加至培育目标库或策略目标库
      getUserPortraitCount: '/permission_proxy/gatherweb/user/getUserPortraitCount', // 获取人物关系聚合
      getUserPortrait: '/permission_proxy/gatherweb/user/getUserPortrait', // 获取人物关系列表
      getUserGroup: '/permission_proxy/gatherweb/group/getDetailByIds', // 获取共同群组
      getUserCommon: '/permission_proxy/gatherweb/common/relation', // 获取点赞或转发记录
      getUserCommonTotal: '/permission_proxy/gatherweb/common/relation/total', // 获取点赞或转发记录
      getUserComment: '/permission_proxy/gatherweb/comment/relation', // 获取点赞或转发记录
      getMessage: '/permission_proxy/gatherweb/userDirectMessages/page', // 获取私信记录
      getUserDetail: '/tbms/aim/user/detail', // 获取目标库账号详情
      getMainPageDetail: '/tbms/aim/mainPage/detail', // 获取目标库主页详情
      getGroupDetail: '/tbms/aim/group/detail', // 获取目标库群组详情
      getTopicDetail: '/tbms/aim/topic/detail', // 获取目标库群组详情
      getAssociationDetail: '/tbms/aim/association/detail', // 获取目标库社群详情
      getTask: '/permission_proxy/gatherweb/task/getTask', // 获取分析状态
      createTask: '/permission_proxy/gatherweb/task/createTask', // 一键分析
      retryTask: '/permission_proxy/gatherweb/task/retryTask' // 重试
    },
    // 文章库
    article: {
      list: '/permission_proxy/gatherweb/post/page',
      editMarkTag: '/permission_proxy/gatherweb/common/editMarkTag',
      delMarkTag: '/permission_proxy/gatherweb/common/delMarkTag',
      transList: '/permission_proxy/translate_post/get_post',
      transDetail: '/permission_proxy/translate_post/post_details',
      emotion: '/permission_proxy/mark_article',
      comments: '/permission_proxy/gatherweb/comment/page',
      language: '/permission_proxy/Translate/LanguageDetection',
      trans: '/permission_proxy/Translate/TranslateText',
      // platform: '/permission_proxy/post_platform ',
      calcu_text: '/permission_proxy/calcu_text',
      standpoint_analysis: '/permission_proxy/proxy/standpoint_analysis',
      del: '/permission_proxy/resource_post/delete',
      deltrans: '/permission_proxy/translate_post/delete_post',
      transComments: '/permission_proxy/translate_comment/get_comments_by_post_id',
      addSysArticle: '/permission_proxy/cf/contentFile/article',
      getLocations: '/permission_proxy/gatherweb/code/locationCodeList',
      exportArticle: '/permission_proxy/gatherweb/post/export',
      getArticleData: '/permission_proxy/gatherweb/mark/getWaitMarkPost',
      getTagList: '/permission_proxy/gatherweb/tag/list',
      addMark: '/permission_proxy/gatherweb/mark/add',
      getWaitMarkFile: '/permission_proxy/gatherweb/mark/getWaitMarkFilePlatform',
      getArticleTopic: '/permission_proxy/gatherweb/textType/list',
      getEditArticleData: '/permission_proxy/gatherweb/mark/getMarkPost',
      getEditMarkFile: '/permission_proxy/gatherweb/mark/getMarkFilePlatform',
      uploadFile2translate: '/permission_proxy/translate/article',
      postDetail: '/permission_proxy/gatherweb/post/detail'
    },
    // 评论管理
    comment: {
      list: '/permission_proxy/gatherweb/comment/page',
      platform: '/permission_proxy/gatherweb/comment/origen',
      del: '/permission_proxy/resource_comment/delete',
      deltrans: '/permission_proxy/translate_comment/delete_comment',
      transList: '/permission_proxy/translate_comment/get_comment',

      /* 网络资源评论添加到系统资源评论接口 */
      addSysComment: '/permission_proxy/cf/contentFile/comment',
      exportComment: '/permission_proxy/gatherweb/comment/export'
    },
    CollectReport: {
      DataLocation: '/gatherbd/gatherReport/data_location',
      DataStatistics: '/gatherbd/gatherReport/data_statistics',
      DataType: '/gatherbd/gatherReport/data_type',
      DataToday: '/gatherbd/gatherReport/data_today',
      Topic: '/gatherbd/gatherReport/data_hotWord',
      BlockTimes: '/log/unlock/account/statisticsGather',
      BlockRecord: '/log/unlock/account/pageGatherLock',
      BlockRecordPlatformList: '/log/unlock/account/gatherLockPlatform'
    },
    // 榜样用户的接口
    example: {
      list: '/permission_proxy/monitor/group/list', // 接口和监控中心分组接口一样 通过gatherType区分他们的值
      add: '/permission_proxy/monitor/group/create',
      delete_invalid: '/permission_proxy/monitor/accountGroup/clearInvalid',
      delete: '/permission_proxy/monitor/group/del',
      rename: '/permission_proxy/monitor/group/edit',
      msg: '/permission_proxy/param/accountGroup/statistics',
      // search: '/permission_proxy/example_user/search',
      member: '/permission_proxy/monitor/accountGroup/listPage',
      // user: '/permission_proxy/example_user'
      user: '/permission_proxy/monitor/account',
      available: '/permission_proxy/monitor/accountGroup/valid_count',
      exampleList: '/permission_proxy/gatherweb/user/support_monitor_user'
    },

    // 群组的接口
    group: {
      tagList: '/permission_proxy/group_manage/tag',
      addGroup: '/permission_proxy/group_manage/create',
      batchAddGroup: '/permission_proxy/export/group',
      editGroup: id => `/permission_proxy/group_manage/${id}`,
      downloadFile: file => `/permission_proxy/export/template?fileName=${file}`, // 下载文件
      // 给群组批量打标签
      batchAddTags: '/permission_proxy/group_manage/tag/add',
      queryGroupList: '/permission_proxy/group_manage/group_list',
      deleteGroup: '/permission_proxy/group_manage/delete'
    },
    // 群组
    groupMsg: {
      filterPlatform: '/permission_proxy/gatherweb/chatGroup/platform',
      list: '/permission_proxy/gatherweb/chatGroup/list',
      groupDetail: '/permission_proxy/gatherweb',
      groupMessageList: '/permission_proxy/gatherweb/userDirectMessages/groupMessage',
      groupUsers: '/permission_proxy/gatherweb/user/page2',
      messageHotWord: '/permission_proxy/gatherweb/group/getHot',
      userGroups: '/permission_proxy/gatherweb/group/getByUser',
      groupUserDetail: '/permission_proxy/gatherweb/user/groupUserDetail',
      messageLinks: '/permission_proxy/gatherweb/group/getLinks',
      getBoardList: '/permission_proxy/gatherweb/group/page',
      getBoardDetail: '/permission_proxy/gatherweb/group/detail',
      getPlatformIdByUrl: '/permission_proxy/gatherweb/common/exchangeUrl',
      groupAddTask: '/permission_proxy/cf/contentFile/addTask',
      exportGroup: '/permission_proxy/gatherweb/group/export'
    },
    // 审核
    audit: {
      auditGroup: '/examine/gather/audit/group',
      auditUser: '/examine/gather/audit/user',
      auditGroupList: '/examine/gather/group/page',
      auditUserList: '/examine/gather/user/page',
      auditArticleList: '/examine/gather/post/page',
      auditArticle: '/examine/gather/audit/post',
      auditTopicList: '/examine/gather/topic/page',
      auditTopic: '/examine/gather/audit/topic'

    },
    // 网站
    web: { webList: '/permission_proxy/gatherweb/v2/website/list' },
    // 账号主页社群-导入
    importApi: { batchImport: '/permission_proxy/import/cfResource' }
  },
  // 培育目标库及策略目标库
  breedTarget: {
    // 账号
    account: {
      list: '/tbms/aim/user/page', // 列表接口
      listPage: '/tbms/aim/user/page2', // 列表接口
      delete: '/tbms/aim/user/batchDelete', // 批量删除
      edit: '/tbms/aim/user/edit', // 编辑
      fileImport: '/tbms/aim/user/fileImport', // 导入
      export: '/tbms/aim/user/export' // 导出
    },
    // 主页
    homePage: {
      list: '/tbms/aim/mainPage/page', // 列表
      listPage: '/tbms/aim/mainPage/page2', // 列表
      delete: '/tbms/aim/mainPage/batchDelete', // 删除
      edit: '/tbms/aim/mainPage/edit', // 编辑
      fileImport: '/tbms/aim/mainPage/fileImport', // 导入
      export: '/tbms/aim/mainPage/export' // 导出

    },
    // 群组
    cluster: {
      list: '/tbms/aim/group/page', // 列表
      listPage: '/tbms/aim/group/page2', // 列表
      delete: '/tbms/aim/group/batchDelete', // 删除
      edit: '/tbms/aim/group/edit', // 编辑
      fileImport: '/tbms/aim/group/fileImport', // 导入
      export: '/tbms/aim/group/export'

    },
    // 社群
    group: {
      fileImport: '/tbms/aim/association/fileImport', // 导入
      export: '/tbms/aim/association/export'
    },
    // 文章
    post: { export: '/tbms/aim/post/export' },
    // 话题
    topic: {
      list: '/tbms/aim/topic/page', // 列表
      delete: '/tbms/aim/topic/batchDelete', // 删除
      edit: '/tbms/aim/topic/edit', // 编辑
      fileImport: '/tbms/aim/topic/fileImport', // 导入

      export: '/tbms/aim/topic/export'

    },
    getCreator: '/tbms/aimBase/aggs',
    listConfig: '/permission_proxy/param/tbms/listConfig'

  },
  // 素材库管理
  material: {
    // 文章
    post: { export: '/tbms/material/post/export' },
    // 评论
    comment: { export: '/tbms/material/comment/export' },
    // 创始人集合
    getCreator: '/tbms/materialBase/creator'
  },

  // 分系统运行状态
  subsystemStatus: {
    list: '/permission_proxy/gateway/subsystem',
    serviceList: '/permission_proxy/gateway/service',
    service_monitor: '/clouds/server/monitor'
  },

  // 监控账号
  monitor: {
    list: '/permission_proxy/monitor/monitor/listPage',
    account: '/permission_proxy/monitor/account',
    addAccounts: '/permission_proxy/monitor_user/add',
    addMonitorMission: '/permission_proxy/monitor/account', // 添加监控任务
    addMonitor: '/permission_proxy/monitor/monitor/add', // 添加监控账号
    removeAccounts: '/permission_proxy/monitor/gather/delete', // 移除监控
    editGroup: '/permission_proxy/monitor/group/edit',
    deleteGroup: '/permission_proxy/monitor/group/del',
    groupList: '/permission_proxy/monitor/group/list',
    deleteGroupStrictly: '/permission_proxy/monitor_user/group_remove',
    monitorList: '/permission_proxy/support_monitor_user',
    MonitorAction: '/gather-monitor/v2/action', // 监控账号的操作
    monitorMissionAction: '/permission_proxy/monitor/gather/taskStatus', // 监控账号任务的操作
    supportMonitorList: '/permission_proxy/gatherweb/user/support_monitor_user',
    monitorPersonInfo: '/permission_proxy/gatherweb/user/resource_user',
    personRelationship: '/permission_proxy/gatherweb/user/pageRelation', // 分页获取用户关系
    userCountRelationships: '/permission_proxy/gatherweb/user/countRelationship', // 获取账号好友关系，统计的总数
    extraInfo: '/permission_proxy/gatherweb/user/extraInfo',
    pageInteractAction: '/permission_proxy/gatherweb/user/pageInteractAction',
    groupTreeList: '/gather-monitor/v2/group/list', // 获取监控账号分组树
    addGroupTree: '/gather-monitor/v2/group/add', // 添加监控账号分组树
    deleteGroupTree: '/gather-monitor/v2/group/delete', // 删除监控账号分组树
    updateGroupTree: '/gather-monitor/v2/group/update', // 修改监控账号分组树
    getArea: '/gather-monitor/v2/target/area', // 获取账号地区分布
    getMonitor: '/gather-monitor/v2/target/list', // 获取监控账号列表
    delMontior: '/gather-monitor/v2/target/delete', // 新的移除监控
    addNewMontior: '/gather-monitor/v2/target/addMonitor', // 新的添加监控
    logList: '/gather-monitor/v2/callback/search' // 监控进度

  },

  // 系统测试
  systemTest: {
    // 验证码分析
    captchaAnalysis: {
      normal: '/permission_proxy/api/v1/predict', // 普通图片验证码识别
      localNormal: '/permission_proxy/api/v1/normalV2', // 普通图片验证码识别
      result: '/captcha/api/v1/result', // 获取分析结果
      netCaptcha: '/system/captcha', // 在线生成验证码（本系统）
      googleCaptcha: '/captcha/api/v1/re_captcha/v2',
      geetestCaptcha: '/captcha/api/v1/geetest',
      geetest: '/permission_proxy/misc/geetest',
      geetestRequestUrl: 'https://www.geetest.com/demo/gt/validate-test', // geet在线测试 请求地址
      geetestUrl: 'https://www.geetest.com/demo/test.html', // geet 测试网址
      geetestOnlineTestUrl: 'https://reqbin.com/', // geet 在线模拟请求网址
      weibo: 'https://login.sina.com.cn/cgi/pin.php', // 新浪验证码地址
      google: 'https://www.google.com/recaptcha/api2/demo' // 谷歌验证地址
    },
    // 智能交互对话
    intelligent: {
      chat: '/permission_proxy/v1/generate/chat',
      // chatCN: '/permission_proxy/v1/generate/chat_cn',
      // resetEn: '/permission_proxy/en/v1/generate/reset',
      // resetZh: '/permission_proxy/zh/v1/generate/reset',
      reset: '/permission_proxy/v1/generate/reset',
      chatV2: '/intelligent/ai/conversation'
    },
    onlineTranslation: {
      getLang: '/permission_proxy/gather-translate/getLang',
      translate: '/permission_proxy/gather-translate/translate'
    },
    tagsMatching: '/intelligent/ai/label-match',
    // 文章洗稿
    articleClean: {
      commit: '/intelligent/ai/wash-text',
      aiWashText: '/intelligent/task/ai-wash-text',
      queryTask: '/intelligent/task/query-task'

    }

  },
  // 开关
  switch: { list: '/task/codePlatform/getCommonCode' },

  // 批量下载音视图
  batchDownload: {
    singleRequest: '/fldl/download/startDownloadSingleFile',
    request: '/fldl/download/startDownloadFile',
    get_file: (date, uuid, name) => `/fldl/download/get/${date}/${uuid}/${name}`
  },

  /* 获取下载的临时key */
  downloadKey: {
    getSignKey: '/fldl/download/getKey',
    refreshKey: '/fldl/download/flushKey',
    getBucketAndRegion: '/fldl/download/getDefinition'
  },

  tagManager: {
    tagCategory: '/dcms/tag-type',
    getTagCategoriesList: '/dcms/tag-type/list',
    tag: '/dcms/all-tag',
    getTagList: '/dcms/all-tag/list'
  },

  // 操作日志
  operationLog: { virtualLog: '/dcms/account/record/import/list' },

  // 桌面
  desktop: {
    addDesktop: '/dcms/collection',
    getDesktopList: '/dcms/collection/list',
    patchDesktop: id => `/dcms/collection/${id}`,
    delDesktop: '/dcms/collection'
  },

  // 矩阵概览
  matrix: {
    availableAccountPlatforms: '/dcms/dashboard/accountCount/platform', // 可用账号平台
    availableAccountAera: '/dcms/dashboard/accountCount/area', // 可用账号地区分布
    centralAccountArea: '/dc-statistics/account/v/count', // 中心账号地区分布
    matrixDistribution: '/dc-statistics/account/label', // 矩阵分布
    accountActions: '/dc-statistics/account/action', // 账号影响力，账号行为
    mainAccountList: '/dcms/dashboard/accountCount/list' // 主要账号列表
  },

  // BT 账号消息提示
  btMsgCenter: {
    // 私信相关
    addUserMonitor: '/gather-reply/userAttention/add',
    cancelUserMonitor: '/gather-reply/directMessages/delete',
    chatUserList: '/gather-reply/directMessages/pageAttention',
    chatUserResourceList: '/gather-reply/directMessages/pageMutual',
    chatList: '/gather-reply/directMessages/pageMessages',
    createReplyTask: '/gather-reply/replyTask/create',
    getSmartReply: '/smart-reply/LLMs4chat/vcn_13b_text',
    queryUnreadMsg: '/gather-reply/userAttention/isUnreadMessage',
    setRitNoticeStatus2Read: '/gather-reply/attentionMutual/readMessage',
    platformList: '/gather-reply/directMessages/listPlatform',
    friendApplicationList: '/gather-reply/friendApplication/list',
    createFriendApplication: '/gather-reply/friendApplication/create',

    // 贴文相关
    getArticleList: '/gather-reply/post/page',
    article_platform_list: '/gather-reply/post/listPlatform',
    batchImportArticles: '/gather-reply/userAttention/batchAdd',
    getCommentList: '/gather-reply/post/pageTreeComment',
    getCommentList2: '/gather-reply/post/pageComment',
    updateStatus: '/gather-reply/userAttention/updateStatus',
    deleteMessage: '/gather-reply/userAttention/deleteMessage',

    // 私信和贴文的共同接口
    retryTask: '/gather-reply/replyTask/retry', // 重试任务

    // 获取素材库图片列表
    imageList: '/tbms/material/file/page'
  },
  // 搜索
  search: { searchList: '/permission_proxy/resourceSearch/searchByKeyword' },
  // 平台筛选聚合
  platform: { plarformList: '/permission_proxy/param/platform/groupDistinct' },
  modelCompletion: { list: '/permission_proxy/modelCompletion' },
  // 同屏接口
  sameScreenConnection: {
    getllms: '/intelligent/generate/getllms', // 获取大模型
    llmQa: '/intelligent/generate/llm-qa', // qa交互接口
    getTask: '/intelligent/generate/queryTask',
    getRelationList: '/permission_proxy/gatherweb/user/relationTopologyDiagram',
    associationSingleSave: '/tbms/aim/association/singleSave', // 单个新增社群至目标库
    mainPageSingleSave: '/tbms/aim/mainPage/singleSave', // 单个新增主页至目标库
    informationAdd: '/permission_proxy/gatherweb/common/information/add', // 单个新增账号
    googleHostList: '/permission_proxy/gatherweb/common/sameScreenHot/list', // google趋势
    getFriendLineData: '/dcms-migration/accountHis/getAccountHisList' // 好友发展趋势
  }
};
