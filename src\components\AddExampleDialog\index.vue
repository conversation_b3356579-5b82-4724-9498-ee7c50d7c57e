<template>
  <titan-dialog
    :title="title"
    :visible.sync="visibleDialog"
    width="24%"
    :config="{
      showConfirm: true,
      confirmText: $t('components.立即添加'),
      cancelText: $t('components.取消'),
      confirmLoading,
      confirmDisabled: confirmDisabled,
      footerAlign: 'center'
    }"
    @confirm="handleAdd"
    @open="init"
    @close="reset"
  >
    <titan-form
      ref="form"
      :model="form"
      :column="column"
      label-positon="left"
      label-width="120px"
      :rules="rules"
    />
  </titan-dialog>
</template>

<script>
import { clearCloneForm, isEmpty } from '@/utils';
import { mapState } from 'vuex';
import { ExampleAPI, NetAccountAPI } from '@/api/modules/net-resource';
import { PlatformMap } from '@/assets/libs/enum';
import { MonitorAPI } from '@/api/modules/monitor';
import { CharacterAPI } from '@/api/modules/character';
import country from '@/assets/libs/country';
import i18n from '@/lang';

function recursionList(list, dict) {
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    dict[item.id] = item.name;
    if (!_.isEmpty(item.children)) {
      recursionList(item.children, dict);
    } else if (item.level < 2) {
      item.disabled = true;
    }
  }
}
export default {
  name: 'AddExampleDialog',
  props: {
    title: {
      type: String,
      default: () => i18n.t('components.新增榜样用户')
    },
    visible: {
      type: Boolean,
      default: false
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    clickNode: {
      type: Object,
      default: () => {}
    },
    isManualAdd: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      examplePlatformList: [],
      areaList: [],
      confirmLoading: false,
      form: {
        id: '',
        platform: '',
        exampleType: '',
        areaId: ''
      },
      exampleTypeList: [],
      exampleTypeDict: {}, // exampleTypeList的映射
      rules: { exampleType: [{ required: true, message: this.$t('components["榜样用户类别"]'), trigger: 'change' }] }
    };
  },
  computed: {
    visibleDialog: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    confirmDisabled() {
      return !this.form.exampleType;
    },
    column() {
      return [
        {
          prop: 'exampleType',
          label: this.$t('components["榜样用户类别"]'),
          render: (h, form) => {
            const cascaderProps = {
              options: this.exampleTypeList,
              props: {
                expandTrigger: 'click',
                label: 'name',
                value: 'id',
                children: 'children'
              }
            };
            return (
              <el-cascader
                clearable
                emitPath={false}
                show-all-levels={false}
                disabled={this.clickNode?.level === 2}
                v-model={form.exampleType}
                {...{ props: cascaderProps }}
              />
            );
          }
        },
        {
          label: this.$t('components["所属平台"]'),
          prop: 'platform',
          rules: [
            {
              required: this.isManualAdd,
              message: this.$t('components["请选择平台"]'),
              trigger: 'change'
            }
          ],
          render: (h, form) => (
            <titan-select
                v-model={form.platform}
                placeholder={this.$t('components["选择账号平台"]')}
                config={{
                  dataSource: this.examplePlatformList,
                  itemLabel: data => PlatformMap[data] || data
                }}
              ></titan-select>
          )
        },
        {
          label: this.$t('components["账号ID"]'),
          prop: 'id',
          rules: [
            {
              required: this.isManualAdd,
              message: this.$t('login.请输入账号') + 'ID',
              trigger: 'blur'
            }
          ],
          render: (h, form) => (
            <div>
              <titan-input v-model={form.id} disabled={!this.isManualAdd} clearable />
              <p class='tip-2'>*{this.$t('components["账号ID为平台认证的唯一值，请确保填写正确"]')}</p>
            </div>
          )
        },
        {
          label: this.$t('operationCenter["地区"]'),
          prop: 'areaId',
          show: () => this.isManualAdd,
          rules: [
            {
              required: this.isManualAdd,
              message: this.$t('accountCenter["请选择地区"]'),
              trigger: 'change'
            }
          ],
          render: (h, form) => {
            const options = this.areaList.map(item => (
              <el-option key={item} label={country[item]} value={item} />
            ));
            return (
              <titan-select
                v-model={form.areaId}
                placeholder={this.$t('accountCenter["请选择地区"]')}
              >
                { options }
              </titan-select>
            );
          }
        }
      ];
    },
    ...mapState({ user: state => state.user })
  },
  // watch: {
  //   clickNode: {
  //     deep: true,
  //     immediate: true,
  //     handler() {
  //       this.exampleTypeList = this.clickNode.level !== 2 ? this.clickNode.children : this.exampleTypeList;
  //       this.exampleType = this.clickNode.level !== 2 ? '' : this.clickNode.id;
  //     }
  //   }
  // },
  async created() {
    this.getAreaList();
    this.fetchPlatformList();
    this.init();
    const { data } = await ExampleAPI.getTypeList();
    const dict = {};
    recursionList(data, dict);
    if (this.clickNode?.children) {
      recursionList(this.clickNode.children, dict);
    }
    this.exampleTypeList = (this.clickNode && this.clickNode?.level !== 2) ? this.clickNode?.children : data;
    this.exampleTypeDict = dict;
    this.form.exampleType = (this.clickNode && this.clickNode?.level !== 2) ? '' : this.clickNode?.id;
  },
  methods: {

    /**
     * 初始化表单数据
     */
    init() {
      Object.keys(this.form).forEach(key => {
        if (!isEmpty(this.userInfo[key])) {
          this.form[key] = this.userInfo[key];
        }
      });
      if (this.isManualAdd) {
        // 手动添加
        this.form.id = '';
      }
    },

    async getAreaList() {
      const { data } = await CharacterAPI.getCascaderArea('account');
      this.areaList = data.map(it => it.area_id) || [];
    },

    async fetchPlatformList() {
      const resp = await NetAccountAPI.getMonitorPlatformList();
      this.examplePlatformList = resp.data;
    },

    /**
     * 重置表单
     */
    reset() {
      this.form = {
        id: '',
        platform: '',
        exampleType: ''
      };
    },

    /**
     * 添加监控
     */
    handleAdd() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.confirmLoading = true;
            const exampleType = (this.clickNode && this.clickNode?.level === 2) ? this.form.exampleType : this.form.exampleType[this.form.exampleType.length - 1];
            const query = {
              pageSize: 40,
              pageIndex: 1,
              groupId: exampleType,
              userName: this.userInfo.userName,
              platform: this.form.platform,
              accountId: !this.isManualAdd ? this.userInfo.id : this.form.id
            };
            const { data } = await ExampleAPI.getUser(clearCloneForm(query));

            if (data.length > 0) {
              this.$message(this.$t('components["此账号已经被添加到该类别榜样用户"]'));
            } else {
              const query = [
                {
                  keyword: !this.isManualAdd ? this.userInfo.id : this.form.id,
                  platform: this.form.platform,
                  groupId: exampleType,
                  designatedArea: this.isManualAdd ? this.form.areaId : ''
                }
              ];

              const { code, data: { existAccountIds, failAccountIds, successAccounts } } = await MonitorAPI.addMonitor(clearCloneForm(query));

              this.visibleDialog = false;
              this.confirmLoading = false;
              if (code === 200) {
                const successCount = successAccounts.length;
                const failedCount = failAccountIds.length;
                const duplicateCount = existAccountIds.length;
                if (failedCount && !successCount && !duplicateCount) {
                  this.$message.error(this.$t('components["添加榜样用户失败"]'));
                } else if (successCount && !failedCount && !duplicateCount) {
                  this.$message.success(`${this.$t('components["成功添加榜样用户"]')}${successCount}${this.$t('components["个"]')}`);
                } else {
                  this.$message.warning(`${this.$t('components["成功添加榜样用户"]')}${successCount}${this.$t('components["个"]')}，${this.$t('components["添加失败"]')}${failedCount}${this.$t('components["个"]')}，${this.$t('components["重复添加"]')}${duplicateCount}${this.$t('components["个"]')}`);
                }

                this.$emit('add-success', this.userInfo.id);
                this.visibleDialog = false;
              } else {
                this.$message.error(this.$t('components["添加榜样用户失败"]'));
              }
            }
          } catch (err) {
            console.error(err);
          } finally {
            this.visibleDialog = false;
            this.confirmLoading = false;
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .tip-2 {
  margin-top: 5px;
  font-size: 14px;
  color: #ff274b;
}
::v-deep .el-form-item {
  margin-bottom: 30px;
  .el-cascader {
    width: 100%;
  }
}
</style>

