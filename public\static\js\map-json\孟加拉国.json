{"title": "Bangladesh", "version": "1.1.2", "type": "FeatureCollection", "copyright": "Copyright (c) 2015 Highsoft AS, Based on data from Natural Earth", "copyrightShort": "Natural Earth", "copyrightUrl": "http://www.naturalearthdata.com", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:EPSG:3106"}}, "hc-transform": {"default": {"crs": "+proj=tmerc +lat_0=0 +lon_0=90 +k=0.9996 +x_0=500000 +y_0=0 +a=6377276.345 +b=6356075.41314024 +towgs84=283.7,735.9,261.1,0,0,0,0 +units=m +no_defs", "scale": 0.00107666711844, "jsonres": 15.5, "jsonmarginX": -999, "jsonmarginY": 9851.0, "xoffset": 300069.637776, "yoffset": 2944834.07994}}, "features": [{"type": "Feature", "id": "BD.DA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.6, "hc-middle-y": 0.44, "hc-key": "bd-da", "hc-a2": "DA", "labelrank": "7", "hasc": "BD.DA", "alt-name": "Daca|Dacca", "woe-id": "2344791", "subregion": null, "fips": "BG81", "postal-code": "DA", "name": "Dhaka", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "90.4053", "woe-name": "Dhaka", "latitude": "24.1653", "woe-label": "Dhaka, BD, Bangladesh", "type": "Bibhag"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[3262, 3797], [3328, 3749], [3343, 3708], [3246, 3767], [3206, 3731], [3142, 3763], [3070, 3844], [3119, 3861], [3262, 3797]]], [[[3538, 4177], [3493, 4169], [3538, 4128], [3527, 4090], [3478, 4109], [3370, 4065], [3344, 4138], [3378, 4233], [3421, 4253], [3384, 4271], [3294, 4241], [3274, 4192], [3219, 4192], [3153, 4227], [3191, 4180], [3311, 4162], [3325, 4147], [3329, 3982], [3283, 3862], [3226, 3886], [2892, 3950], [2769, 4012], [2783, 3971], [3005, 3838], [3073, 3752], [3202, 3708], [3313, 3696], [3378, 3608], [3392, 3512], [3356, 3557], [3372, 3412], [3366, 3368], [3309, 3291], [3297, 3355], [3259, 3311], [3203, 3290], [3118, 3284], [3112, 3241], [3064, 3196], [2957, 3269], [2902, 3282], [2842, 3229], [2885, 3230], [2900, 3188], [2854, 3115], [2812, 3100], [2734, 3166], [2721, 3243], [2671, 3281], [2587, 3265], [2559, 3202], [2527, 3185], [2441, 3091], [2375, 2980], [2310, 2926], [2220, 2909], [2150, 2946], [2049, 3037], [2032, 3088], [1927, 3201], [1869, 3315], [1826, 3329], [1798, 3404], [1849, 3369], [1916, 3481], [1821, 3466], [1796, 3499], [1798, 3569], [1707, 3594], [1775, 3683], [1724, 3682], [1681, 3732], [1656, 3802], [1658, 3861], [1706, 3835], [1659, 4024], [1573, 4084], [1523, 4090], [1496, 4050], [1485, 4100], [1535, 4143], [1358, 4397], [1241, 4392], [1166, 4499], [1255, 4727], [1230, 4852], [1286, 4814], [1438, 4748], [1544, 4664], [1587, 4655], [1696, 4709], [1829, 4727], [1807, 4863], [1837, 4960], [2003, 5100], [2032, 5151], [2058, 5304], [2048, 5357], [2017, 5365], [2015, 5415], [1928, 5504], [1918, 5682], [1936, 5883], [1952, 5978], [1946, 6035], [1986, 6124], [1978, 6183], [1999, 6287], [1990, 6360], [1934, 6426], [1838, 6490], [1801, 6572], [1758, 6787], [1768, 6942], [1807, 7139], [1844, 7219], [1905, 7303], [1915, 7350], [1900, 7450], [1953, 7568], [2005, 7589], [1998, 7536], [2019, 7407], [2064, 7366], [2124, 7390], [2188, 7393], [2561, 7235], [2825, 7177], [2956, 7122], [3015, 7120], [3185, 7158], [3323, 7146], [3414, 7163], [3532, 7130], [3583, 7161], [3678, 7117], [3725, 7111], [3915, 7138], [3910, 7059], [3936, 6978], [3879, 6810], [3990, 6629], [4039, 6608], [4239, 6630], [4274, 6509], [4281, 6427], [4270, 6330], [4281, 6297], [4372, 6296], [4407, 6251], [4381, 6161], [4342, 6141], [4373, 6081], [4423, 6046], [4351, 5898], [4416, 5865], [4424, 5799], [4396, 5779], [4456, 5706], [4417, 5665], [4440, 5628], [4347, 5576], [4296, 5478], [4197, 5440], [4196, 5386], [4149, 5406], [4093, 5328], [4044, 5309], [4050, 5264], [4101, 5175], [4089, 5136], [4009, 5062], [3994, 4937], [3960, 4860], [3895, 4770], [3856, 4742], [3786, 4779], [3688, 4764], [3627, 4716], [3634, 4634], [3576, 4474], [3617, 4436], [3592, 4370], [3649, 4294], [3603, 4280], [3608, 4243], [3538, 4177]]]]}}, {"type": "Feature", "id": "BD.KH", "properties": {"hc-group": "admin1", "hc-middle-x": 0.5, "hc-middle-y": 0.44, "hc-key": "bd-kh", "hc-a2": "KH", "labelrank": "7", "hasc": "BD.KH", "alt-name": null, "woe-id": "2344792", "subregion": null, "fips": "BG82", "postal-code": "KH", "name": "Khulna", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "89.3708", "woe-name": "Khulna", "latitude": "22.9179", "woe-label": "Khulna, BD, Bangladesh", "type": "Bibhag"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[866, 824], [845, 792], [793, 820], [706, 925], [728, 1014], [696, 1136], [718, 1154], [742, 1104], [758, 979], [777, 925], [857, 875], [866, 824]]], [[[2307, 2170], [2228, 2084], [2206, 2025], [2124, 2075], [2143, 1973], [2137, 1934], [2065, 1796], [2104, 1691], [2126, 1503], [2106, 1448], [2077, 1432], [1993, 1442], [2060, 1393], [2088, 1321], [2084, 1234], [2148, 1117], [2136, 1088], [2085, 1075], [2022, 1008], [1955, 983], [1939, 1019], [1869, 986], [1808, 995], [1758, 1024], [1743, 1078], [1762, 1111], [1734, 1164], [1685, 1062], [1738, 982], [1826, 905], [1770, 842], [1706, 867], [1644, 859], [1662, 783], [1629, 750], [1593, 773], [1544, 848], [1527, 957], [1532, 999], [1578, 1060], [1597, 1119], [1590, 1253], [1613, 1398], [1664, 1512], [1643, 1590], [1598, 1677], [1665, 1777], [1673, 1855], [1700, 1923], [1615, 1799], [1631, 1756], [1587, 1715], [1569, 1658], [1617, 1554], [1570, 1292], [1538, 1274], [1522, 1385], [1545, 1467], [1468, 1651], [1471, 1808], [1441, 1848], [1426, 1726], [1440, 1493], [1472, 1556], [1513, 1452], [1497, 1379], [1465, 1347], [1518, 1233], [1502, 1135], [1419, 1043], [1421, 899], [1381, 835], [1298, 780], [1254, 830], [1243, 905], [1249, 988], [1281, 1078], [1278, 1158], [1239, 1253], [1215, 1225], [1239, 1152], [1232, 1105], [1174, 1036], [1128, 952], [1107, 976], [1125, 857], [1088, 748], [1129, 710], [1080, 665], [1011, 647], [969, 700], [997, 768], [944, 920], [918, 954], [884, 912], [798, 966], [756, 1117], [776, 1263], [765, 1345], [719, 1427], [725, 1546], [756, 1585], [729, 1697], [655, 1796], [639, 1835], [664, 1852], [626, 1924], [606, 2022], [629, 2098], [576, 2283], [503, 2321], [541, 2346], [545, 2438], [508, 2525], [471, 2677], [521, 2753], [535, 2875], [468, 2909], [412, 3021], [363, 3091], [379, 3295], [402, 3342], [557, 3495], [556, 3538], [510, 3537], [361, 3591], [310, 3593], [252, 3567], [140, 3604], [100, 3661], [100, 3701], [157, 3802], [242, 4021], [224, 4079], [160, 4023], [135, 4074], [67, 4117], [-26, 4248], [-73, 4248], [-107, 4273], [-98, 4323], [-143, 4362], [-105, 4530], [-119, 4575], [-76, 4673], [-81, 4723], [-48, 4762], [54, 4767], [158, 4868], [159, 4940], [138, 4998], [179, 5060], [138, 5087], [93, 5160], [109, 5236], [101, 5292], [128, 5331], [192, 5357], [226, 5288], [316, 5231], [380, 5232], [490, 5283], [530, 5320], [608, 5291], [709, 5178], [757, 5089], [756, 5012], [842, 4919], [915, 4878], [961, 4869], [1102, 4883], [1230, 4852], [1255, 4727], [1166, 4499], [1241, 4392], [1358, 4397], [1535, 4143], [1485, 4100], [1496, 4050], [1523, 4090], [1573, 4084], [1659, 4024], [1706, 3835], [1658, 3861], [1656, 3802], [1681, 3732], [1724, 3682], [1775, 3683], [1707, 3594], [1798, 3569], [1796, 3499], [1821, 3466], [1916, 3481], [1849, 3369], [1798, 3404], [1826, 3329], [1869, 3315], [1927, 3201], [2032, 3088], [2049, 3037], [2150, 2946], [2220, 2909], [2277, 2830], [2231, 2811], [2222, 2749], [2190, 2709], [2220, 2674], [2159, 2624], [2155, 2591], [2199, 2586], [2221, 2542], [2174, 2528], [2223, 2439], [2261, 2409], [2271, 2319], [2249, 2256], [2284, 2226], [2307, 2170]]]]}}, {"type": "Feature", "id": "BD.BA", "properties": {"hc-group": "admin1", "hc-middle-x": 0.33, "hc-middle-y": 0.49, "hc-key": "bd-ba", "hc-a2": "BA", "labelrank": "7", "hasc": "BD.BA", "alt-name": "Bakerganj", "woe-id": "23706410", "subregion": null, "fips": "BG85", "postal-code": "BA", "name": "Barisal", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "90.23690000000001", "woe-name": "Barisal", "latitude": "22.4351", "woe-label": "Barisal, BD, Bangladesh", "type": "Bibhag"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[3360, 1184], [3343, 1142], [3317, 1151], [3275, 1074], [3241, 1047], [3211, 1063], [3259, 1075], [3222, 1114], [3249, 1199], [3365, 1279], [3388, 1191], [3360, 1184]]], [[[2915, 1024], [2892, 1018], [2898, 1148], [2952, 1259], [3021, 1303], [3015, 1262], [2972, 1189], [2931, 1044], [2915, 1024]]], [[[2938, 1251], [2960, 1344], [3009, 1391], [2999, 1345], [2950, 1289], [2938, 1251]]], [[[3087, 1132], [3036, 1130], [3022, 1189], [3052, 1258], [3052, 1356], [3080, 1391], [3158, 1310], [3180, 1260], [3163, 1201], [3087, 1132]]], [[[3398, 1607], [3364, 1493], [3351, 1545], [3306, 1347], [3270, 1304], [3219, 1302], [3210, 1348], [3280, 1417], [3269, 1430], [3175, 1367], [3189, 1451], [3260, 1506], [3306, 1558], [3398, 1607]]], [[[3983, 1691], [3989, 1574], [3976, 1531], [3942, 1522], [3926, 1602], [3951, 1699], [3983, 1691]]], [[[4042, 1668], [4022, 1650], [4032, 1786], [4056, 1775], [4042, 1668]]], [[[4044, 1827], [3981, 1726], [3954, 1736], [3987, 1820], [3993, 1903], [4020, 1978], [4050, 1903], [4044, 1827]]], [[[3430, 2101], [3474, 2005], [3443, 1980], [3454, 1906], [3406, 2088], [3375, 2096], [3386, 2136], [3430, 2101]]], [[[3465, 2100], [3454, 2089], [3464, 2197], [3488, 2115], [3465, 2100]]], [[[3767, 1587], [3676, 1445], [3646, 1419], [3634, 1445], [3578, 1394], [3534, 1317], [3458, 1293], [3444, 1334], [3516, 1494], [3420, 1354], [3393, 1348], [3389, 1437], [3432, 1531], [3413, 1572], [3429, 1640], [3500, 1796], [3514, 1862], [3514, 1970], [3524, 2002], [3473, 2273], [3453, 2317], [3328, 2410], [3301, 2417], [3296, 2550], [3334, 2702], [3364, 2736], [3457, 2759], [3493, 2639], [3569, 2484], [3624, 2407], [3637, 2318], [3659, 2261], [3775, 2152], [3811, 2109], [3777, 1933], [3781, 1635], [3767, 1587]]], [[[3381, 2924], [3447, 2873], [3504, 2797], [3354, 2807], [3253, 2724], [3258, 2649], [3235, 2618], [3187, 2694], [3123, 2720], [3140, 2759], [3140, 2876], [3176, 2911], [3263, 2904], [3274, 2930], [3381, 2924]]], [[[3446, 3105], [3484, 3065], [3426, 3064], [3484, 3000], [3431, 3006], [3349, 3081], [3322, 3064], [3239, 3064], [3211, 3106], [3245, 3165], [3302, 3211], [3345, 3215], [3446, 3105]]], [[[3159, 3173], [3145, 3164], [3121, 3266], [3215, 3278], [3224, 3246], [3159, 3173]]], [[[3112, 3241], [3143, 3143], [3210, 3043], [3216, 2999], [3267, 2959], [3232, 2929], [3158, 2936], [3125, 2895], [3089, 2782], [3041, 2770], [3069, 2747], [3039, 2712], [3018, 2734], [2981, 2709], [3088, 2657], [3133, 2686], [3182, 2633], [3215, 2497], [3222, 2433], [3200, 2397], [3130, 2373], [3101, 2242], [3136, 2265], [3132, 2321], [3178, 2381], [3241, 2380], [3303, 2342], [3334, 2289], [3384, 2164], [3349, 2178], [3349, 2136], [3396, 2037], [3406, 1961], [3397, 1885], [3342, 1744], [3302, 1679], [3204, 1629], [3183, 1559], [3134, 1473], [3097, 1443], [3009, 1492], [3061, 1626], [3068, 1670], [3044, 1747], [3049, 1828], [3028, 1797], [3015, 1688], [3053, 1631], [2996, 1568], [2972, 1452], [2914, 1373], [2874, 1273], [2843, 1099], [2812, 1039], [2718, 957], [2661, 934], [2503, 988], [2507, 1042], [2560, 1119], [2543, 1153], [2512, 1077], [2458, 1031], [2384, 1047], [2401, 1197], [2420, 1239], [2490, 1302], [2524, 1402], [2640, 1518], [2678, 1504], [2714, 1529], [2754, 1620], [2748, 1669], [2696, 1547], [2597, 1506], [2495, 1396], [2433, 1281], [2336, 1302], [2361, 1378], [2403, 1412], [2402, 1496], [2313, 1384], [2313, 1277], [2287, 1255], [2229, 1297], [2195, 1416], [2191, 1552], [2167, 1693], [2137, 1746], [2137, 1834], [2171, 1918], [2313, 2124], [2339, 2190], [2307, 2170], [2284, 2226], [2249, 2256], [2271, 2319], [2261, 2409], [2223, 2439], [2174, 2528], [2221, 2542], [2199, 2586], [2155, 2591], [2159, 2624], [2220, 2674], [2190, 2709], [2222, 2749], [2231, 2811], [2277, 2830], [2220, 2909], [2310, 2926], [2375, 2980], [2441, 3091], [2527, 3185], [2559, 3202], [2587, 3265], [2671, 3281], [2721, 3243], [2734, 3166], [2812, 3100], [2854, 3115], [2900, 3188], [2885, 3230], [2842, 3229], [2902, 3282], [2957, 3269], [3064, 3196], [3112, 3241]]]]}}, {"type": "Feature", "id": "BD.CG", "properties": {"hc-group": "admin1", "hc-middle-x": 0.75, "hc-middle-y": 0.51, "hc-key": "bd-cg", "hc-a2": "CG", "labelrank": "7", "hasc": "BD.CG", "alt-name": "Chattagram|Parbattya Chattagram|Rangamati", "woe-id": "2344790", "subregion": null, "fips": "BG80", "postal-code": "CG", "name": "Chittagong", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "92.13330000000001", "woe-name": "Chittagong", "latitude": "22.4245", "woe-label": "Chittagong, BD, Bangladesh", "type": "Bibhag"}, "geometry": {"type": "MultiPolygon", "coordinates": [[[[5689, 796], [5753, 674], [5769, 602], [5748, 443], [5732, 411], [5617, 410], [5627, 372], [5588, 362], [5549, 446], [5576, 485], [5570, 623], [5533, 746], [5542, 814], [5573, 868], [5638, 817], [5623, 905], [5682, 918], [5689, 796]]], [[[5520, 832], [5490, 845], [5516, 998], [5502, 1093], [5520, 1149], [5561, 1195], [5594, 1100], [5592, 1045], [5520, 832]]], [[[4206, 1495], [4173, 1474], [4102, 1483], [4072, 1523], [4122, 1582], [4164, 1663], [4179, 1753], [4189, 2026], [4219, 2031], [4316, 1969], [4356, 1900], [4383, 1730], [4307, 1579], [4206, 1495]]], [[[4333, 2142], [4254, 2161], [4271, 2212], [4402, 2261], [4421, 2225], [4404, 2182], [4333, 2142]]], [[[5014, 2058], [4932, 2047], [4878, 2077], [4826, 2151], [4786, 2284], [4824, 2378], [4860, 2397], [5014, 2199], [5028, 2120], [5014, 2058]]], [[[4563, 4993], [4505, 4984], [4465, 4939], [4408, 4836], [4403, 4785], [4429, 4719], [4374, 4553], [4284, 4524], [4276, 4448], [4331, 4423], [4328, 4365], [4282, 4364], [4284, 4287], [4335, 4195], [4379, 4072], [4434, 4024], [4488, 3848], [4532, 3826], [4502, 3767], [4522, 3719], [4564, 3422], [4587, 3346], [4628, 3291], [4686, 3276], [4692, 3337], [4658, 3491], [4657, 3574], [4684, 3644], [4755, 3620], [4813, 3527], [4842, 3502], [4913, 3244], [4938, 3227], [4941, 3173], [4971, 3129], [5040, 3106], [5056, 3061], [5108, 3124], [5160, 3121], [5241, 3144], [5315, 3212], [5344, 3281], [5379, 3288], [5411, 3333], [5382, 3411], [5355, 3538], [5329, 3596], [5319, 3670], [5350, 3761], [5398, 3848], [5469, 3933], [5563, 3975], [5612, 4020], [5650, 4086], [5651, 4159], [5604, 4372], [5613, 4507], [5637, 4508], [5753, 4369], [5779, 4356], [5848, 4392], [5929, 4502], [6001, 4528], [6036, 4477], [6077, 4375], [6103, 4400], [6123, 4486], [6186, 4485], [6172, 4366], [6178, 4305], [6247, 4095], [6258, 4005], [6298, 3942], [6320, 3862], [6351, 3819], [6365, 3716], [6319, 3571], [6319, 3497], [6366, 3236], [6367, 3108], [6381, 3046], [6414, 2998], [6497, 2945], [6527, 2745], [6594, 2679], [6621, 2622], [6636, 2480], [6634, 2373], [6667, 2253], [6777, 1651], [6775, 1604], [6710, 1576], [6751, 1452], [6808, 1186], [6814, 1130], [6788, 847], [6821, 407], [6846, 268], [6918, 44], [6870, -22], [6794, -53], [6773, -30], [6746, 83], [6711, 157], [6536, 158], [6462, 196], [6438, 290], [6359, 337], [6294, 257], [6211, 252], [6169, 137], [6122, 67], [6113, -2], [6127, -75], [6111, -195], [6135, -253], [6176, -291], [6198, -346], [6270, -405], [6258, -553], [6286, -696], [6337, -771], [6402, -965], [6383, -999], [6340, -956], [6299, -861], [6177, -647], [6146, -527], [6043, -433], [5977, -326], [5899, -237], [5883, -168], [5896, -48], [5883, 31], [5785, 204], [5732, 259], [5714, 322], [5738, 311], [5771, 379], [5809, 408], [5781, 514], [5836, 584], [5858, 712], [5835, 727], [5798, 677], [5755, 699], [5768, 757], [5731, 750], [5708, 798], [5737, 871], [5735, 915], [5776, 945], [5636, 918], [5619, 933], [5639, 1017], [5670, 1033], [5670, 1098], [5646, 1068], [5614, 1220], [5583, 1265], [5582, 1334], [5536, 1523], [5532, 1586], [5509, 1537], [5473, 1608], [5459, 1699], [5506, 1776], [5504, 1807], [5458, 1840], [5452, 1886], [5563, 1942], [5620, 2107], [5585, 2107], [5568, 1993], [5512, 1940], [5434, 1896], [5427, 1848], [5483, 1815], [5482, 1779], [5416, 1759], [5389, 1800], [5357, 2011], [5337, 2078], [5219, 2305], [4996, 2609], [4911, 2658], [4837, 2764], [4893, 2941], [4893, 2985], [4847, 2854], [4769, 2791], [4766, 2705], [4715, 2736], [4649, 2743], [4690, 2702], [4643, 2678], [4554, 2675], [4626, 2642], [4600, 2599], [4476, 2601], [4473, 2641], [4368, 2652], [4398, 2601], [4346, 2575], [4491, 2531], [4511, 2494], [4472, 2373], [4406, 2313], [4277, 2282], [4211, 2314], [4177, 2364], [4158, 2443], [4088, 2569], [4126, 2430], [4088, 2378], [3980, 2370], [3900, 2448], [3858, 2515], [3845, 2567], [3775, 2571], [3747, 2647], [3704, 2675], [3668, 2742], [3626, 2887], [3584, 2978], [3570, 3042], [3437, 3221], [3414, 3311], [3445, 3541], [3481, 3646], [3453, 3706], [3457, 3761], [3414, 3745], [3385, 3784], [3364, 3874], [3365, 3982], [3380, 4024], [3421, 4050], [3521, 4050], [3561, 4073], [3562, 4141], [3538, 4177], [3608, 4243], [3603, 4280], [3649, 4294], [3592, 4370], [3617, 4436], [3576, 4474], [3634, 4634], [3627, 4716], [3688, 4764], [3786, 4779], [3856, 4742], [3895, 4770], [3960, 4860], [3994, 4937], [4009, 5062], [4089, 5136], [4101, 5175], [4050, 5264], [4044, 5309], [4093, 5328], [4149, 5406], [4196, 5386], [4197, 5440], [4296, 5478], [4436, 5407], [4479, 5399], [4535, 5447], [4599, 5441], [4524, 5386], [4523, 5360], [4575, 5338], [4503, 5305], [4528, 5145], [4570, 5052], [4563, 4993]]]]}}, {"type": "Feature", "id": "BD.SY", "properties": {"hc-group": "admin1", "hc-middle-x": 0.47, "hc-middle-y": 0.41, "hc-key": "bd-sy", "hc-a2": "SY", "labelrank": "2", "hasc": "BD.SY", "alt-name": null, "woe-id": "23706411", "subregion": null, "fips": "BG86", "postal-code": "SY", "name": "<PERSON>yl<PERSON><PERSON>", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "91.7033", "woe-name": "<PERSON>yl<PERSON><PERSON>", "latitude": "24.6452", "woe-label": "Sylhet, BD, Bangladesh", "type": "Bibhag"}, "geometry": {"type": "Polygon", "coordinates": [[[4296, 5478], [4347, 5576], [4440, 5628], [4417, 5665], [4456, 5706], [4396, 5779], [4424, 5799], [4416, 5865], [4351, 5898], [4423, 6046], [4373, 6081], [4342, 6141], [4381, 6161], [4407, 6251], [4372, 6296], [4281, 6297], [4270, 6330], [4281, 6427], [4274, 6509], [4239, 6630], [4039, 6608], [3990, 6629], [3879, 6810], [3936, 6978], [3910, 7059], [3915, 7138], [3982, 7160], [4251, 7205], [4345, 7203], [4420, 7227], [4501, 7185], [4751, 7112], [4817, 7105], [4976, 7155], [5028, 7098], [5138, 7096], [5164, 7144], [5220, 7130], [5252, 7172], [5359, 7170], [5539, 7195], [5632, 7182], [5709, 7207], [5763, 7206], [5862, 7176], [5902, 7130], [6009, 7108], [6079, 7042], [6220, 7014], [6262, 6966], [6352, 6923], [6363, 6870], [6484, 6795], [6470, 6782], [6528, 6750], [6547, 6688], [6520, 6631], [6357, 6577], [6203, 6676], [6139, 6696], [6090, 6674], [6081, 6643], [6112, 6551], [6111, 6464], [6029, 6222], [6008, 6086], [5982, 6031], [5911, 5974], [5904, 5939], [5914, 5826], [5909, 5774], [5877, 5728], [5784, 5703], [5643, 5713], [5682, 5604], [5596, 5655], [5559, 5643], [5554, 5610], [5571, 5500], [5537, 5342], [5504, 5296], [5469, 5304], [5417, 5355], [5405, 5425], [5279, 5447], [5262, 5423], [5280, 5318], [5231, 5278], [5167, 5301], [5121, 5404], [5096, 5388], [5079, 5271], [5050, 5206], [4986, 5177], [4854, 5172], [4725, 5199], [4654, 5192], [4634, 5145], [4615, 4998], [4563, 4993], [4570, 5052], [4528, 5145], [4503, 5305], [4575, 5338], [4523, 5360], [4524, 5386], [4599, 5441], [4535, 5447], [4479, 5399], [4436, 5407], [4296, 5478]]]}}, {"type": "Feature", "id": "BD.RJ", "properties": {"hc-group": "admin1", "hc-middle-x": 0.65, "hc-middle-y": 0.46, "hc-key": "bd-rj", "hc-a2": "RJ", "labelrank": "7", "hasc": "BD.RJ", "alt-name": null, "woe-id": "2344793", "subregion": null, "fips": "BG83", "postal-code": "RJ", "name": "<PERSON><PERSON><PERSON>", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "89.04380000000001", "woe-name": "<PERSON><PERSON><PERSON>", "latitude": "24.6664", "woe-label": "Rajshahi, BD, Bangladesh", "type": "Bibhag"}, "geometry": {"type": "Polygon", "coordinates": [[[1807, 7139], [1768, 6942], [1758, 6787], [1801, 6572], [1838, 6490], [1934, 6426], [1990, 6360], [1999, 6287], [1978, 6183], [1986, 6124], [1946, 6035], [1952, 5978], [1936, 5883], [1918, 5682], [1928, 5504], [2015, 5415], [2017, 5365], [2048, 5357], [2058, 5304], [2032, 5151], [2003, 5100], [1837, 4960], [1807, 4863], [1829, 4727], [1696, 4709], [1587, 4655], [1544, 4664], [1438, 4748], [1286, 4814], [1230, 4852], [1230, 4852], [1102, 4883], [961, 4869], [915, 4878], [842, 4919], [756, 5012], [757, 5089], [709, 5178], [608, 5291], [530, 5320], [490, 5283], [380, 5232], [316, 5231], [226, 5288], [192, 5357], [224, 5418], [204, 5536], [166, 5589], [74, 5632], [30, 5551], [-8, 5549], [-91, 5578], [-239, 5593], [-364, 5694], [-805, 5933], [-854, 5944], [-895, 6020], [-894, 6116], [-939, 6191], [-999, 6213], [-989, 6269], [-952, 6334], [-943, 6415], [-903, 6426], [-883, 6490], [-850, 6499], [-793, 6578], [-835, 6712], [-751, 6756], [-687, 6763], [-621, 6676], [-620, 6643], [-501, 6618], [-395, 6760], [-368, 6851], [-308, 6954], [-292, 7075], [-296, 7179], [-279, 7210], [-221, 7219], [-81, 7173], [-13, 7213], [97, 7201], [197, 7162], [297, 7151], [353, 7216], [490, 7165], [533, 7161], [509, 7201], [540, 7207], [542, 7275], [620, 7341], [621, 7410], [968, 7402], [1038, 7384], [1043, 7314], [1085, 7220], [1138, 7166], [1251, 7230], [1352, 7206], [1512, 7153], [1678, 7152], [1807, 7139]]]}}, {"type": "Feature", "id": "BD.RP", "properties": {"hc-group": "admin1", "hc-middle-x": 0.56, "hc-middle-y": 0.59, "hc-key": "bd-rp", "hc-a2": "RP", "labelrank": "7", "hasc": "BD.RP", "alt-name": null, "woe-id": "-2344793", "subregion": null, "fips": "BG83", "postal-code": "RP", "name": "Rangpur", "country": "Bangladesh", "type-en": "Division", "region": null, "longitude": "89.112", "woe-name": null, "latitude": "25.7571", "woe-label": null, "type": "Bibhag"}, "geometry": {"type": "Polygon", "coordinates": [[[1807, 7139], [1678, 7152], [1512, 7153], [1352, 7206], [1251, 7230], [1138, 7166], [1085, 7220], [1043, 7314], [1038, 7384], [968, 7402], [621, 7410], [499, 7412], [461, 7450], [409, 7457], [358, 7517], [327, 7595], [356, 7661], [354, 7700], [283, 7781], [232, 7802], [233, 7769], [172, 7763], [124, 7716], [70, 7719], [-17, 7771], [-123, 7782], [-197, 7861], [-214, 7898], [-299, 7954], [-305, 8068], [-349, 8118], [-470, 8202], [-569, 8321], [-623, 8350], [-791, 8298], [-857, 8403], [-877, 8546], [-853, 8573], [-821, 8678], [-788, 8739], [-745, 8772], [-736, 8827], [-762, 8872], [-721, 8973], [-628, 9045], [-478, 9091], [-434, 9136], [-429, 9231], [-378, 9289], [-299, 9337], [-269, 9377], [-241, 9361], [-160, 9358], [-163, 9405], [-218, 9546], [-262, 9568], [-396, 9605], [-427, 9538], [-462, 9600], [-431, 9661], [-395, 9779], [-362, 9840], [-325, 9851], [-321, 9741], [-299, 9716], [-214, 9685], [-141, 9608], [-70, 9568], [-2, 9557], [32, 9501], [106, 9471], [99, 9427], [126, 9416], [133, 9370], [193, 9332], [182, 9275], [86, 9214], [115, 9192], [256, 9243], [316, 9219], [355, 9133], [410, 9130], [435, 9190], [469, 9213], [514, 9204], [587, 9130], [634, 9113], [742, 9155], [733, 9189], [683, 9208], [690, 9248], [635, 9253], [620, 9306], [531, 9345], [497, 9382], [510, 9447], [553, 9498], [595, 9507], [696, 9419], [750, 9402], [769, 9339], [836, 9316], [849, 9277], [791, 9278], [792, 9229], [848, 9113], [861, 9001], [894, 8944], [1004, 8895], [1046, 8856], [1070, 8803], [1128, 8776], [1168, 8729], [1259, 8689], [1326, 8723], [1351, 8685], [1499, 8685], [1537, 8623], [1589, 8636], [1606, 8734], [1679, 8795], [1681, 8850], [1636, 8846], [1620, 8876], [1682, 8916], [1624, 8934], [1613, 8957], [1653, 9006], [1699, 8996], [1686, 9083], [1734, 9110], [1773, 9077], [1772, 8987], [1853, 8978], [1925, 8847], [1925, 8769], [1991, 8670], [2032, 8685], [2051, 8629], [1995, 8598], [2065, 8566], [2058, 8522], [1986, 8395], [1979, 8349], [2010, 8183], [2048, 8090], [2065, 8017], [2049, 7887], [2046, 7748], [2005, 7589], [1953, 7568], [1900, 7450], [1915, 7350], [1905, 7303], [1844, 7219], [1807, 7139]]]}}]}