// 任务相关映射
import i18n from '@/lang'

/**
 * 任务策划 行为
 */
export const MissionActionCount = {
  // 创建虚拟账号
  createVirtualAccountSchema: i18n.t('assets["注册账号数量"]'),
  // 培育虚拟角色
  virtualAccountSchema: i18n.t('assets["培育账号数量"]'),
  // 舆论采集
  collectionSchema: i18n.t('assets["舆论采集数量"]'),
  // 数据管理
  dataManageSchema: i18n.t('assets["数据管理数量"]'),
  // 发帖
  postSchema: i18n.t('assets["发帖数量"]'),
  // 编辑贴文
  editPostSchema: i18n.t('assets["编辑贴文数量"]'),
  // 评论
  commentSchema: i18n.t('assets["评论数量"]'),
  // 点赞
  likeSchema: i18n.t('assets["点赞数量"]'),
  // 转发
  repostSchema: i18n.t('assets["转发数量"]'),
  // 关注
  followSchema: i18n.t('assets["关注数量"]'),
  // 加好友
  addFriendSchema: i18n.t('assets["加好友数量"]'),
  // 私信
  privateMessageSchema: i18n.t('assets["私信数量"]'),
  // 踩
  dislikeSchema: i18n.t('assets["踩数量"]'),
  // 分享
  shareSchema: i18n.t('assets["分享数量"]'),
  // 创建任务
  genericMissionSchema: i18n.t('assets["创建任务数量"]'),
  // 加群
  joinSchema: i18n.t('assets["加群数量"]'),
  // 回复
  replySchema: i18n.t('assets["回复数量"]'),
  // 邀请好友主页点赞
  publicPageInviteLikeSchema: i18n.t('assets["好友主页点赞数量"]'),
  // 举报
  reportSchema: i18n.t('assets["举报数量"]'),
  uploadAvatarSchema: i18n.t('assets["上传头像数量"]'),
  publicPageRepostSchema: i18n.t('assets["公共主页转发数量"]'),
  repostToGroupSchema: i18n.t('assets["转发至群组数量"]'),
  uploadBackgroundSchema: i18n.t('assets["上传背景数量"]'),

  // 访问
  visitSchema: i18n.t('assets["访问数量"]'),
  followPublicPageSchema: i18n.t('assets["关注数量"]'),
  deletePostSchema: i18n.t('assets["删帖数量"]'),
  voteSchema: i18n.t('assets["发起/参与投票数量"]'),
  deliverMessageSchema: i18n.t('assets["发消息数量"]'),

  // 收藏
  collectSchema: i18n.t('assets["收藏数量"]'),
  deleteFriendSchema: i18n.t('assets["删除好友数量"]'),
  topGroupMessageSchema: i18n.t('assets["置顶群内消息数量"]'),
  broadcastSchema: i18n.t('assets["发送广播数量"]'),
  viewFriendTrendSchema: i18n.t('assets["查看好友动态数量"]'),
  sendChannelMessageSchema: i18n.t('assets["发送频道消息数量"]'),
  exitGroupSchema: i18n.t('assets["退出社群/群组/频道数量"]'),
  newGroupSchema: i18n.t('assets["建立群组/频道数量"]'),
  newCommunitySchema: i18n.t('assets["建立社群数量"]'),
  likeMsgSchema: i18n.t('assets["点赞消息数量"]'),
  toTopSchema:  i18n.t('assets["置顶贴文数量"]'),
  cancelTopSchema: i18n.t('assets["取消置顶贴文数量"]'),
  generateLicenseCodeSchema:  i18n.t('assets["生成授权码数量"]'),
  publicPageAdminSchema:  i18n.t('assets["邀请用户成为公共主页管理员数量"]'),
};

//通过别名获取对应的 missionAction
export function $m_a(key) {
  const TaskActionAlias = {
    GATHER: 'collectionSchema', //舆论采集
    BREED: 'virtualAccountSchema', //虚拟账号培育
    REGISTER: 'createVirtualAccountSchema', //虚拟账号注册
    COMMENT: 'commentSchema', //评论
    POST: 'postSchema', //发帖
    EDIT_POST: 'editPostSchema', //编辑贴文
    LIKE: 'likeSchema', //点赞
    REPOST: 'repostSchema', //转发
    FOLLOW: 'followSchema', //关注
    ADD_FRIEND: 'addFriendSchema', //加好友
    DISLIKE: 'dislikeSchema', //点踩
    VISIT: 'visitSchema', //访问
    UPLOAD_AVATAR: 'uploadAvatarSchema',    //上传头像
    JOIN: 'joinSchema',   //加入社区/子版块
    SEND_MSG: 'privateMessageSchema', //私信
    RECV_MSG: 'replySchema',    //回复
    BYPASS: 'bypassSchema',   //通过好友
    CREATE_PUBLIC_PAGE: 'createPublicPageSchema', //创建主页
    MONITOR: 'monitorSchema',   //监控采集
    PUBLIC_PAGE_INVITE_LIKE: 'publicPageInviteLikeSchema',  // 邀请好友主页点赞
    REPORT: 'reportSchema', // 举报
    PUBLIC_PAGE_REPOST: 'publicPageRepostSchema',//公共主页转发数量
    REPOST_TO_GROUP: 'repostToGroupSchema',//转发至群组
    UPLOAD_BACKGROUND: 'uploadBackgroundSchema',    //上传背景图
    FOLLOW_PUBLIC_PAGE: 'followPublicPageSchema',    //关注公共主页
    DELETE_POST: 'deletePostSchema',    //删帖
    VOTE: 'voteSchema',    //删帖
    DELIVER_MESSAGE: 'deliverMessageSchema', //发消息
    COLLECT: 'collectSchema', //收藏
    DELETE_FRIEND: 'deleteFriendSchema', //删除好友
    TOP_GROUP_MESSAGE: 'topGroupMessageSchema', //置顶群内消息
    BROADCAST: 'broadcastSchema', //发送广播
    VIEW_FRIEND_TREND: 'viewFriendTrendSchema', //查看好友动态
    SEND_CHANNEL_MESSAGE: 'sendChannelMessageSchema', //发送频道消息
    EXIT_GROUP: 'exitGroupSchema', //退出社群/群组/频道
    NEW_GROUP: 'newGroupSchema', //建立群组/频道
    NEW_COMMUNITY: 'newCommunitySchema', //建立社群
    LIKE_MSG: 'likeMsgSchema', //点赞消息
    TO_TOP:'toTopSchema',
    CANCEL_TOP:"cancelTopSchema",
    GENERATE_LICENSE_CODE:"generateLicenseCodeSchema",
    PUBLIC_PAGE_ADMIN:"publicPageAdminSchema",
  };

  return TaskActionAlias[key];
}

//通过别名获取对应的 taskAction
export function $t_a(key) {
  const MissionActionAlias = {
    collectionSchema: 'GATHER', //舆论采集
    virtualAccountSchema: 'BREED', //虚拟账号培育
    createVirtualAccountSchema: 'REGISTER', //虚拟账号注册
    commentSchema: 'COMMENT', //评论
    postSchema: 'POST', //发帖
    editPostSchema: 'EDIT_POST', //编辑贴文
    likeSchema: 'LIKE', //点赞
    repostSchema: 'REPOST', //转发
    followSchema: 'FOLLOW', //关注
    addFriendSchema: 'ADD_FRIEND', //加好友
    dislikeSchema: 'DISLIKE', //点踩
    visitSchema: 'VISIT', //访问
    uploadAvatarSchema: 'UPLOAD_AVATAR',    //上传头像
    joinSchema: 'JOIN',   //加入社区/子版块
    privateMessageSchema: 'SEND_MSG', //私信
    replySchema: 'RECV_MSG',    //回复
    bypassSchema: 'BYPASS',   //通过好友
    createPublicPageSchema: ' CREATE_PUBLIC_PAGE', //创建主页
    monitorSchema: 'MONITOR',   //监控采集
    publicPageInviteLikeSchema: 'PUBLIC_PAGE_INVITE_LIKE',  // 邀请好友主页点赞
    reportSchema: 'REPORT',  // 举报
    publicPageRepostSchema: 'PUBLIC_PAGE_REPOST',//转发至公共主页
    repostToGroupSchema: 'REPOST_TO_GROUP',//转发至群组
    uploadBackgroundSchema: 'UPLOAD_BACKGROUND',//上传背景图
    followPublicPageSchema: 'FOLLOW_PUBLIC_PAGE',    //关注公共主页
    deletePostSchema: 'DELETE_POST',    //删贴
    voteSchema: 'VOTE',    //删贴
    deliverMessageSchema: 'DELIVER_MESSAGE',    //发消息
    collectSchema: 'COLLECT',    //收藏
    deleteFriendSchema: 'DELETE_FRIEND',    //删除好友
    topGroupMessageSchema: 'TOP_GROUP_MESSAGE',    //置顶群内消息
    broadcastSchema: 'BROADCAST',    //发送广播
    viewFriendTrendSchema: 'VIEW_FRIEND_TREND',    //查看好友动态
    sendChannelMessageSchema: 'SEND_CHANNEL_MESSAGE',    //发送频道消息
    exitGroupSchema: 'EXIT_GROUP',    //退出社群/群组/频道
    newGroupSchema: 'NEW_GROUP',    //建立群组/频道
    newCommunitySchema: 'NEW_COMMUNITY',    //建立社群
    likeMsgSchema: 'LIKE_MSG',    //点赞消息
    toTopSchema: 'TO_TOP',    //置顶贴文
    cancelTopSchema: 'CANCEL_TOP',    //取消置顶
    generateLicenseCodeSchema: 'GENERATE_LICENSE_CODE',    //生成广告授权码
    publicPageAdminSchema: 'PUBLIC_PAGE_ADMIN',    //邀请用户成为公共主页管理员

  };
  return MissionActionAlias[key];
}

/**
 * 行为
 */
export const PlatformActionList = [
  {
    label: i18n.t('assets["采集"]'),
    key: 'gather',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["虚拟账号培育"]'),
    key: 'breed',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["虚拟账号注册"]'),
    key: 'register',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["创建公共主页"]'),
    key: 'create_public_page',
    unit: i18n.t('assets["条"]'),
    available: true
  },
  {
    label: i18n.t('assets["公共主页发帖"]'),
    key: 'public_page_post',
    unit: i18n.t('assets["条"]'),
    available: true
  },
  {
    label: i18n.t('assets["访问"]'),
    key: 'visit',
    unit: i18n.t('assets["次"]'),
    available: true
  },
  {
    label: i18n.t('assets["上传头像"]'),
    key: 'upload_avatar',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["点赞"]'),
    key: 'like',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["点踩"]'),
    key: 'dislike',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["评论"]'),
    key: 'comment',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["加群"]'),
    key: 'join',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["发帖"]'),
    key: 'post',
    unit: i18n.t('assets["篇"]')
  },
  {
    label: i18n.t('assets["编辑贴文"]'),
    key: 'edit_post',
    unit: i18n.t('assets["篇"]')
  },
  {
    label: i18n.t('assets["转发"]'),
    key: 'repost',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["关注"]'),
    key: 'follow',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["加好友"]'),
    key: 'add_friend',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["通过关注或好友请求"]'),
    key: 'bypass',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["拒绝好友请求"]'),
    key: 'no_pass',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["发送私信"]'),
    key: 'send_msg',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["回复私信"]'),
    key: 'recv_msg',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["邀请好友点赞主页"]'),
    key: 'public_page_invite_like',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["解封"]'),
    key: 'unlock',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["公共主页转发"]'),
    key: 'public_page_repost',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["转发至群组"]'),
    key: 'repost_to_group',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["上传背景图"]'),
    key: 'upload_background',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["举报"]'),
    key: 'report',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["回关"]'),
    key: 'callback_follow',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["关注"]'),
    key: 'follow_public_page',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["删帖"]'),
    key: 'delete_post',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["账号状态检查"]'),
    key: 'request_review',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["更新个人信息"]'),
    key: 'update_profile',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["发起/参与投票"]'),
    key: 'vote',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["发消息"]'),
    key: 'deliver_message',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["采集账号基本信息"]'),
    key: 'update_account_info',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["收藏"]'),
    key: 'collect',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["删除好友"]'),
    key: 'delete_friend',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["置顶群内消息"]'),
    key: 'top_group_message',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["发送广播"]'),
    key: 'broadcast',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["查看好友动态"]'),
    key: 'view_friend_trend',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["发送频道消息"]'),
    key: 'send_channel_message',
    unit: i18n.t('assets["条"]')
  },
  {
    label: i18n.t('assets["退出社群/群组/频道"]'),
    key: 'exit_group',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["建立群组/频道"]'),
    key: 'new_group',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["建立社群"]'),
    key: 'new_community',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["点赞消息"]'),
    key: 'like_msg',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["开通专业版"]'),
    key: 'account_pro',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["置顶贴文"]'),
    key: 'to_top',
    unit: i18n.t('assets["篇"]')
  },
  {
    label: i18n.t('assets["取消置顶贴文"]'),
    key: 'cancel_top',
    unit: i18n.t('assets["篇"]')
  },
  {
    label: i18n.t('assets["生成授权码"]'),
    key: 'generate_license_code',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["邀请用户成为公共主页管理员"]'),
    key: 'public_page_admin',
    unit: i18n.t('assets["个"]')
  },
  {
    label: i18n.t('assets["同屏操作"]'),
    key: 'vnc',
    unit: i18n.t('assets["次"]')
  },
  {
    label: i18n.t('assets["转化账号"]'),
    key: 'convert_account',
    unit: i18n.t('assets["次"]')
  }

];

export const ActionMap = {};
PlatformActionList.forEach(action => {
  ActionMap[action.key] = action.label;
});

/**
 * 任务类型
 */
export const MissionTypeOptions = [
  {
    value: 'social',
    label: i18n.t('assets["虚拟账号注册/账号培育/社交行为"]')
  },
  {
    value: 'delivery',
    label: i18n.t('assets["投送行为"]')
  },
  {
    value: 'article',
    label: i18n.t('assets["文章采集"]')
  }
];

/**
 * 任务状态
 * code 为 task.status
 * fields 为前端使用的一些别名
 * dealAble 当前状态可以执行的操作，注意这个数组的顺序代表了优先级，对于某些只想做一个操作的展示，就只拿数组的第一项
 * value [deprecated] 老版本 当前任务运行的状态
 */
export const MissionStatusListMap = [
  {
    value: 0,
    code: 'CREATE',
    label: i18n.t('unit["待处理"]'),
    color: '#8294b1',
    bgColor: '#ebf2fa',
    borderColor: '#3569e7',
    fields: 'wait',
    dailyStatusText: i18n.t('unit["未执行"]'),
    dailyBtnType: 'primary',
    action: 'start',
    icon: 'fa-hourglass-start',
    taskIconSvg: 'create-taskIcon',
    dealAble: [
      { action: 'START', label: '启动' },
      { action: 'CLOSE_DOWN', label: '关闭' }
    ]
  },
  {
    value: 1,
    code: 'RUN',
    label: i18n.t('unit["进行中"]'),
    color: '#6589d1',
    bgColor: '#ebf2fa',
    borderColor: '#3569e7',
    fields: 'running',
    dailyStatusText: i18n.t('unit["运行中"]'),
    dailyBtnType: 'primary',
    action: 'pause',
    icon: 'fa-spinner',
    taskIconSvg: 'run-taskIcon',
    dealAble: [
      { action: 'PAUSE', label: '暂停' }
    ]
  },
  {
    value: 2,
    code: 'SUCCESS',
    label: i18n.t('unit["已完成"]'),
    color: '#0fd2be',
    bgColor: '#e2f3f0',
    borderColor: '#53cbbf',
    fields: 'complete',
    dailyStatusText: i18n.t('unit["运行中"]'),
    dailyBtnType: 'success',
    createStatusText: i18n.t('unit["已完成"]'),
    action: 'start',
    icon: 'fa-check',
    taskIconSvg: 'success-taskIcon',
    dealAble: [
      { action: 'CLOSE_DOWN', label: '关闭' }
    ]
  },
  {
    value: 3,
    code: 'FAILURE',
    label: i18n.t('unit["异常"]'),
    color: '#ff274b',
    bgColor: '#feeeee',
    borderColor: '#ff274b',
    fields: 'fail',
    dailyStatusText: i18n.t('unit["异常"]'),
    dailyBtnType: 'danger',
    action: 'start',
    icon: 'fa-exclamation-triangle',
    taskIconSvg: 'failure-taskIcon',
    dealAble: [
      { action: 'RETRY', label: '重试' },
      { action: 'CLOSE_DOWN', label: '关闭' }
    ]
  },
  {
    value: 4,
    code: 'PAUSED',
    label: i18n.t('unit["已暂停"]'),
    color: '#ffa600',
    bgColor: '#ffffff',
    borderColor: '#f7c531',
    fields: 'pause',
    dailyStatusText: i18n.t('unit["已暂停"]'),
    dailyBtnType: 'warning',
    action: 'resume',
    icon: 'fa-pause',
    taskIconSvg: 'paused-taskIcon',
    dealAble: [
      { action: 'RESUME', label: '继续' },
      { action: 'CLOSE_DOWN', label: '关闭' }
    ]
  },
  {
    value: 5,
    code: 'CLOSED',
    label: i18n.t('unit["已关闭"]'),
    color: '#b9bdc7',
    bgColor: '#ffffff',
    borderColor: '#aeaeae',
    fields: 'close',
    dailyStatusText: i18n.t('unit["已暂停"]'),
    dailyBtnType: 'info',
    createStatusText: '已暂停（任务参数已更改）',
    action: 'start',
    icon: 'fa-close',
    taskIconSvg: 'close-taskIcon',
  },
  {
    value: 6,
    code: 'PAUSING',
    label: i18n.t('unit["暂停中"]'),
    color: '#f7c531',
    bgColor: '#ffffff',
    borderColor: '#f7c531',
    fields: 'pausing',
    dailyStatusText: i18n.t('unit["暂停中"]'),
    dailyBtnType: 'info'
  },
  {
    value: 7,
    code: 'CLOSING',
    label: i18n.t('unit["关闭中"]'),
    color: '#ea5a73',
    bgColor: '#feeeee',
    borderColor: '#ff274b',
    fields: 'closing',
    dailyStatusText: i18n.t('unit["关闭中"]'),
    dailyBtnType: 'danger'
  },
  {
    value: 8,
    code: 'RESUMING',
    label: i18n.t('unit["恢复中"]'),
    color: '#3f539a',
    bgColor: '#ebf2fa',
    borderColor: '#3569e7',
    fields: 'resuming',
    dailyStatusText: i18n.t('unit["恢复中"]'),
    dailyBtnType: 'info'
  },
  {
    value: 9,
    code: 'STARTING',
    label: i18n.t('unit["等待中"]'),
    color: '#5a7acc',
    bgColor: '#ebf2fa',
    borderColor: '#3569e7',
    fields: 'queue',
    dailyStatusText: i18n.t('unit["等待中"]'),
    dailyBtnType: 'primary',
    action: 'pause'
  },
];

export const taskDealMap = {};  //状态对应的dealAble[0]
export const taskAllDealMap = {}; //状态对应的dealAble
export const MissionStatusMap = {};
export const MissionLabelMap = {};  //状态code对应的label
MissionStatusListMap.forEach(status => {
  taskDealMap[status.code] = status.dealAble ? status.dealAble[0] : undefined;
  taskAllDealMap[status.code] = status.dealAble;
  MissionStatusMap[status.fields] = status.code;
  MissionLabelMap[status.code] = status.label;
});

/**
 * 任务分解-社交行为
 */
export const MissionActionOptions = [
  // 创建虚拟账号
  { schema: 'createVirtualAccountSchema', label: i18n.t('assets["创建虚拟账号"]') },
  // 培育虚拟角色
  { schema: 'virtualAccountSchema', label: i18n.t('assets["培育虚拟角色"]') },
  // 舆论采集
  { schema: 'collectionSchema', label: i18n.t('assets["舆论采集"]') },
  // 监控采集
  { schema: 'monitorSchema', label: i18n.t('assets["监控采集"]') },
  // 发帖
  { schema: 'postSchema', label: i18n.t('assets["发帖"]'), type: 'action' },
  // 编辑贴文
  { schema: 'editPostSchema', label: i18n.t('assets["编辑贴文"]') },
  // 评论
  { schema: 'commentSchema', label: i18n.t('assets["评论"]'), type: 'action' },
  // 点赞
  { schema: 'likeSchema', label: i18n.t('assets["点赞"]'), type: 'action' },
  // 转发
  { schema: 'repostSchema', label: i18n.t('assets["转发"]'), type: 'action' },
  // 关注
  { schema: 'followSchema', label: i18n.t('assets["关注"]'), type: 'action' },
  // 加好友
  { schema: 'addFriendSchema', label: i18n.t('assets["加好友"]'), type: 'action' },
  // 私信
  { schema: 'privateMessageSchema', label: i18n.t('assets["私信"]'), type: 'action' },
  // 踩
  { schema: 'dislikeSchema', label: i18n.t('assets["踩"]'), type: 'action' },
  // 分享
  { schema: 'shareSchema', label: i18n.t('assets["分享"]'), type: 'action' },
  // 加群
  { schema: 'joinSchema', label: i18n.t('assets["加群"]'), type: 'action' },
  // 回复
  { schema: 'replySchema', label: i18n.t('assets["回复"]'), type: 'action' },
  // 邀请好友主页点赞
  { schema: 'publicPageInviteLikeSchema', label: i18n.t('assets["邀请好友点赞主页"]'), type: 'action' },
  // 举报
  { schema: 'reportSchema', label: i18n.t('assets["举报"]'), type: 'action' },
  { schema: 'createPublicPageSchema', label: i18n.t('assets["创建公共主页"]'), type: 'action' },
  { schema: 'uploadAvatarSchema', label: i18n.t('assets["上传头像"]'), type: 'action' },
  { schema: 'publicPageRepostSchema', label: i18n.t('assets["转发至公共主页"]'), type: 'action' },
  { schema: 'repostToGroupSchema', label: i18n.t('assets["转发至群组"]'), type: 'action' },
  { schema: 'uploadBackground', label: i18n.t('assets["上传背景图"]'), type: 'action' },
  { schema: 'visitSchema', label: i18n.t('assets["访问"]'), type: 'action' },
  { schema: 'followPublicPageSchema', label: i18n.t('assets["关注"]'), type: 'action' },
  { schema: 'deletePostSchema', label: i18n.t('assets["删帖"]'), type: 'action' },
  { schema: 'voteSchema', label: i18n.t('assets["发起/参与投票"]'), type: 'action' },
  { schema: 'deliverMessageSchema', label: i18n.t('assets["发消息"]'), type: 'action' },
  { schema: 'collectSchema', label: i18n.t('assets["收藏"]'), type: 'action' },
  { schema: 'deleteFriendSchema', label: i18n.t('assets["删除好友"]'), type: 'action' },
  { schema: 'topGroupMessageSchema', label: i18n.t('assets["置顶群内消息"]'), type: 'action' },
  { schema: 'broadcastSchema', label: i18n.t('assets["发送广播"]'), type: 'action' },
  { schema: 'viewFriendTrendSchema', label: i18n.t('assets["查看好友动态"]'), type: 'action' },
  { schema: 'sendChannelMessageSchema', label: i18n.t('assets["发送频道消息"]'), type: 'action' },
  { schema: 'exitGroupSchema', label: i18n.t('assets["退出社群/群组/频道"]'), type: 'action' },
  { schema: 'newGroupSchema', label: i18n.t('assets["建立群组/频道"]'), type: 'action' },
  { schema: 'newCommunitySchema', label: i18n.t('assets["建立社群"]'), type: 'action' },
  { schema: 'likeMsgSchema', label: i18n.t('assets["点赞消息"]'), type: 'action' },
  { schema: 'toTopSchema', label: i18n.t('assets["置顶贴文"]'), type: 'action' },
  { schema: 'cancelTopSchema', label: i18n.t('assets["取消置顶贴文"]'), type: 'action' },
  { schema: 'generateLicenseCodeSchema', label: i18n.t('assets["生成授权码"]'), type: 'action' },
  { schema: 'publicPageAdminSchema', label: i18n.t('assets["邀请用户成为公共主页管理员"]'), type: 'action' },
];
export const MissionActionMap = MissionActionOptions.filter(
  item => item.type === 'action'
).map(item => item.schema);

/**
 * 表单类型映射表
 */
export const SchemaNameMap = {};
MissionActionOptions.forEach(item => {
  SchemaNameMap[item.schema] = item.label;
});

/**
 * 任务执行时间选项
 */
export const TaskTypeOptions = [
  {
    prop: 'immediate',
    value: 0,
    label: i18n.t('assets["立即执行"]')
  },
  {
    prop: 'reservation',
    value: 1,
    label: i18n.t('assets["预约执行"]')
  },
  {
    prop: 'cycle',
    value: 2,
    label: i18n.t('assets["周期任务"]')
  }
];

/**
 * 任务执行时间对象
 */
export const TaskTypeMap = {};
export const TaskTypeLabelMap = {};
TaskTypeOptions.forEach(item => {
  TaskTypeMap[item.prop] = item.value;
  TaskTypeLabelMap[item.prop] = item.label;
});

export const SchemaFormNameMap = {
  // 创建虚拟账号
  REGISTER: 'create-account',
  // 培育虚拟角色
  BREED: 'breed-account',
  // 舆论采集
  GATHER: 'collection',
  // 发帖
  postSchema: 'post',
  // 评论
  commentSchema: 'comment',
  // 点赞
  likeSchema: 'like',
  // 转发
  repostSchema: 'repost',
  // 关注
  followSchema: 'follow',
  // 加好友
  addFriendSchema: 'add-friend',
  // 私信
  privateMessageSchema: 'private-message',
  // 踩
  dislikeSchema: 'dislike',
  // 加入群
  joinSchema: 'join',
  //回复
  replySchema: 'reply',
  //访问
  visitSchema: 'visit',
  //删帖
  deletePostSchema: "delete-post",
  //发起/参与投票
  voteSchema: 'vote',
  //发消息
  deliverMessageSchema: 'deliver-message',
  //收藏
  collectSchema: 'collect',
  /* 删除好友 */
  deleteFriendSchema: 'delete-friend',
  /* 置顶群内消息 */
  topGroupMessageSchema: 'top-group-message',
  /* 发送广播 */
  broadcastSchema:'broadcast',
  /* 查看好友动态 */
  viewFriendTrendSchema:'view_friend_trend',
  /* 发送频道消息 */
  sendChannelMessageSchema:'send_channel_message',
  /* 退出社群/群组/频道 */
  exitGroupSchema:'exit_group',
  /* 建立群组/频道 */
  newGroupSchema:'new_group',
  /* 建立社群 */
  newCommunitySchema:'new_community',
   /* 点赞消息 */
   likeMsgSchema:'like_msg',
   toTopSchema:'to_top',
   cancelTopSchema:'cancel_top',
   generateLicenseCodeSchema:'generate_license_code',
   publicPageAdminSchema:'public_page_admin',
};

/**
 * 周期单位
 */
export const CycleUnitOptions = [
  { prop: 'minute', value: 'MINUTE', label: i18n.t('mission["分钟"]'), min: 1 },
  { prop: 'hour', value: 'HOUR', label: i18n.t('dataCenter["小时"]'), min: 1, max: 6 },
  { prop: 'day', value: 'DAY', label: i18n.t('dataCenter["天"]'), min: 1, max: 4 * 24 }
  // { prop: "week", value: "WEEK", label: "周", min: 1, max: 4 * 24 * 7 },
  // { prop: "month", value: "MONTH", label: "月", min: 1, max: 4 * 24 * 7 * 30 }
];

/**
 * 周期单位 值映射 Map[key] = value
 */
export const CycleUnitValueMap = {};
CycleUnitOptions.forEach(item => {
  CycleUnitValueMap[item.prop] = item.value;
});

/**
 * 注册、投送 卡片步骤
 */
export const STEP_MAP_LIST = {
  createVirtualAccount: [
    { step: 'get_proxy', name: i18n.t('assets["获取代理"]') },
    { step: 'get_person', name: i18n.t('assets["申请人设"]') },
    { step: 'send_ava', name: i18n.t('assets["投送分布式平台"]') },
    { step: 'forward_proxy', name: i18n.t('assets["激活代理"]') },
    { step: 'test_proxy', name: i18n.t('assets["代理测速"]') },
    { step: 'action_result', name: i18n.t('assets["运行结果"]') },
    { step: 'save_account', name: i18n.t('assets["注册账号写入数据平台"]') },
    { step: 'update_proxy', name: i18n.t('assets["更新代理使用信息"]') },
    { step: 'update_person', name: i18n.t('assets["更新人设使用记录"]') },
    { step: 'update_email', name: i18n.t('assets["更新邮箱使用记录"]') },
    { step: 'unlock_person', name: i18n.t('assets["解锁人设"]') }
  ],
  delivery: [
    { step: 'get_proxy', name: i18n.t('assets["获取代理"]') },
    { step: 'get_account', name: i18n.t('assets["获取账号"]') },
    { step: 'get_txt', name: i18n.t('assets["获取文章"]') },
    { step: 'send_ava', name: i18n.t('assets["投送分布式平台"]') },
    { step: 'forward_proxy', name: i18n.t('assets["激活代理"]') },
    { step: 'test_proxy', name: i18n.t('assets["代理测速"]') },
    { step: 'action_result', name: i18n.t('assets["运行结果"]') },
    { step: 'save_action', name: i18n.t('assets["保存行为记录"]') },
    { step: 'update_account', name: i18n.t('assets["更新账号信息"]') },
    { step: 'update_proxy', name: i18n.t('assets["更新代理使用信息"]') }
  ]
};

/**
 * 注册、投送 卡片步骤
 */
export const STEP_DICT = {
  GET_ACCOUNT: i18n.t('assets["获取账号"]'),
  GET_PERSON: i18n.t('assets["申请人设"]'),
  OCCUPY_RESOURCE: i18n.t('assets["申请执行资源"]'),
  GET_PROXY: i18n.t('assets["获取代理"]'),
  TEST_PROXY_SPEED: i18n.t('assets["代理测速"]'),
  GET_TXT: i18n.t('assets["获取文章"]'),
  GET_COMMENT: i18n.t('assets["获取评论"]'),
  PUSH_DISTRIBUTED_PLATFORM: i18n.t('assets["推送分布式平台"]'),
  WAIT_DO: i18n.t('assets["等待运行结果"]'),
  DO_JOB: i18n.t('assets["运行结果"]'),
  WRITE_ACCOUNT: i18n.t('assets["注册账号写入平台数据"]'),
  WRITE_PUBLIC_PAGE_RECORD: i18n.t('assets["写入公共主页开通记录"]'),
  UPDATE_ACCOUNT: i18n.t('assets["更新账号信息"]'),
  UPDATE_CHARACTER_SETTING_USAGE_INFO: i18n.t('assets["更新人设使用记录"]'),
  ENABLE_CHARACTER_SETTING: i18n.t('assets["解锁人设"]'),
  UPDATE_EMAIL_USAGE_INFO: i18n.t('assets["更新邮箱使用记录"]'),
  UPDATE_PROXY_USAGE_INFO: i18n.t('assets["更新代理使用信息"]'),
  GET_PHONE: i18n.t('assets["申请电话"]'),
  GET_EMAIL: i18n.t('assets["申请邮箱"]'),
  DO_JOB_AFTER:i18n.t('assets["执行后续处理"]'),
};

/* 回写培育日志的动作 */
export const BREED_ACTION = [
  { value: 'VISIT', label: i18n.t('assets["访问"]') },
  { value: 'LIKE', label: i18n.t('assets["点赞"]') },
  { value: 'FOLLOW', label: i18n.t('assets["关注"]') },
  { value: 'REPOST', label: i18n.t('assets["转发"]') },
  { value: 'ADD_FRIEND', label: i18n.t('assets["加好友"]') },
  { value: 'JOIN', label: i18n.t('assets["加群"]') },
  { value: 'BYPASS', label: i18n.t('assets["通过好友"]') },
  { value: 'POST', label: i18n.t('assets["发帖"]'), needMessage: true },
  { value: 'COMMENT', label: i18n.t('assets["评论"]'), needMessage: true }
]
