<template>
  <span
    class="task-status-tag"
    :class="type"
  >
    <slot />
  </span>
</template>

<script>
export default {
  name: 'TaskStatusTag',
  props: {
    type: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped>
.task-status-tag {
  box-sizing: border-box;
  display: inline-block;
  padding: 0 5px;
  line-height: 19px;
  border-radius: 6px;
	border: solid 1px;
  background-color: #fff;
  border-color: #d3d4d6;
  color: #909399;
  cursor: pointer;
}
.CREATE{
  background-color: #edf1f8;
  border-color: #c8d1e0;
  color: #8294b1;
}
.RUN{
  background-color: #e5efff;
  border-color: #c4d6fb;
  color: #3569e7;
}
.SUCCESS{
  background-color: #e1f3f5;
  border-color: #bae9e4;
  color: #04c9b4;
}
.FAILURE{
  background-color: #feeeee;
  border-color: #f9bdbd;
  color: #ff274b;
}
.PAUSED{
  background-color: #fff4e0;
  border-color: #fbe8ad;
  color: #ffa600;
}
.CLOSED{
  background-color: #f2f3f5;
  border-color: #dcdfe6;
  color: #757d88;
}
</style>
