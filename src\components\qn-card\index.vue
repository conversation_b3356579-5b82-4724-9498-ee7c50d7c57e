<template>
  <div
    v-loading="loading"
    class="qn-card"
  >
    <div class="title">
      <p>{{ title }}</p>
      <div class="header">
        <slot name="header" />
        <p
          v-if="showExpand"
          class="expand"
          @click.stop="() => isExpand = !isExpand"
        >
          <template v-if="isExpand">
            <titan-tooltip :content="$t('accountCenter.展开')">
              <svg-icon
                class="expand-icon"
                icon-class="expand1"
              />
            </titan-tooltip>
          </template>
          <template v-else>
            <titan-tooltip :content="$t('accountCenter.收起')">
              <svg-icon
                class="expand-icon"
                icon-class="stow1"
              />
            </titan-tooltip>
          </template>
        </p>
      </div>
    </div>
    <div
      class="main"
      :class="{ showExpand: showExpand && isExpand }"
    >
      <slot
        v-if="showEmpty"
        name="empty"
      >
        <titan-placement
          :empty="true"
          :loading="loading"
        />
      </slot>
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'QnCard',
  props: {
    loading: {
      type: Boolean,
      default: undefined
    },
    title: {
      type: String,
      default: ''
    },
    showEmpty: {
      type: Boolean,
      default: false
    },
    showExpand: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return { isExpand: false };
  }
};
</script>

<style lang="scss" scoped>
.qn-card {
  width: 100%;
  height: 100%;
  .title {
    width: 100%;
    height: 28px;
    line-height: 28px;
    color: #fafcff;
    font-size: 14px;
    background-color: #6589d1;
    border-radius: 4px;
    padding: 0 9px;
    display: flex;
    justify-content: space-between;
    .expand {
      cursor: pointer;
      .expand-icon {
        color: #fff;
      }
    }
  }
  .main {
    width: 100%;
    height: calc(100% - 28px) !important;
    transition: all 0.5s;
  }
  .showExpand {
    display: none;
    height: 0px !important;
  }
}
</style>
