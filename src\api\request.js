import axios from 'axios';
import URL from '@/api/urls';
import router from '@/router';
import { handleResponse, handleError, handleRequest } from './handle-interceptors';
import { filterRepeatRequest, filterRepeatResponse } from '@/utils/filterRepeatReq';

class Service {
  constructor(config) {
    this.config = config;
    this.service = undefined;
    this.whiteList = [
      URL.message.type,
      URL.missionManage.uploadFile,
      URL.netResource.article.language,
      URL.netResource.article.trans,
      URL.system.switchDepartList,
      URL.netResource.comment.platform,
      URL.platformVisible.getPlatformByDepartment,
      URL.knowledge_graph.getAllRegion,
      URL.resourceDashboard.upload2TbmsUseBucket,
      URL.account.screenUploadFile,
      URL.missionManage.screenUploadFile,
      URL.account.initConnect,
      URL.account.getCurLiveScreen,
      URL.monitor.personRelationship,
      URL.account.getInfluenceGroup,
      URL.charactor.getDeviceInfo,
      URL.account.fetchIceConfig,
      URL.account.fetchGroupList,
      URL.account.envCheckCallBack,
      URL.account.getScreenStatusCallback,
      URL.account.putStatusAfterInit,
      URL.account.setCurLiveScreenList
    ];
    this.httpStore = new Map();
    return this._init();
  }

  _init() {
    this.service = axios.create(this.config);

    // 添加重复请求的过滤
    this.service.interceptors.request.use(
      config => {
        if (this.whiteList.includes(config.url)) {
          config.timeout = 500000;
          return config;
        }
        return filterRepeatRequest(config, this.httpStore);
      }
    );
    this.service.interceptors.response.use(
      response => {
        if (this.whiteList.includes(response.config.url)) return response;
        return filterRepeatResponse(response, this.httpStore);
      }
    );

    // request interceptor
    this.service.interceptors.request.use(
      config => handleRequest(config),
      error => handleError(error)
    );

    // 响应拦截器
    this.service.interceptors.response.use(
      response => handleResponse(response),
      error => handleError(error, true)
    );

    // 当路由变化时清除未完成的请求
    if (router) {
      router.beforeEach((to, from, next) => {
        // 当通过router.replace添加地址栏query时不触发清理
        if (to.name !== from.name) {
          this.clear();
        }
        // 除刷新页面，路由跳转不同页面，都清空sessionStorage里的platform
        if (from.fullPath !== '/' && to.name !== from.name) {
          window.sessionStorage.removeItem('platform');
        }
        next();
      });
    }

    return this.service;
  }

  clear() {
    this.httpStore.forEach(source => {
      source.cancel();
    });
    this.httpStore.clear();
  }
}

// create an axios instance
const service = new Service({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // baseURL: '/permission_proxy',
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 30000
});

export default service;
