import http from '@/api/request';
import URL from '../urls';

export class SystemRunStatusAPI {
  /**
   * 获取分系统列表
   */
  static getSubSystem() {
    return http.get(URL.subsystemStatus.list);
  }

  /**
   * 获取分系统服务列表
   */
  static getSubSystemService(params) {
    return http.get(URL.subsystemStatus.serviceList, { params });
  }

  /**
   * 获取分系统服务 对应
   */
  static getServiceMonitor(data) {
    return http.post(URL.subsystemStatus.service_monitor, data);
  }
}
