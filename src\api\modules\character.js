import http from '@/api/request';
import URL from '../urls';

/**
 * 自主创建账号
 * --------------------------------------------------
 */
export class SysAccountAPI {
  /**
   * 锁定| 账号
   */
  static putAccountsLockStatus(data) {
    return http.post(URL.account.putAccountsLock, data);
  }

  /**
   * 解锁 账号
   */
  static putAccountsUnLockStatus(data) {
    return http.post(URL.account.putAccountsUnLock, data);
  }

  /**
   * 账号迁移
   */
  static migrate(data) {
    return http.post(URL.proxy.migrate, data);
  }

  /**
   * 账号迁移部门列表获取
   */
  static getAllMainDepartment(data) {
    return http.post(URL.proxy.getAllMainDepartment, data);
  }

  /**
   * 根据查询条件获取自主创建账号列表
   */
  static getList(query) {
    return http.post(URL.account.list, query);
  }

  /**
   * 查询账号相关的群组/社群/频道
   */
  static getAccountExtraInfo(query) {
    return http.post(URL.account.accountExtraInfo + '/' + query.id, query);
  }

  /**
   * 查询账号已加入的相同标签群组
   */
  static getInfluenceGroup(data) {
    return http.post(URL.account.getInfluenceGroup, data);
  }

  /**
   * 获取备用库账号列表
   */
  static getSpareAccountList(query) {
    return http.post(URL.account.getSpareAccountList, query);
  }

  /**
   * 根据查询条件获取当前任务下的所注册的所有账号
   */
  static getUnderTaskList(query) {
    return http.post(URL.account.underTaskList, query);
  }

  /**
   * 查询团体列表
   */
  static getGroupList(query) {
    return http.post(URL.account.groupList, query);
  }

  /**
   * 获取没有加入过团体的账号
   */
  static getNonGroupAccount(data) {
    return http.post(URL.account.noGroupAccount, data);
  }

  /**
   * 查看结果账号列表（创建虚拟账号）
   */
  static getTimeList(query) {
    return http.post(URL.account.timeList, query);
  }

  /**
   * 查看结果账号列表（虚拟角色培育）
   */
  static getBreedList(query) {
    return http.post(URL.account.breedList, query);
  }

  /**
   * 查看账号分级标准
   */
  static getBreedRuleList() {
    return http.post(URL.account.breedRuleList, { page_index: 1, page_size: 999 });
  }

  static getCorpFilterList(query) {
    return http.post(URL.account.corpFilterList(query.accountId), { ...query, accountId: undefined });
  }

  /**
   * 查询指定账户信息（同账户列表数据一致）
   * @param {string}} id 账户ID
   */
  static getById(id) {
    return http.post(URL.account.list, { user_id: id });
  }

  /**
   * 查询账户详情
   * @param {string} id 账户ID
   */
  static getDetail(id) {
    return http.get(URL.account.info(id));
  }

  /**
   * 查询备用库账户详情
   * @param {string} id 账户ID
   */
  static getSpareDetail(id, env) {
    return http.get(URL.account.SpareInfo(id, env));
  }

  /**
   * 表单模糊查询
   */
  static search(data) {
    return http.post(URL.account.search, data);
  }

  /**
   * 查询账户标签
   */
  static getTags(params) {
    return http.get(URL.account.tag, { params: { type: 'tag', ...params } });
  }

  /**
   * 自主创建账号 打标签
   */
  static postTags(data) {
    return http.post(URL.account.addLabel, data);
  }

  /**
   * 查询账户来源
   */
  static getSource(params) {
    return http.get(URL.account.source, { params });
  }

  /**
   * 根据筛选条件查询账号数量
   * @param {object} filter 筛选条件
   */
  static getCount(filter) {
    return http.post(URL.account.count, filter);
  }

  /**
   * 上传账号
   * @param {File} file 二进制文件
   * @param {object} otherParams 其他参数
   */
  static uploadSource(file, otherParams = {}) {
    const form = new FormData();
    form.append('file', file);
    Object.keys(otherParams).forEach(key => {
      form.append(key, otherParams[key]);
    });
    return http.post(URL.account.upload, form, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
   * 账号活动日志查询
   */
  static getActivitylogList(filter) {
    return http.post(URL.account.logList, filter);
  }

  /**
   * 培育记录日志查询
   */
  static getBreedLogList(filter) {
    return http.post(URL.account.breedLogList, filter);
  }

  static getActivityLogCount(data) {
    return http.post(URL.account.logCount, data);
  }

  /**
   * 解封记录日志查询
   */
  static getUnsealLogList(filter) {
    return http.post(URL.account.unsealLogList, filter);
  }

  /**
   * 代理更换日志查询记录日志查询
   */
  static getProxyChangeLogList(params) {
    return http.get(URL.account.getProxyChangeLogList, { params });
  }

  /**
   * 手机号或邮箱更换日志查询记录日志查询
   */
  static getResourceChangeLogList(data) {
    return http.post(URL.account.getResourceChangeLogList, data);
  }

  /**
   * 改变公共主页的状态（用于删除公共主页）
   * @param {*} id
   * @param {*} data
   */
  static editAccount(id, data) {
    return http.patch(URL.account.info(id), data);
  }

  /**
   * 获取创建公共主页历史记录
   * @param {*} params
   */
  static getPublicHistory(data) {
    return http.post(URL.account.publicRecord, data);
  }

  /**
   * 清空历史失败记录
   */
  static clearPublicHistory(query) {
    return http.delete(URL.account.clearPublic, { data: query });
  }

  /**
   * 一键重新开通公共主页
   */
  static reCreatePublicPage(data) {
    return http.post(URL.operation.reCreate, data);
  }

  /**
   * 获取某些培育状态下的平台账号数量
   * @param {*} data
   */
  static getAccountStatusCount(data) {
    return http.post(URL.account.statusCount, data);
  }

  /**
   * 批量删除账号
   * @param {*} ids
   */
  static deleteAccount(ids) {
    return http.delete(URL.account.delete, { data: { account_ids: ids } });
  }

  /**
   * 批量导出账号
   * @param {*} ids
   */
  static exportAccount(data) {
    return http.post(URL.account.export, data, { responseType: 'blob' });
  }

  /**
   * 导入备用库账号
   * @param {*} data
   */
  static importSpareAccount(data) {
    return http.post(URL.account.importSpareAccount, data);
  }

  /**
   * 检测是否生成过个人画像
   * @param {*} data
   */
  static checkUser(data) {
    return http.patch(URL.account.checkUser, data);
  }

  /**
   * 生成过个人画像
   * @param {*} data
   */
  static createPersonPortrait(data) {
    return http.patch(URL.account.createPersonPortrait, data);
  }

  /**
   * 批量出库
   * @param {*} data
   */
  static batchExWarehouse(data) {
    return http.post(URL.account.batchExWarehouse, data);
  }

  /**
   * 出库记录
   * @param {*} data
   */
  static exWarehouseRecord(data) {
    return http.post(URL.account.exWarehouseRecord, data);
  }

  /**
   * 系统版本更新记录
   */
  static exVersionRecordList(data) {
    return http.post(URL.charactor.versionRecordList, data);
  }

  /**
   * 新增系统版本更新记录
   */
  static exVersionCreate(data) {
    return http.post(URL.charactor.versionCreate, data);
  }

  /* 根据出库id查询出库的账号 */
  static getExWarehouseAccountListbyId(id) {
    return http.get(URL.account.getExWarehouseAccountListbyId(id));
  }

  /**
   * 初始化运行设备
   */
  static initConnect(data) {
    return http.post(URL.account.initConnect, data);
  }

  /**
   * 同屏成功/失败的回调
   */
  static getScreenStatusCallback(data) {
    return http.post(URL.account.getScreenStatusCallback, data);
  }

  /**
   * 同屏上传文件
   */
  // static screenUploadFile(data) {
  //   return http.post(URL.account.screenUploadFile, data);
  // }

  /**
   * 控制权限
   */
  static initControl() {
    return http.get(URL.account.initControl);
  }

  /**
   * .获取当前登录用户所有在线的同屏链接
   */
  static getCurLiveScreen() {
    return http.get(URL.account.getCurLiveScreen);
  }

  /* 设置当前同屏的数据 */
  static setCurLiveScreen(data) {
    return http.post(URL.account.setCurLiveScreen, data);
  }

  /* 设置团体当前同屏的数据 */
  static setCurLiveScreenList(data) {
    return http.post(URL.account.setCurLiveScreenList, data);
  }

  /* 手机同屏检测回调 */
  static envCheckCallBack(data) {
    return http.post(URL.account.envCheckCallBack, data);
  }

  /**
   * 多端登录成功回调
   */
  static loginSuccess(data) {
    return http.post(URL.account.loginSuccess, data);
  }

  /**
   * 开始回写日志
   */
  static startWriteBackLog(data) {
    return http.post(URL.account.startWriteBackLog, data);
  }

  /* 添加手动培育日志 */

  static addManualBreedLog(data) {
    return http.post(URL.account.addManualBreedLog, data);
  }

  /* 添加培育日志时获取代理ip */
  static getProxyByIp(params) {
    return http.get(URL.account.getProxyByIp, { params });
  }

  /* 查询解封过的账号 */
  static getUnsealAccountList(data) {
    return http.post(URL.account.getUnsealAccountList, data);
  }

  static editAccounts(id, data) {
    return http.patch(URL.account.info(id), data);
  }

  /* 账号更换头像 */
  static changeAccountPhoto(data) {
    return http.post(URL.account.changeAccountPhoto, data);
  }

  /* 获取账号更换头像状态 */
  static getAccountPhotoStatus(id) {
    return http.get(URL.account.getAccountPhotoStatus(id));
  }

  /* 账号更换背景图 和更换头像同接口*/
  static changeAccountPhotoBgc(data) {
    return http.post(URL.account.changeAccountPhoto, data);
  }

  /* 获取修改标签列表 */
  static getAccountTagList(data) {
    return http.post(URL.account.getAccountTagList, data);
  }

  /* 标签列表的标签修改 */
  static editAccountTag(id, data) {
    return http.patch(URL.account.editAccountTag(id), data);
  }

  /* 标签列表的标签的批量创建*/
  static createAccountTag(data) {
    return http.post(URL.account.createAccountTag, data);
  }

  /* 标签列表的标签的批量删除 */
  static delAccountTag(data) {
    return http.delete(URL.account.delAccountTag, { data });
  }

  /* 获取分配记录日志列表*/
  static accountRecord(data) {
    return http.post(URL.account.accountRecord, data);
  }

  /* 分配记录根据id查账号*/
  static accountRecordlist(id, data) {
    return http.post(URL.account.accountRecordlist(id), data);
  }

  /* 分配记录根据id回收*/
  static accountRecordrevoke(id, data) {
    return http.post(URL.account.accountRecordrevoke(id), data);
  }

  /* 分配记录根据id分配*/
  static accountRedistribute(id, data) {
    return http.post(URL.account.accountRedistribute(id), data);
  }

  /* 分配*/
  static redistribute(data) {
    return http.post(URL.account.redistribute, data);
  }

  /* 撤回*/
  static revoke(data) {
    return http.post(URL.account.revoke, data);
  }

  /* 回收过滤列表*/
  static findTarget(data) {
    return http.post(URL.account.findTarget, data);
  }

  /* 删除分配记录*/
  static deleteRecord(ids) {
    return http.delete(URL.account.deleteRecord, { data: { id: ids } });
  }

  /* 下载批量导入的url模板文件*/
  static downloadUrlTemplate(file) {
    return URL.account.downloadUrlTemplate(file);
  }

  static resolveTargetUrlFile(formData) {
    const config = { headers: { 'Content-Type': 'multipart/form-data' } };
    return http({
      url: URL.account.resolveTargetUrlFile,
      method: 'POST',
      ...config,
      onUploadProgress: () => { }, // 必须加这个参数，因为 handle-interceptors.js里面做了判断
      data: formData
    });
  }

  /* 创建克隆任务*/
  static createCopyTask(data) {
    return http.post(URL.account.createCopyTask, data);
  }

  /* 更换账号手机号和邮箱*/
  static changeAccountResource(id, data) {
    return http.patch(URL.account.changeAccountResource(id), data);
  }

  /* 账号删除记录 */
  static accountDeleteRecords(data) {
    return http.post(URL.account.accountDeleteRecords, data);
  }

  /* 删除记录根据id查账号*/
  static accountDeleteRecordslist(data) {
    return http.post(URL.account.accountDeleteRecordslist, data);
  }

  /* 根据账号的web_reg获取ice配置 */
  static fetchIceConfig(params) {
    return http.get(URL.account.fetchIceConfig, { params });
  }

  static hangUp(data) {
    return http.put(URL.account.hangUp, data);
  }

  /* 初始化后推送状态 */
  static putStatusAfterInit(data) {
    return http.post(URL.account.putStatusAfterInit, data);
  }

  /** 手机注册账号入库-批量导入 */
  static accountBatchImport(file) {
    const form = new FormData();
    form.append('file', file);
    return http.post(URL.account.accountBatchImport, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /** 手机注册账号入库-批量导入 */
  static accountBatchImportSpare(env, file) {
    const form = new FormData();
    form.append('file', file);
    return http.post(URL.account.accountBatchImportSpare(env), form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /* 到入备用库账号 */
  static importNewSpareAccount(data) {
    return http.post(URL.account.importNewSpareAccount, data);
  }

  /* 导入虚拟角色表 */
  static importNewVirtualAccount(data) {
    return http.post(URL.account.importNewVirtualAccount, data);
  }

  /* 收藏账号 */
  static addcollect(data) {
    return http.post(URL.account.addCollect, data);
  }

  /* 移除账号 */
  static removeCollect(data) {
    return http.post(URL.account.removeCollect, data);
  }

  /* 设置同屏剪切板 */
  static setVncClipboard(data) {
    return http.post(URL.account.setVncClipboard, data);
  }

  /* 获取同屏剪切板 */
  static getVncClipboard(params) {
    return http.get(URL.account.getVncClipboard, { params });
  }

  /**
   * 同屏上传文件
   */
  static screenUploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    return http.post(URL.missionManage.screenUploadFile, formData, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
   * 同屏上传文件
   */
  static screenUpload(data) {
    return http.post(URL.account.screenUploadFile, data);
  }

  /**
   * 获取群操群控团体列表和账号
   */
  static fetchGroupList(data) {
    return http.post(URL.account.fetchGroupList, data);
  }

  /* 获取团体下面的账号 */

  static fetchGroupAccountList(data) {
    return http.post(URL.account.fetchGroupAccountList, data);
  }

  /**
   * 查询同屏任务记录
   */
  static getScreenRecordList(data) {
    return http.post(URL.account.getScreenRecordList, data);
  }

  /*
  *浏览器同屏注册账号导入账号
  */
  static getScreenSaveAccount(data) {
    return http.post(URL.account.getScreenSaveAccount, data);
  }

  /*
  *浏览器同屏注册账号获取当前标签下的cookie
  */
  static getScreenCookie(params) {
    return http.get(URL.account.getScreenCookie, { params });
  }

  /**
   * 创建并启动更新转化账号任务
   */
  static createAndStartConvertAccount(data) {
    return http.post(URL.account.createAndStartConvertAccount, data);
  }

  /**
   * 创建并启动更新个人信息任务
   */
  static createAndStartUpdateProfile(data) {
    return http.post(URL.account.createAndStartUpdateProfile, data);
  }

  /**
   * 账号后台最新任务进度
   */
  static getAccountTaskLastProcess(id) {
    return http.get(URL.account.getAccountTaskLastProcess(id));
  }

  /**
   * 获取群操群控人设分组列表
   */
  static listUserGroup(data) {
    return http.post(URL.account.listUserGroup, data);
  }

  /**
   * 获取群操群控人设分组各个人设下账号数量
   */
  static userAccountCount(data) {
    return http.post(URL.account.userAccountCount, data);
  }

  /**
   * 获取群操群控人设分组下通过人设id获取账号列表
   */
  static userAccountList(data) {
    return http.post(URL.account.userAccountList, data);
  }

  static getCoreData(data) {
    return http.post(URL.account.getCoreData, data);
  }
}

/**
 * 非合作手段账号（导入的账号）
 * --------------------------------------------------
 */
export class ImportedAccountAPI {
  /**
   * 批量导出账号
   * @param {*} ids
   */
  static exportAccount(data) {
    return http.post(URL.account.importedExport, data, { responseType: 'blob' });
  }

  static getList(query) {
    return http.post(URL.importedAccount.list, query);
  }

  /**
   * 根据ID获取账号详情
   * @param {string} id 账号id
   */
  static getDetail(id) {
    return http.get(URL.importedAccount.detail(id));
  }

  /**
   * 获取账号标签列表
   */
  static getTags(params) {
    return http.get(URL.importedAccount.tag, { params });
  }

  /**
   * 获取账号来源列表
   */
  static getSource(params) {
    return http.get(URL.importedAccount.source, { params });
  }

  /**
   * 上传账号
   * @param {File} file 二进制文件
   * @param {object} otherParams 其他参数
   */
  static uploadSource(file, otherParams = {}) {
    const form = new FormData();
    form.append('file', file);
    Object.keys(otherParams).forEach(key => {
      form.append(key, otherParams[key]);
    });
    return http.post(URL.importedAccount.upload, form, { header: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' } });
  }

  /**
   * 获取非合作手段账号平台可选项
   */
  static getPlatforms() {
    return http.get(URL.importedAccount.platform);
  }

  /**
   * 批量删除账号
   * @param {*} ids
   */
  static deleteAccount(ids) {
    return http.delete(URL.importedAccount.delete, { data: { account_ids: ids } });
  }

  /**
   * 更新账号信息
   * @param {*} data
   */
  static updateAccount(data, id) {
    return http.patch(URL.importedAccount.update(id), data);
  }

  /**
   * 获取随机人设
   * @param {*} params
   */
  static getCharacterAccount(data) {
    return http.post(URL.importedAccount.getCharacterAccount, data);
  }

  /**
   * 导入备用库账号
   * @param {*} data
   */
  static importSpareAccount(data) {
    return http.post(URL.importedAccount.importSpareAccount, data);
  }
}

/**
 * 人设
 * --------------------------------------------------
 */
export class CharacterAPI {
  /* 查询手机号邮箱人设 */
  static getPhoneEmail(query) {
    return http.post(URL.charactor.phoneEmaillist, query);
  }

  /**
   * 获取人设列表
   */

  static getList(query) {
    return http.post(URL.charactor.list, query);
  }

  /**
   * 注册任务创建时，获取可用人设
   */
  static getAvailableList(query) {
    return http.post(URL.charactor.availableList, query);
  }

  /**
    * 获取高亮显示日期
    */
  static getShowDateList(query) {
    return http.post(URL.charactor.getShowDateList, query);
  }

  /**
   * 接码平台列表
   */
  static getCvsListByPlatform(query) {
    return http.post(URL.charactor.cvsListByPlatform, query);
  }

  /**
   * 接码平台国家
   */
  static getCodeArea(query) {
    return http.post(URL.charactor.codeArea, query);
  }

  /**
   * 系统资源可用人设数量
   */
  static getCodeAvailableCount(query) {
    return http.post(URL.charactor.codeAvailableCount, query);
  }

  /**
   * 接码平台可注册账号数量
   */
  static get3rdAvailableCount(query) {
    return http.post(URL.charactor.get3rdAvailableCount, query);
  }

  /**
   * 获取可用人设数量
   */
  static getAvailableCount(query) {
    return http.post(URL.charactor.availableCount, query);
  }

  /**
   * 根据ID获取人设详情
   * @param {string} id 人设id
   */
  static getDetail(id) {
    return http.get(URL.charactor.detail(id));
  }

  /**
   * 根据ID获取备用库账号人设详情
   * @param {string} id 人设id
   */
  static getSpareDetail(id, env) {
    return http.get(URL.charactor.Sparedetail(id, env));
  }

  /**
   * 获取人设标签
   */
  static getTags(params) {
    return http.get(URL.charactor.userTag, { params });
  }

  /**
   * 新增/编辑人设标签
   */
  static addTags(data) {
    return http.post(URL.charactor.addUserTag, data);
  }

  /**
   * 查询可用IP数量
   * @param {*} filter 筛选条件
   */
  static getIpCount(filter) {
    return http.post(URL.charactor.ipCount, filter);
  }

  /**
   * 查询可用人设所在区域
   */
  static getAreas(data) {
    return http.post(URL.charactor.area, data);
  }

  /**
   * 查询人设可创建地区列表
   */
  static getAvailableAreas() {
    // return http.get(URL.charactor.available_areas);
    return http.get(URL.charactor.fake_available_areas);
  }

  /**
   * 人设列表里面查询地区级联下拉
   */
  static getCascaderArea(type) {
    const dict = {
      'user': URL.charactor.cascadeUserAreas,
      'account': URL.charactor.cascadeAccountAreas
    };
    return http.get(dict[type]);
  }

  /**
   * 查询人设所在国家列表
   */
  static getAvailableCountries() {
    return http.get(URL.charactor.country);
  }

  /**
   * 查询人设语言列表
   */
  static getAvailableLanguages() {
    return http.get(URL.charactor.language);
  }

  /**
   * 根据平台清空人设使用信息
   * @param {string} platform 平台名称
   */
  static clearRecord(platform) {
    return http.post(URL.charactor.reset, { platform });
  }

  /**
   * 获取未注册的人设平台列表
   */
  static getUnregistePlatformList(data) {
    return http.post(URL.charactor.unRegistePlatform, data);
  }

  /**
   * 批量删除人设
   * @param {*} ids
   */
  static deleteCharacter(ids) {
    return http.delete(URL.charactor.delete, { data: { user_ids: ids } });
  }

  /**
   * 批量删除虚拟账号任务
   * @param {*} ids
   */
  static getDeleteList(data) {
    return http.delete(URL.charactor.getDeleteList, { data });
  }

  /**
   * 更新人设信息
   * @param {*} data
   */
  static updateCharacter(data, id) {
    return http.patch(URL.charactor.update(id), data);
  }

  /**
   * 导入人设信息
   * @param {*} data
   */
  static importCharacter(data) {
    return http.post(URL.charactor.importCharacter, data);
  }

  /**
   * 批量生成人设信息
   * @param {*} data
   */
  static batchGenerateCharacter(data) {
    return http.post(URL.charactor.batchGenerateCharacter, data);
  }

  /**
   * 批量导入人设
   * @param {*} data
   */
  static batchImportCharacter(file, tagIds) {
    const form = new FormData();
    form.append('tagIds', tagIds);
    form.append('file', file);
    return http.post(URL.charactor.upload, form, { header: { 'Content-Type': 'multipart/form-data' } });
  }

  /**
   * 人设去查设备信息
   * @param {*} data
   */
  static getDeviceInfo(data) {
    return http.post(URL.charactor.getDeviceInfo, data);
  }

  /* 获取修改标签列表 */
  static getUserTagList(data) {
    return http.post(URL.charactor.getUserTagList, data);
  }

  /* 标签列表的标签修改 */
  static editUserTag(id, data) {
    return http.patch(URL.charactor.editUserTag(id), data);
  }

  /* 标签列表的标签的批量创建*/
  static createUserTag(data) {
    return http.post(URL.charactor.createUserTag, data);
  }

  /* 标签列表的标签的批量删除 */
  static delUserTag(data) {
    return http.delete(URL.charactor.delUserTag, { data });
  }

  // 将该人设已注册的平台标记为禁用
  // 因为注册好了的账号不会直接变为已注册, 而是存入备用库, 备用库洗出来了之后才会变为已注册
  static setCharacterPlatformForbidden(data) {
    return http.post(URL.charactor.setCharacterPlatformForbidden, data);
  }

  static getAccountAllowedNum(params) {
    return http.get(URL.charactor.getAccountAllowedNum, { params });
  }

  /* 根据人设id获取人设资源信息 */
  static getCharacterResources(data) {
    return http.post(URL.charactor.getCharacterResources, data);
  }

  /* 删除人设资源 */
  static deleteCharacterResources(data) {
    return http.post(URL.charactor.deleteCharacterResources, data);
  }

  /* 编辑人设资源 */
  static editCharacterResources(data) {
    return http.post(URL.charactor.editCharacterResources, data);
  }

  /* 置顶人设资源 */
  static setCharacterResourcesTop(data) {
    return http.post(URL.charactor.setCharacterResourcesTop, data);
  }

  /* 上传人设资源 */
  static uploadCharacterResources(data, uploadProgress) {
    const { userId, files } = data;
    return http({
      method: 'POST',
      url: URL.charactor.uploadCharacterResources(userId),
      data: files,
      onUploadProgress: uploadProgress,
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }

  /**
   * 创建人设分组
   */
  static addCharacterGroup(data) {
    return http.post(URL.charactor.addCharacterGroup, data);
  }

  /**
   * 查询人设分组
   */
  static getCharacterGroups(data) {
    return http.post(URL.charactor.getCharacterGroups, data);
  }

  /**
   * 编辑人设分组
   */
  static editCharacterGroup(id, data) {
    return http.patch(URL.charactor.editCharacterGroup(id), data);
  }

  /**
   * 删除人设分组
   */
  static deleteCharacterGroup(id) {
    return http.delete(URL.charactor.deleteCharacterGroup(id));
  }

  /**
   * 人设分组添加人设
   */
  static addMemberCharacterGroup(id, data) {
    return http.post(URL.charactor.addMemberCharacterGroup(id), data);
  }

  /**
   * 删除人设分组
   */
  static removeMemberCharacterGroup(id, data) {
    return http.post(URL.charactor.removeMemberCharacterGroup(id), data);
  }
}

/**
 * 虚拟团体
 * ------------------------------------------------------------
 */
export class CorpsAPI {
  static getList(data) {
    if (!data.groupId) {
      return;
    }
    const url = URL.corps.list.replace(/\{groupId\}$/u, data.groupId);
    return http.post(url, data);
  }

  static getAccounts(data) {
    return http.post(URL.account.groupList, data);
  }

  static editAccounts(id, data) {
    return http.patch(URL.account.info(id), data);
  }

  static getTags(params) {
    return http.get(URL.account.tag, { params });
  }

  /**
   * 新增团体
   * @param {object} data
   */
  static addCorp(data) {
    return http.post(URL.corps.corp, data);
  }

  /**
   * 自主创建账号快速新增团体
   * @param {object} data
   */
  static addQuickCorp(data) {
    return http.post(URL.corps.addQuickCorp, data);
  }

  /**
   * 更新团体信息
   * @param {object} query
   */
  static upGroup(query) {
    if (!query.groupId) {
      return;
    }
    const url = URL.corps.updateGroup.replace(/\{groupId\}$/u, query.groupId);
    return http.patch(url, query);
  }

  /**
   * 删除团体信息
   * @param
   */
  static deleteGroup(query) {
    if (!query.groupId) {
      return;
    }
    const url = URL.corps.updateGroup.replace(/\{groupId\}$/u, query.groupId);
    return http.delete(url);
  }

  /**
   * 获取团体下面分组的信息
   * @param
   */
  static getCorpsGroups(id, params) {
    return http.get(URL.corps.getCorpsGroups(id), { params });
  }

  /**
   * 获取部门下当前平台的所有团体列表
   * @param
   */
  static getAllCorpsGroupslist(data) {
    return http.post(URL.corps.getAllCorpsGroupslist, data);
  }

  /**
   * 更新团体成员
   * @param {object} query
   */
  static updateMember(query) {
    if (!query.groupId) {
      return;
    }
    const url = URL.corps.updateMember.replace(/\{groupId\}$/u, query.groupId);
    return http.patch(url, query);
  }

  /**
   * 加入团体
   */
  static accountJoinCorp(data) {
    return http.patch(URL.corps.accountJoinCorp, data);
  }

  /**
   * 移除团体成员
   * @param {object} query
   */
  static deleteMember(query) {
    if (!query.groupId) {
      return;
    }
    const url = URL.corps.updateMember.replace(/\{groupId\}$/u, query.groupId);
    return http.delete(url, { data: query });
  }

  /**
   * 移除团体成员
   * @param {object} query
   */
  static deleteInvalidAccount(params) {
    if (!params.groupId) {
      return;
    }
    return http.get(URL.corps.deleteInvalidAccount, { params });
  }

  /**
   * 批量团体打标签
   */
  static batchTagAdd(data) {
    return http.post(URL.corps.batchTagAdd, data);
  }

  /**
   * 批量团体删除标签
   */
  static batchTagDelete(data) {
    return http.post(URL.corps.batchTagDelete, data);
  }
}

/**
 * 活跃版块
 */
export class ActiveForumApi {
  // 列表
  static activeForumList(data) {
    return http.post(URL.account.activeForumList, data);
  }

  // 创建
  static addActiveForum(data) {
    return http.post(URL.account.addActiveForum, data);
  }

  // 更新
  static editActiveForum(data) {
    return http.patch(URL.account.editActiveForum(data.id), data);
  }

  // 删除
  static deleteActiveForum(id) {
    return http.delete(URL.account.deleteActiveForum + '/' + id);
  }

  // 账号设置/添加活跃版块
  static accountJoinActiveForum(data) {
    return http.post(URL.account.accountJoinActiveForum, data);
  }

  // 活跃版块账号列表
  static getActiveforumAccountList(data) {
    return http.post(URL.account.getActiveforumAccountList(data.id), data);
  }

  // 活跃版块移除成员
  static deleteActiveForumMember(data) {
    return http.delete(URL.account.deleteActiveForumMember, { data });
  }
}

/**
 * 桌面-快捷方式
 */
export class DesktopApi {
  // 创建
  static addDesktop(data) {
    return http.post(URL.desktop.addDesktop, data);
  }

  // 列表
  static getDesktopList(data) {
    return http.post(URL.desktop.getDesktopList, data);
  }

  // 修改
  // static patchDesktop(id, data) {
  //   return http.patch(URL.desktop.patchDesktop(id), data);
  // }

  // 删除
  static delDesktop(id) {
    return http.delete(URL.desktop.delDesktop + '/' + id);
  }
}

/**
 * 账号信息预览及导出
 */

export class PreviewExprotAPI {
  // 账号信息预览
  static accountPreview(data) {
    return http.post(URL.account.accountPreview, data);
  }

  // 账号信息预览字段
  static previewList(params) {
    return http.get(URL.account.previewList, { params });
  }

  // 当前账号保存的预览字段
  static accountPreviewList(params) {
    return http.get(URL.account.accountPreviewList, { params });
  }

  // 保存预览字段
  static accountPreviewListSave(data) {
    return http.post(URL.account.accountPreviewListSave, data);
  }

  // 导出账号
  static accountExcel(data) {
    return http.post(URL.account.accountExcel, data, { responseType: 'blob' });
  }
}
