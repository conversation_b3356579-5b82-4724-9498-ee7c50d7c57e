
import { calcMediaDuration, calcMediaSize, checkType } from '@/utils';
import { merge } from 'lodash'
import i18n from '@/lang'

export const visitTimeSetPlatforms = [
  'facebook',
  'twitter',
  'reddit',
  'tiktok',
  'instagram',
  'youtube',
  'mewe'
]

/**
 * 账号类型
 * - init
 */
export const ResourceTypeOptions = [
  {
    label: i18n.t('assets["手机号"]'),
    prop: "phone",
    value: 0,
    enumable: true
  },
  {
    label: i18n.t('assets["邮箱"]'),
    prop: "email",
    value: 1,
    enumable: true
  },
  {
    label: i18n.t('assets["手机号+邮箱"]'),
    prop: "both",
    value: 2,
    enumable: false
  },
  {
    label: i18n.t('assets["联合登录"]'),
    prop: "union",
    value: 3,
    enumable: false
  }
];

/**
 * 账号标签
 * - 结构: { 0: "手机号" }
 */
export const ResourceTypeLabelMap = {};

/**
 * 账号类型
 * - 结构: { "phone": 0 }
 */
export const ResourceTypeMap = {};

ResourceTypeOptions.forEach(item => {
  ResourceTypeLabelMap[item.value] = item.label;
  ResourceTypeMap[item.prop] = item.value;
});

/**
 * 时间换算
 */
const TimeConfig = {
  second: 1000,
  minute: 60 * 1000,
  hour: 60 * 60 * 1000,
  day: 24 * 60 * 60 * 1000,
  month: 30 * 24 * 60 * 60 * 1000
};

/**
 * 采集结果类型
 */
export const CollectionResultTypeMap = {
  article: i18n.t('assets["文章"]'),
  image: i18n.t('assets["图片"]'),
  video: i18n.t('assets["视频"]'),
  audio: i18n.t('assets["音频"]'),
  account: i18n.t('assets["账号"]'),
  comment: i18n.t('assets["评论"]')
};

/**
 * 发帖默认配置
 */
const DefaultPostConfig = {
  text: {
    // 发帖最大字数
    maxLength: 100,
    // 发帖时隐藏标题
    hideTitle: false
  },
  image: {
    // 默认最大上传数
    limit: 4,
    // 以前默认支持的类型
    // suffix: ["jpg", "jpeg", "png", "gif"],

    // 现在默认支持的类型
    suffix: ["jpg", "jpeg", "png"],
    suffixTip: i18n.t('schema["常见格式"]'),
    // 默认最大尺寸
    size: calcMediaSize("10MB"),

  },
  cover:{
    // 默认最大上传数
    limit: 1,
    // 以前默认支持的类型
    // suffix: ["jpg", "jpeg", "png", "gif"],

    // 现在默认支持的类型
    suffix: ["jpg", "jpeg", "png"],
    suffixTip: i18n.t('schema["常见格式"]'),
    // 默认最大尺寸
    size: calcMediaSize("0.1MB"),
    width:200,//最小支持图片宽度
    height:200//最小支持图片的高度
  },
  video: {
    // 默认最大上传数
    limit: 1,
    // 默认支持的类型
    // suffix: [1, 2],
    // 默认最大尺寸
    size: calcMediaSize("50MB"),
    // 以前默认支持的类型
    // suffix: [
    //   "3g2",
    //   "3gp",
    //   "3gpp",
    //   "asf",
    //   "avi",
    //   "dat",
    //   "divx",
    //   "dv",
    //   "f4v",
    //   "flv",
    //   "m2ts",
    //   "m4v",
    //   "mkv",
    //   "mod",
    //   "mov",
    //   "mp4",
    //   "mpe",
    //   "mpeg",
    //   "mpeg4",
    //   "mpg",
    //   "mts",
    //   "nsv",
    //   "ogm",
    //   "ogv",
    //   "qt",
    //   "tod",
    //   "ts",
    //   "vob",
    //   "wmv"
    // ],

    // 现在默认支持的类型
    suffix: [
      "mp4",
      "avi"
    ],
    // 标签文字
    duration: calcMediaDuration("30分钟"),
    suffixTip: i18n.t('schema["常见格式"]'),
    supportCommentVideo: false,

  }
};

const DefaultCommentConfig = {
  text: {
    // 发帖最大字数
    maxLength: 100,
    minLength: 1,
    supportSearchGif: false,
    supportCommentVideo: false,
    supportCommentImage: false,
    commentLimit: 1,
    commentSuffix: ["jpg", "jpeg", "png"],
    commentSize: calcMediaSize("10MB")
  }
};

const DefaultPrivateMsgConfig = {
  text: {
    // 发帖最大字数
    maxLength: 500,
    minLength: 1,
    showTitle: false,
    titleMaxLength: 50,
    titleMinLength: 1
  }
};

const DefaultaddFriendsConfig = {
  text: {
    // 发帖最大字数
    maxLength: 200,
    minLength: 1,
    supportImage: false,
  }
}

/**
 * interface platform {
 *   name: String,             平台名称
 *   url: String,              平台网址
 *   sortIdx: Number           平台权重（用于列表排序，值越小，排名越靠前）
 *   color: String,            平台简述组件颜色映射
 *   accountType: Number       平台账号类型：枚举列表ResourceTypeOptions
 *   rule: {                   平台特殊分发配置 / 目标url正则配置
 *     PROXY_COOL_DOWN         代理冷却时间
 *     URL_REGEXP: {           各个类型的url正则规则
 *        platformBasicUrl: 平台的url基本配置
 *        visitUrl: 访问url
 *        postUrl: 目标帖子
 *        userUrl: 目标人物
 *        groupUrl: 目标群
 *        personUrl: 个人主页
 *        publicPageUrl: 公共主页,
 *        commentUrl: 目标评论
 *        postForumUrl: 发帖的目标板块
 *        commentForumUrl: 评论的目标板块
 *     }
 *   }
 *   accountLevel: Object      虚拟账号分级表
 *   action: [                 平台支持的社交行为
 *     'post'                  发帖
 *     'comment'               评论
 *     'repost'                转发
 *     'like'                  点赞
 *     'dislike'               点踩
 *     'follow'                加好友/关注
 *     'send_msg'              送消息
 *     'recv_msg'              接收消息
 *     'join'                  加入社区/子版块
 *     'bypass'                通过好友请求
 *     'visit'                 访问
 *     'upload_avatar'         上传头像
 *     'vote'                  发起/参与投票
 *     'deliver_message'       发消息
 *     'collect'               收藏
 *   ]
 *   postConfig: [             发帖配置：数组每项支持String和Object 示例：['text']等价于[{ type: 'text' }]
 *     {
 *       type                  类型 text / image / video
 *       maxLength             文本：最大长度（Int)
 *       minLength             文本：最小长度（Int)
 *       hideTitle             文本：隐藏标题
 *       suffix                图片/视频：文件格式类型数组([String])
 *       suffixTip             图片/视频：提示文本，默认值'常见格式'
 *       size                  图片/视频：上传大小限制（单位：MB），支持calcMediaSize(size)函数
 *       limit                 图片/视频：上传数量限制（单位：个）
 *       required              是否必填[true/false]
 *       hideTitle             是否隐藏标题
 *       titleMinLength         标题的最小长度
         titleMaxLength         标题的最大长度
 *     }
 *   ]
 *   collectionType: [        采集支持的类型
 *      'user_list'           目标人物
 *      'topic'               目标话题
 *      'channel'             目标频道
 *      'hot'                 热点采集
 *      'keyword'             关键词采集
 *      'lang'                语言
 *      'since'               时间范围
 *      'group'               目标群组
 *      'image'               采集图片
 *      'audio'               采集音频
 *      'video'               采集视频
 *   ]
 *   commentConfig: [         评论配置
 *     {
 *        type                类型 目前仅text
 *        minLength           评论最小字数
 *        maxLength           评论最大字数
 *     }
 *   ]
 *   collectionResultTypes: Array   采集结果类型列表，参照枚举值 CollectionResultTypeMap
 *   location：String          采集方向
 *   supportTask: [            支持的任务行为
 *      'create'               注册
 *      'breed'                培育
 *      'collect'              采集
 *      'delevery'             投送
 *   ]
 *   language: Array           平台需要的语言列表
 *   targetUrls: Array         目标帖子的快捷选项
 *   tips: Object              各投送行为注意事项
 *   messageTargetType: String 平台 投送 目标人物投送方式 url / phone
 *   hideCookie                是否在账号详情页内隐藏Cookie信息
 * }
 */
export const PlatformFeatures = {
  facebook: {
    sortIdx: 1,
    name: i18n.t('assets["Facebook"]'),
    url: "https://www.facebook.com/",
    color: "#4164ab",
    accountType: ResourceTypeMap.both,
    rule: {
      PROXY_COOL_DOWN: 1 * TimeConfig.day,
      URL_REGEXP: {
        visitUrl: {
          regexp: new RegExp('https://(\\w+\\.)?facebook\\.com/.+')
        },
        postUrl: {
          regexp: new RegExp('https://(\\w+\\.)?facebook\\.com/.+')
        },
        userUrl: {
          regexp: new RegExp('https://(\\w+\\.)?facebook\\.com/.+')
        },
        commentUrl: {
          regexp: new RegExp('https://(\\w+\\.)?facebook\\.com/.+')
        },
        groupUrl: {
          regexp: new RegExp('^https://www\\.facebook\\.com/groups/[a-zA-Z0-9]+$')
        },
        postForumUrl: {
          regexp: new RegExp('^https://www\\.facebook\\.com/groups/[a-zA-Z0-9]+$')
        },
      }
    },
    action: ["visit", "post", "comment", "repost", "like", "upload_avatar", "reply", "join", "add_friend", "send_msg", "public_page_invite_like", 'public_page_repost', 'repost_to_group', 'follow_public_page', 'bypass', 'delete_post'],
    postConfig: [
      {
        type: "text",
        maxLength: 50000,
        hideTitle: true,
        chineseCount: 3,//中文及中文符号占字符数
        emojiCount: 4//表情占字符数
      },
      {
        type: "image",
        limit: 4,
        // suffix: ["jpg", "jpeg", "png"],
        size: calcMediaSize("10MB"),
      },
      {
        type: "video",
        size: calcMediaSize("500MB"),
        duration: calcMediaDuration("4小时"),

      }
    ],
    collectionType: ["user_list"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 7000,
        supportSearchGif: true,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    privateMsgConfig: ['text'],
    collectionResultTypes: ["article", "image", "account"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-27",
      2: "27-41",
      3: "42-55",
      4: "56-70",
      5: ">=70"
    }
  },
  twitter: {
    sortIdx: 2,
    name: i18n.t('assets["Twitter"]'),
    url: "https://x.com/",
    color: "#2da9e3",
    accountType: ResourceTypeMap.both,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5,
      URL_REGEXP: {
        platformBasicUrl: {
          regexp: new RegExp('^(https?|ftp):\\/\\/(\\w+\\.)?(x|twitter)' + '\\.\\S+', 'iu')
        },
        visitUrl: {
          regexp: new RegExp('https://(\\w+\\.)?(x|twitter)\\.com/\\w+/status/(?:\\d)+$')
        },
        postUrl: {
          regexp: new RegExp('https://(\\w+\\.)?(x|twitter)\\.com/\\w+/status/(?:\\d)+$')
        },
        userUrl: {
          regexp: new RegExp('https://(\\w+\\.)?(x|twitter)\\.com/(?:[^.])+$')
        },
        commentUrl: {
          regexp: new RegExp('https://(\\w+\\.)?(x|twitter)\\.com/\\w+/status/(?:\\d)+$')
        }
      }
    },
    action: ["visit", "post", "comment", "repost", "like", "follow", "send_msg", "reply", 'callback_follow', 'delete_post'],
    postConfig: [
      {
        type: "text",
        maxLength: 270,
        hideTitle: true,
        chineseCount: 2,//中文及中文符号占字符数
        emojiCount: 2//表情占字符数
      },
      {
        type: "image",
        limit: 4,
        size: calcMediaSize("5MB"),

      },
      {
        type: "video",
        size: calcMediaSize("500MB"),
        duration: calcMediaDuration("140秒")
      }
    ],
    collectionType: ["keyword", "topic", "user_list", "lang", "since"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 270,
        supportSearchGif: true,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    privateMsgConfig: ['text'],
    collectionResultTypes: ["article", "image", "account"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-27",
      2: "27-41",
      3: "42-55",
      4: "56-70",
      5: ">=70"
    }
  },
  youtube: {
    sortIdx: 3,
    name: i18n.t('assets["Youtube"]'),
    url: "https://www.youtube.com/",
    color: "#dd2d27",
    // 系统设置要求显示youtube平台，暂时改为both
    accountType: ResourceTypeMap.both,
    action: ["visit", "post", "comment", "like", "dislike", "follow", 'delete_post'],
    postConfig: [
      {
        type: "text",
        maxLength: 9000,
        titleMinLength: 1,
        titleMaxLength: 90,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 1
      },
      {
        type: "video",
        // suffix: [
        //   "mov",
        //   "mpeg4",
        //   "mp4",
        //   "avi",
        //   "wmv",
        //   "mpegps",
        //   "flv",
        //   "3gpp",
        //   "webm",
        //   "dnxhr",
        //   "prores",
        //   "cineform",
        //   "hevc"
        // ]
        suffix: [
          "mp4",
          "avi"
        ],
        size: calcMediaSize("500MB"),
        required: true
      }
    ],
    collectionType: ["hot", "keyword", "user_list", "channel"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 9000
      }
    ],
    collectionResultTypes: ["video", "audio"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-27",
      2: "27-41",
      3: "42-55",
      4: "56-70",
      5: ">=70"
    }
  },
  instagram: {
    name: i18n.t('assets["Ins"]'),
    url: "https://www.instagram.com/",
    color: "#fd0e60",
    accountType: ResourceTypeMap.both,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5
    },
    action: ["visit", "post", "comment", "like", "follow", 'callback_follow', 'delete_post'],
    postConfig: [
      {
        type: "text",
        hideTitle: true,
        maxLength: 2100,
        chineseCount: 1,//中文及中文符号占字符数
        emojiCount: 2//表情占字符数
      },
      {
        type: "image",
        limit: 10,
        // suffix: ["jpg", "jpeg", "png", "gif", "webp", "bmp", "tif", "psb"],
      },
      {
        type: "video",
        size: calcMediaSize("80MB"),
        duration: calcMediaDuration("90秒")
      }
    ],
    collectionType: ["user_list"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 2100
      }
    ],
    collectionResultTypes: ["article", "image", "video", "account"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-13",
      2: "14-27",
      3: "28-41",
      4: "42-55",
      5: ">=56"
    }
  },
  linkedin: {
    sortIdx: 4,
    name: i18n.t('assets["LinkedIn"]'),
    url: "https://www.linkedin.com/",
    color: "#0b66c1",
    accountType: ResourceTypeMap.both,
    collectionType: ["hot"],
    action: ["visit", "post", "comment", "repost", "like", "follow", 'callback_follow', 'bypass', "add_friend"],
    postConfig: [
      {
        type: "text",
        maxLength: 2900,
        hideTitle: true,
        titleMinLength: 1,
        titleMaxLength: 100,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 3
      },
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 1200,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      },

    ],
    addFriendsConfig: [
      {
        type: 'text',
        maxLength: 300,
        supportCommentImage: false,
      }
    ],

    collectionResultTypes: ["article"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-13",
      2: "14-27",
      3: "28-41",
      4: "42-55",
      5: ">=56"
    }
  },
  pinterest: {
    name: i18n.t('assets["Pinterest"]'),
    url: "https://www.pinterest.com/",
    color: "#bd2627",
    accountType: ResourceTypeMap.email,
    rule: {},
    tips: {
      like: "Pinterest 只支持对视频进行点赞",
    },
    action: ["visit", "post", "comment", "repost", "like", "follow", 'callback_follow'],
    postConfig: [
      {
        type: "text",
        hideContent: false,
        titleMinLength: 1,
        titleMaxLength: 100,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 3
      },
      {
        type: "image",
        limit: 1,
        // suffix: ["jpg", "png"],
        size: calcMediaSize("20MB"),

      }
    ],
    collectionType: ["hot", "keyword"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 500
      }
    ],
    collectionResultTypes: ["article", "image", "account"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-13",
      2: "14-27",
      3: "28-41",
      4: "42-55",
      5: ">=56"
    }
  },
  eyny: {
    name: i18n.t('assets["伊莉论坛"]'),
    url: "http://www01.eyny.com/",
    color: "#f98401",
    accountType: ResourceTypeMap.email,
    rule: {},
    action: ["visit", "post", "comment", "like", "add_friend"],
    postConfig: [
      {
        type: "text",
        maxLength: 50000,
        minLength: 10,
        titleMinLength: 1,
        titleMaxLength: 120,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 6

      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 50000,
        min: 30,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    collectionResultTypes: ["article", "account", "comment"],
    location: "台湾",
    supportTask: ["create", "breed", "collect", "delivery"],
    language: ["yue", "zt"],
    // targetUrls: [ //#14200 去掉快捷目标帖子地址选项
    //   {
    //     label: "趣味笑话区",
    //     value: "http://www01.eyny.com/thread-7250538-1-1.html"
    //   },
    //   {
    //     label: "美食区",
    //     value: "http://www01.eyny.com/thread-2096079-1-D2WYCF2W.html"
    //   },
    //   {
    //     label: "趣味笑话区",
    //     value: "http://www01.eyny.com/thread-********-1-244XQ6SV.html"
    //   }
    // ],
    tips: {
      like: "新手每天只能点赞25次，对点赞的版块没有限制。",
      post:
        "新手会员在线时间需超过一小时才能发帖，在线时间的增加方式不能纯挂网，而是要有“操作”，也就是要至少每隔十分钟执行一次翻看帖子的行为。",
      comment:
        "1.评论帖子需达 8个字以上；与其他用户的评论互动需达20字以上。其中，重复字与主题不相关的内容不计入有效字数。2.评论的内容不能复制帖子的标题与内容或者是其他用户的评论，只将复制内容作简单修改、加字、减字处理的都视为复制文。3.同一篇主题的帖子不能重复评论。4.不能使用同一内容回复不同会员。"
    },
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    },
    hideCookie: true
  },
  pixnet: {
    name: i18n.t('assets["痞客邦博客"]'),
    url: "https://www.pixnet.net/",
    color: "#1b4cb9",
    accountType: ResourceTypeMap.email,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5,
      PROXY_COOL_DOWN: 12 * TimeConfig.hour
    },
    action: ["visit", "post", "comment", "repost", "like", "follow", "join", 'callback_follow'],
    postConfig: [
      {
        type: "text",
        hideTitle: true,
        maxLength: 20000,
        titleMinLength: 1,
        titleMaxLength: 300,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 3
      },
      {
        type: "image",
        limit: 10,
        // suffix: ["jpg", "png", "gif", "webp", "bmp", "tif", "psb"],
        size: calcMediaSize("8MB")
      }
    ],
    collectionType: ["hot", "keyword"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 20000
      }
    ],
    collectionResultTypes: ["article", "comment"],
    location: "台湾",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-13",
      2: "14-27",
      3: "28-41",
      4: "42-55",
      5: ">=56"
    },
    hideCookie: true
  },
  band: {
    name: i18n.t('assets["Band"]'),
    url: "https://band.us/en",
    color: "#0FAD22",
    accountType: ResourceTypeMap.email,
    action: ["visit", "post", 'comment', "join"],
    postConfig: [
      {
        type: "text",
        hideTitle: true,
        maxLength: 10000,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 1
      },
      {
        type: "image",
        // suffix: [
        //   "tiff",
        //   "svgz",
        //   "pjp",
        //   "jpg",
        //   "jepg",
        //   "ico",
        //   "gfif",
        //   "gif",
        //   "tif",
        //   "xbm",
        //   "pjepg",
        //   "bmp",
        //   "svg",
        //   "png",
        //   "dib",
        //   "webp"
        // ],
        limit: 99,
        size: calcMediaSize("30MB")
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 9900,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    collectionResultTypes: ["article"],
    location: "韩国",
    supportTask: ["create", "breed", "collect", "delivery"],
    collectionType: [
      "hot",
      {
        type: "group",
        suggestions: [
          "Hong Kong - 香港",
          "这是我们要的台湾吗？",
          "臺灣第一女總統 l 蔡英文",
          "台灣股票SPL型態選股系統",
          "各Band社群推廣服務",
          "民主中国",
          "台灣怎麼了- 時事選舉議題討論",
          "總統你選誰 | 立委衝啥毀",
          "橘色力量再起",
          "台日友好即時文化交流団へようこそ♪(´ε｀) 台湾 x 日本 一号団",
          "HR學點什麼/一個人資的獨白",
          "靠北Band團",
          "勞資爭議、勞工權益、勞基法、工會、勞保-免費法律諮詢（勞動法推廣協會）",
          "菲律賓天使城. 泰國曼谷討論區",
          "聚集吧！正面能量",
          "社工 資訊交流園地",
          "韓國瑜後援會"
        ]
      }
    ],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    },
    hideCookie: true
  },
  tistory: {
    name: i18n.t('assets["Tistory"]'),
    url: "https://www.tistory.com/",
    color: "#ec6923",
    accountType: ResourceTypeMap.email,
    rule: {},
    action: ["visit", "post", "comment", "like", "follow"],
    postConfig: [
      {
        type: "text",
        maxLength: 1000,
        titleMaxLength: 30,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 2
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 120,
        min: 10
      }
    ],
    collectionResultTypes: ["article", "comment"],
    location: "韩国",
    supportTask: ["breed", "collect", "delivery"],
    language: ["ko"],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  mixi: {
    name: i18n.t('assets["Mixi"]'),
    url: "https://mixi.jp/",
    color: "#d1ad59",
    accountType: ResourceTypeMap.email,
    action: ["visit", "post"],
    postConfig: [
      {
        type: "text",
        maxLength: 150,
        hideTitle: true,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 1
      },
      {
        type: "image",
        limit: 1,
        // suffix: ["jpg", "png"],
        size: calcMediaSize("32MB")
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 150
      }
    ],
    collectionResultTypes: ["article", "comment"],
    location: "日本",
    supportTask: ["create", "breed", "collect", "delivery"],
    language: ["ja"],
    tips: {
      post: "由于Mixi平台限制，发帖需要间隔5天"
    },
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  pixiv: {
    name: i18n.t('assets["Pixiv"]'),
    url: "https://www.pixiv.net/",
    color: "#0096db",
    accountType: ResourceTypeMap.email,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 3,
      PROXY_COOL_DOWN: 1 * TimeConfig.day
    },
    action: ["visit", "post", "comment", "like", "follow", 'callback_follow'],
    postConfig: [
      {
        type: "text",
        maxLength: 50000,
        titleMinLength: 1,
        titleMaxLength: 100,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 3
      },
      {
        type: "image",
        limit: 200,
        size: calcMediaSize("8MB")
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 200
      }
    ],
    collectionResultTypes: ["article", "image", "account"],
    location: "日本",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  ameba: {
    name: i18n.t('assets["Ameba"]'),
    url: "https://www.ameba.jp/",
    color: "#4dac26",
    accountType: ResourceTypeMap.email,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5
    },
    action: ["visit", "post", "comment", "repost", "like", "follow", 'callback_follow'],
    postConfig: [
      {
        type: "text",
        maxLength: 50000,
        titleMinLength: 1,
        titleMaxLength: 45,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 1
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 1900
      }
    ],
    collectionResultTypes: ["article", "account"],
    location: "日本",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-13",
      2: "14-27",
      3: "28-41",
      4: "42-55",
      5: ">=56"
    }
  },
  zalo: {
    name: i18n.t('assets["Zalo"]'),
    url: "https://chat.zalo.me",
    color: "#00acd4",
    accountType: ResourceTypeMap.phone,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 2
    },
    action: ["send_msg", 'bypass'],
    collectionType: ["group"],
    postConfig: [],
    commentConfig: [],
    privateMsgConfig: ['text'],
    collectionResultTypes: ["article"],
    location: "越南",
    supportTask: ["create", "breed", "collect", "delivery"],
    messageTargetType: "phone",
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  tiktok: {
    name: i18n.t('assets["Tiktok"]'),
    url: "https://www.tiktok.com/",
    color: "#040303",
    accountType: ResourceTypeMap.both,
    action: ["visit", "post", "comment", "like", "follow", "upload_avatar", 'delete_post', "send_msg"],
    rule: {
      URL_REGEXP: {
        visitUrl: {
          regexp: new RegExp('https://(\\w+\\.)?tiktok\\.com/@\\w+/video/\\d+')
        },
        postUrl: {
          regexp: new RegExp('https://(\\w+\\.)?tiktok\\.com/@\\w+/video/\\d+')
        },
        userUrl: {
          regexp: new RegExp('https://(\\w+\\.)?tiktok\\.com/@\\w+')
        }
      }
    },
    postConfig: [
      {
        type: "text",
        maxLength: 4000,
        hideTitle: true,
        chineseCount: 1,
        emojiCount: 2
      },
      {
        type: 'video',
        required: true
      },
      {
        type: 'cover',
        limit: 1,
        size: calcMediaSize("10MB"),
        width:324,//最小支持图片宽度
        height:432//最小支持图片的高度
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 150
      }
    ],
    collectionResultTypes: ["article", "video", "account"],
    location: "中国",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-6",
      2: "7-27",
      3: "28-34",
      4: "35-41",
      5: ">=42"
    }
  },
  weibo: {
    name: i18n.t('assets["微博平台"]'),
    url: "https://weibo.com/",
    color: "#d9251d",
    accountType: ResourceTypeMap.phone,
    action: ["visit", "post", "comment", "repost", "like", "follow", "send_msg", 'callback_follow'],
    postConfig: [
      {
        type: "text",
        maxLength: 50000,
        titleMinLength: 0,
        titleMaxLength: 30,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 7
      },
      {
        type: "image",
        limit: 18,
        size: calcMediaSize("20MB")
      },
      {
        type: "video",
        // suffix: [
        //   "mpg",
        //   "m4v",
        //   "mp4",
        //   "flv",
        //   "3gp",
        //   "mov",
        //   "avi",
        //   "rmvb",
        //   "mkv",
        //   "wmv"
        // ]
        suffix: [
          "mp4",
          "avi"
        ],
        size: calcMediaSize("500MB")
      }
    ],
    collectionType: ["hot", "keyword", "user_list"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 140,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    collectionResultTypes: ["article", "account", "comment"],
    location: "中国",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-27",
      2: "27-41",
      3: "42-55",
      4: "56-70",
      5: ">=70"
    }
  },
  reddit: {
    name: i18n.t('assets["Reddit"]'),
    url: "https://www.reddit.com/",
    color: "#ff381e",
    accountType: ResourceTypeMap.email,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5,
      PROXY_COOL_DOWN: 2 * TimeConfig.hour,
      URL_REGEXP: {
        visitUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/\\b(user|r)\\b/(?:[^.])*$')
        },
        postUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/\\b(user|r)\\b/.+/comments/(?:[^.])*$')
        },
        userUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/user/(?:[^.])*$')
        },
        groupUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/r/(?:[^.])*$')
        },
        commentUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/r/.+/comments/.+/comment/(?:[^.])*$')
        },
        postForumUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/r/(?:[^.])*$')
        },
        commentForumUrl: {
          regexp: new RegExp('https://(\\w+\\.)?reddit\\.com/r/(?:[^.])*$')
        }
      }
    },
    action: ["visit", "post", "comment", "like", "dislike", "follow", "join", 'callback_follow', 'delete_post', 'send_msg', 'repost'],
    postConfig: [
      {
        type: "text",
        maxLength: 35000,
        titleMinLength: 1,
        titleMaxLength: 300,
        showWordLimit: true,
        chineseCount: 3,//中文及中文符号占字符数
        emojiCount: 4//表情占字符数
      },
      {
        type: "image",
        limit: 4,
        size: calcMediaSize("20MB")
      }
    ],
    collectionType: ["hot", "keyword", "user_list", "topic"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 9000,
      }
    ],
    privateMsgConfig: ['text'],
    collectionResultTypes: ["article", "account", "comment"],
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-6",
      2: "7-27",
      3: "28-34",
      4: "35-41",
      5: ">=42"
    }
  },
  hkgolden: {
    name: i18n.t('assets["高登"]'),
    url: "https://forum.hkgolden.com/",
    color: "#ffd115",
    accountType: ResourceTypeMap.union,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5,
      PROXY_COOL_DOWN: 12 * TimeConfig.hour
    },
    action: ["visit", "post", "comment", "like", "dislike", "follow"],
    postConfig: [
      {
        type: "text",
        maxLength: 2500
      },
      {
        type: "image",
        size: calcMediaSize("5MB")
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 2500,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    collectionResultTypes: ["article", "account", "comment"],
    location: "香港",
    supportTask: ["collect"],
    language: ["yue"],
    hideCookie: true
  },
  lihkg: {
    name: i18n.t('assets["连登"]'),
    url: "https://lihkg.com/category/1",
    color: "#f8dc1e",
    // accountType: ResourceTypeMap.email,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5,
      PROXY_COOL_DOWN: 12 * TimeConfig.hour
    },
    action: ["visit", "post", "comment", "like", "dislike", "follow"],
    postConfig: [
      {
        type: "text",
        maxLength: 2500
      },
      {
        type: "image",
        size: calcMediaSize("5MB")
      }
    ],
    collectionType: ["hot"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 4000,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    collectionResultTypes: ["article", "account"],
    location: "香港",
    supportTask: ["collect"],
    language: ["yue"]
  },
  discuss: {
    name: i18n.t('assets["香港讨论区"]'),
    url: "https://www.discuss.com.hk/",
    color: "#f99920",
    accountType: ResourceTypeMap.email,
    action: ["visit", "post", "comment", "like", "dislike", "follow", "add_friend", 'callback_follow'],
    postConfig: [
      {
        type: 'text',
        maxLength: 18000,
        titleMinLength: 1,
        titleMaxLength: 80,
        showWordLimit: true,
        chineseCount: 1,//中文及中文符号占字符数
        emojiCount: 20//表情占字符数
      }
    ],
    collectionType: ["hot", "keyword"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 18000,
        supportCommentImage: true,
        commentLimit: 1,
        commentSuffix: ["jpg", "jpeg", "png", 'gif'],
      }
    ],
    collectionResultTypes: ["article", "comment"],
    supportTask: ["create", "breed", "collect", "delivery"],
    language: ["yue"]
  },
  ptt: {
    name: i18n.t('assets["PTT"]'),
    url: "https://term.ptt.cc/",
    color: "#970000",
    accountType: ResourceTypeMap.email,
    collectionResultTypes: ["article"],
    collectionType: ["hot"],
    location: "台湾",
    supportTask: ["create", "breed", "collect", "delivery"],
    action: ["visit", 'post', 'comment'],
    postConfig: [
      {
        type: 'text',
        maxLength: 10000,
        minLength: 50,
        titleMinLength: 1,
        titleMaxLength: 46,
        showWordLimit: true,
        chineseCount: 2,//中文及中文符号占字符数
        emojiCount: 1//表情占字符数
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 100
      }
    ],
    language: ["zt"]
  },
  yam: {
    name: i18n.t('assets["番薯藤论坛"]'),
    url: "https://www.yam.com/",
    color: "#189d4a",
    accountType: ResourceTypeMap.email,
    collectionType: ["hot"],
    collectionResultTypes: ["article"],
    location: "台湾",
    action: ["visit"],
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  hinet: {
    name: i18n.t('assets["Hinet"]'),
    url: "https://www.hinet.net/",
    color: "#162185",
    collectionResultTypes: ["article"],
    collectionType: ["hot", "keyword"],
    location: "台湾",
    supportTask: ["collect"],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  nccu: {
    name: i18n.t('assets["NCCU"]'),
    url: "https://www.nccu.edu.tw/",
    color: "#13377f",
    collectionResultTypes: ["article"],
    collectionType: ["hot"],
    location: "台湾",
    supportTask: ["collect"],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  kakao: {
    name: i18n.t('assets["Kakao story"]'),
    url: "https://story.kakao.com/",
    color: "#ffc412",
    // 系统设置要求显示youtube平台，暂时改为both,原本为email
    accountType: ResourceTypeMap.both,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5
    },
    action: ["visit", "post", "comment", "like", "follow", 'callback_follow'],
    postConfig: [
      {
        type: "text",
        hideTitle: true,
        maxLength: 2000,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 3
      },
      {
        type: "image",
        limit: 20,
        // suffix: ["jpg", "png", "gif", "bmp"],
        size: calcMediaSize("20MB")
      },
      {
        type: "video",
        size: calcMediaSize("500MB")
      }
    ],
    collectionType: ["channel", "topic"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 500
      }
    ],
    collectionResultTypes: ["article", "account", "comment"],
    location: "韩国",
    supportTask: ["create", "breed", "collect", "delivery"],
    language: ["ko"],
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  odnoklassniki: {
    name: i18n.t('assets["Odnoklassniki"]'),
    url: "https://ok.ru/",
    color: "#fff3ec",
    // accountType: ResourceTypeMap.phone,
    collectionResultTypes: ["article"]
  },
  // parler: {
  //   name: i18n.t('assets["Parler"]'),
  //   url: "https://parler.com/",
  //   color: "#B2184D",
  //   accountType: ResourceTypeMap.email,
  //   action: ['visit', 'comment', 'like', 'post', 'follow', 'repost', 'send_msg'],
  //   postConfig: [
  //     {
  //       type: 'text',
  //       maxLength: 1000,
  //       hideTitle: true,
  //       showWordLimit: true,
  //       chineseCount: 1,
  //       emojiCount: 2
  //     }
  //   ],
  //   commentConfig: [
  //     {
  //       type: 'text',
  //       maxLength: 1000
  //     }
  //   ],
  //   supportTask: ["create", "breed", "delivery"]
  // },
  plurk: {
    name: i18n.t('assets["Plurk"]'),
    url: "https://www.plurk.com/",
    color: "#FF574D",
    accountType: ResourceTypeMap.email,
    action: ['visit', 'comment', 'like', 'post', 'follow', 'repost', 'send_msg', 'add_friend'],
    postConfig: [
      {
        type: 'text',
        maxLength: 360,
        hideTitle: true,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 20
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 360
      }
    ],
    supportTask: ["create", "breed", "collect", "delivery"]
  },
  quora: {
    name: i18n.t('assets["Quora"]'),
    url: "https://www.quora.com/",
    color: "#B92B27",
    accountType: ResourceTypeMap.email,
    rule: {
      URL_REGEXP: {
        postUrl: {
          regexp: new RegExp('https://(.+\\.)*quora\\.com/.+')
        },
        commentUrl: {
          regexp: new RegExp('https://(.+\\.)*quora\\.com/.+')
        },
      }
    },
    action: ['visit', 'like', 'comment', 'post', 'follow', 'collect'],
    postConfig: [
      {
        type: 'text',
        maxLength: 50000,
        hideTitle: true,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 1
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 50000
      }
    ],
    supportTask: ["create", "breed", "delivery", "collect"]
  },
  rumble: {
    name: i18n.t('assets["Rumble"]'),
    url: "https://rumble.com/",
    color: "#80BC42",
    accountType: ResourceTypeMap.email,
    action: ['visit', 'comment', 'like', 'dislike', 'post', 'follow'],
    postConfig: [
      {
        type: 'text',
        maxLength: 50000,
        titleMinLength: 0,
        titleMaxLength: 100,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 1
      },
      {
        type: "video",
        size: calcMediaSize("15360MB"),
        required: true
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 2000
      }
    ],
    supportTask: ["create", "breed", "collect", "delivery"]
  },
  tumblr: {
    name: i18n.t('assets["Tumblr"]'),
    url: "https://www.tumblr.com/",
    color: "#314358",
    accountType: ResourceTypeMap.email,
    collectionResultTypes: ["article"],
    action: ['visit', 'comment', 'like', 'post', 'follow', 'repost', 'send_msg'],
    postConfig: [
      {
        type: 'text',
        maxLength: 4096,
        titleMinLength: 1,
        titleMaxLength: 4096,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 1
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 475
      }
    ],
    supportTask: ["create", "breed", "collect", "delivery"]
  },
  zhihu: {
    name: i18n.t('assets["知乎"]'),
    url: "https://www.zhihu.com/",
    color: "#026CF3",
    accountType: ResourceTypeMap.phone,
    rule: {
      URL_REGEXP: {
        postUrl: {
          regexp: new RegExp('https://(.+\\.)*zhihu\\.com/.+')
        },
        commentUrl: {
          regexp: new RegExp('https://(.+\\.)*zhihu\\.com/.+')
        },
      }
    },
    action: ['visit', 'comment', 'like', 'post', 'follow', 'repost', 'send_msg'],
    collectionType: ["hot", "keyword"],
    postConfig: [
      {
        type: 'text',
        maxLength: 50000,
        titleMinLength: 1,
        titleMaxLength: 100,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 5
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 5000
      }
    ],
    collectionResultTypes: ["article", "comment", "account"],
    supportTask: ["create", "breed", "collect", "delivery"],
    location: "中国",
  },
  douyin: {
    name: i18n.t('assets["抖音"]'),
    url: "https://www.douyin.com/",
    color: "#000000",
    accountType: ResourceTypeMap.phone,
    action: ["visit", 'like', 'dislike', 'comment', 'post', 'send_msg', 'follow', 'repost', 'collect'],
    collectionType: ["hot", "keyword"],
    postConfig: [
      {
        type: 'text',
        maxLength: 30000,
        titleMinLength: 1,
        titleMaxLength: 300,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 4
      }
    ],
    collectionResultTypes: ["article", "comment", "account"],
    supportTask: ["create", "breed", "collect", "delivery"],
    location: "中国",
  },
  kuaishou: {
    name: i18n.t('assets["快手"]'),
    url: "https://www.kuaishou.com/",
    color: "#FF4906",
    accountType: ResourceTypeMap.phone,
    action: ['visit', 'comment', 'like', 'post', 'follow', 'repost', 'send_msg'],
    collectionType: ["hot", "keyword"],
    postConfig: [
      {
        type: 'text',
        maxLength: 30000,
        titleMinLength: 1,
        titleMaxLength: 300,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 4
      }
    ],
    collectionResultTypes: ["article", "comment", "account"],
    supportTask: ["create", "breed", "collect", "delivery"],
    location: "中国",
  },
  // huoshan: {
  //   name: i18n.t('assets["火山视频"]'),
  //   url: "https://www.huoshan.com/",
  //   color: "#FF5709",
  //   accountType: ResourceTypeMap.phone,
  //   // action: ['visit','comment','like','post','follow','repost','send_msg'],
  //   collectionType: ["hot", "keyword"],
  //   postConfig: [
  //     {
  //       type: 'text',
  //       maxLength: 30000,
  //       titleMinLength: 1,
  //       titleMaxLength: 300,
  //       showWordLimit: true,
  //       chineseCount: 3,
  //       emojiCount: 4
  //     }
  //   ],
  //   collectionResultTypes: ["article", "comment", "account"],
  //   supportTask: ["collect"],
  //   location: "中国",
  // },
  // ixigua: {
  //   name: i18n.t('assets["西瓜视频"]'),
  //   url: "https://www.ixigua.com/",
  //   color: "#FA2840",
  //   accountType: ResourceTypeMap.phone,
  //   // action: ['visit','comment','like','post','follow','repost','send_msg'],
  //   collectionType: ["hot", "keyword"],
  //   postConfig: [
  //     {
  //       type: 'text',
  //       maxLength: 30000,
  //       titleMinLength: 1,
  //       titleMaxLength: 300,
  //       showWordLimit: true,
  //       chineseCount: 3,
  //       emojiCount: 4
  //     }
  //   ],
  //   collectionResultTypes: ["article", "comment", "account"],
  //   supportTask: ["collect"],
  //   location: "中国",
  // },
  // medium: {
  //   name: i18n.t('assets["medium"]'),
  //   url: "https://www.medium.com/",
  //   color: "#000000",
  //   accountType: ResourceTypeMap.email,
  //   action: ['visit', 'comment', 'like', 'post', 'collect'],
  //   collectionType: ["hot", "keyword"],
  //   postConfig: [
  //     {
  //       type: 'text',
  //       maxLength: 50000,
  //       titleMinLength: 1,
  //       titleMaxLength: 50000,
  //       showWordLimit: true,
  //       chineseCount: 1,
  //       emojiCount: 1
  //     },
  //     {
  //       type: "image",
  //       limit: 4,
  //       size: calcMediaSize("5MB"),

  //     },
  //   ],
  //   commentConfig: [
  //     {
  //       type: 'text',
  //       maxLength: 5000
  //     }
  //   ],
  //   collectionResultTypes: ["article", "comment", "account"],
  //   supportTask: ["create", "breed", "collect", "delivery"],
  // },
  snapchat: {
    name: i18n.t('assets["Snapchat"]'),
    url: "https://www.snapchat.com/",
    color: "#efe200",
    // accountType: ResourceTypeMap.phone,
    collectionResultTypes: ["article"]
  },
  douban: {
    name: i18n.t('assets["Douban"]'),
    url: "https://www.douban.com/",
    color: "#edf7e9",
    // accountType: ResourceTypeMap.phone,
    collectionResultTypes: ["article"]
  },
  daum: {
    name: i18n.t('assets["Daum"]'),
    url: "https://www.daum.net/",
    color: "#dee4ed",
    action: [
      "visit",
      "comment",
      "like",
      "follow"
    ],
    collectionType: ["hot"],
    postConfig: [
      {
        type: 'text',
        maxLength: 10000
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 1000
      }
    ],
    collectionResultTypes: ["article", "account", "comment"],
    supportTask: [],
    location: "韩国"
  },
  naver: {
    name: i18n.t('assets["Naver"]'),
    url: "https://www.naver.com/",
    color: "#04cf5c",
    action: [
      "visit",
      "comment",
      "repost",
      "like",
      "follow"
    ],
    collectionType: ["hot"],
    postConfig: [
      {
        type: 'text',
        maxLength: 10000
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 1000
      }
    ],
    collectionResultTypes: ["article"],
    supportTask: ["collect"],
    location: "韩国"
  },
  line: {
    name: i18n.t('assets["Line"]'),
    color: "#3aae36",
    collectionResultTypes: ["article"],
    accountType: ResourceTypeMap.phone,
    collectionType: ["group"],
    action: ['visit', 'comment', 'like', 'post', 'follow', 'repost', 'join', 'add_friend', 'deliver_message', 'vote','delete_friend','top_group_message','exit_group','new_group','like_msg'],//
    postConfig: [
      {
        type: "text",
        maxLength: 10000,
        hideTitle: true,
        chineseCount: 1,//中文及中文符号占字符数
        emojiCount: 11//表情占字符数
      },
    ],
    commentConfig: [
      {
        type: "text",
        maxLength: 200,
        hideTitle: true,
      },
    ],
    privateMsgConfig: ['text'],
    location: "韩国",
    supportTask: ["create", "breed", "collect", "delivery"],
    messageTargetType: "user",
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  telegram: {
    name: i18n.t('assets["Telegram"]'),
    color: "#2796D1",
    collectionResultTypes: ["article"],
    accountType: ResourceTypeMap.both,
    collectionType: ["group"],
    rule: {
      URL_REGEXP: {
        platformBasicUrl: {
          regexp: new RegExp('^(https?|ftp):\\/\\/(www.)?(t|telegram)\\.\\S+', 'iu')
        },
      }
    },
    action: ["add_friend", 'join', 'vote', 'deliver_message','delete_friend','exit_group','like_msg'],//
    postConfig: [],
    commentConfig: [],
    privateMsgConfig: ['text'],
    location: "越南",
    supportTask: ["create", "breed", "collect", "delivery"],
    messageTargetType: "user",
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  google: {
    name: i18n.t('assets["Google"]'),
    color: "#518ef8",
    collectionResultTypes: ["article"]
  },
  wechat: {
    name: i18n.t('assets["微信"]'),
    color: "#09bb07",
    collectionResultTypes: ["article"]
  },
  whatsapp: {
    name: i18n.t('assets["Whatsapp"]'),
    color: "#09bb07",
    collectionResultTypes: ["article"],
    accountType: ResourceTypeMap.phone,
    collectionType: ["group"],
    rule: {
      URL_REGEXP: {
        platformBasicUrl: {
          regexp: new RegExp('^(https?|ftp):\\/\\/(www.)?(wa|whatsapp)\\.\\S+', 'iu')
        }
      }
    },
    action: ["add_friend", 'join', 'post', 'deliver_message','vote','delete_friend','broadcast','view_friend_trend','send_channel_message','exit_group','new_group','new_community','like_msg'],
    postConfig: [
      {
        type: "text",
        maxLength: 150,
        hideTitle: true,
        chineseCount: 1,//中文及中文符号占字符数
        emojiCount: 1//表情占字符数
      },
      {
        type: "image",
        limit: 10,
        // suffix: ["jpg", "jpeg", "png"],
        size: calcMediaSize("10MB"),
      },
      {
        type: "video",
        size: calcMediaSize("500MB"),
        duration: calcMediaDuration("4小时"),

      }
    ],
    commentConfig: [],
    privateMsgConfig: ['text'],
    location: "越南",
    supportTask: ["create", "breed", "collect", "delivery"],
    messageTargetType: "user",
    accountLevel: {
      1: "0-6",
      2: "7-13",
      3: "14-20",
      4: "21-27",
      5: ">=28"
    }
  },
  mewe: {
    name: i18n.t('assets["MeWe"]'),
    url: "https://mewe.com/",
    color: "#4164ab",
    accountType: ResourceTypeMap.email,
    rule: {
      PROXY_COOL_DOWN: 1 * TimeConfig.day
    },
    action: ["visit", "post", "follow", 'comment'],
    postConfig: [
      {
        type: "image",
        limit: 50,
        size: calcMediaSize("500MB")
      },
      {
        type: "video",
        size: calcMediaSize("500MB")
      },
      {
        type: "text",
        hideTitle: true,
        maxLength: 50000,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 2
      }
    ],
    commentConfig: [
      {
        type: 'text',
        maxLength: 50000,
        supportSearchGif: true,
      }
    ],
    collectionType: ["user_list"],
    collectionResultTypes: ["article", "account"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-27",
      2: "27-41",
      3: "42-55",
      4: "56-70",
      5: ">=70"
    }
  },
  mobile01: {
    name: i18n.t('assets["mobile01"]'),
    url: "https://www.mobile01.com/",
    color: "#f99920",
    accountType: ResourceTypeMap.both,
    action: ["visit", "post", "comment", "like", "send_msg", "add_friend", 'callback_follow'],
    postConfig: [
      {
        type: 'text',
        maxLength: 30000,
        titleMinLength: 1,
        titleMaxLength: 300,
        showWordLimit: true,
        chineseCount: 3,
        emojiCount: 4
      }
    ],
    commentConfig: [{
      type: 'text',
      maxLength: 6000,
    }],
    privateMsgConfig: [
      {
        type: 'text',
        showTitle: true
      }
    ],
    collectionType: ["hot", "keyword"],
    collectionResultTypes: ["article", "comment", "account"],
    supportTask: ["create", "breed", "collect", "delivery"],
    location: "台湾",
    language: ["zt"]
  },
  gamer: {
    name: i18n.t('assets["巴哈姆特"]'),
    url: "https://www.gamer.com.tw/",
    color: "#f99920",
    accountType: ResourceTypeMap.email,
    action: ["visit", "post", "repost", "follow", "add_friend"],
    collectionType: ["hot", "keyword"],
    postConfig: [
      {
        type: 'text',
        hideTitle: true,
        maxLength: 3000,
        showWordLimit: true,
        chineseCount: 1,
        emojiCount: 3
      },
      'image'
    ],
    collectionResultTypes: ["article", "comment", "account"],
    supportTask: ["create", "breed", "collect", "delivery"],
    location: "台湾",
    language: ["zt"]
  },
  threads: {
    name: i18n.t('assets["Threads"]'),
    url: "https://www.threads.net/",
    color: "#1d2129",
    accountType: ResourceTypeMap.both,
    rule: {
      PROXY_MAX_SUCCESS_COUNT: 5,
      URL_REGEXP:{
        postUrl: {
          regexp: new RegExp('https://(\\w+\\.)?threads\\.net/@.+/post/.+')
        },
      }
    },
    action: ["visit", "post", "comment", "like", 'repost', "follow", 'callback_follow', 'delete_post'],
    postConfig: [
      {
        type: "text",
        hideTitle: true,
        maxLength: 500,
        chineseCount: 1,//中文及中文符号占字符数
        emojiCount: 1//表情占字符数
      },
      {
        type: "image",
        limit: 10,
        size: calcMediaSize("2MB"),
      },
      {
        type: "video",
        size: calcMediaSize("100MB"),
        duration: calcMediaDuration("5分钟"),

      }
    ],
    collectionType: ["user_list"],
    commentConfig: [
      {
        type: 'text',
        maxLength: 500
      }
    ],
    collectionResultTypes: ["article", "image", "video", "account"],
    location: "欧美",
    supportTask: ["create", "breed", "collect", "delivery"],
    accountLevel: {
      1: "0-13",
      2: "14-27",
      3: "28-41",
      4: "42-55",
      5: ">=56"
    }
  },
};

/* 日报隐藏的平台 */
export const DisabledTaskMap = {
  create: ["tistory", "nccu", "hinet"],
  breed: [],
  collect: []
};

/**
 * 平台名称 示例：{twitter: Twitter}
 */
export const PlatformMap = { news: "新闻网站" };

/**
 * 平台索引排序
 */
export const PlatformIdxMap = {};

/**
 * 平台采集结果类型
 */
export const PlatformResMap = {};

/**
 * 平台映射数组
 * - 示例：[{label: 'Twitter', value: 'twitter'}]
 */
export const PlatformOptions = [];

// 排除的平台
const __excludePlatform = ['odnoklassniki', 'tumblr', 'snapchat', 'douban', 'naver', 'daum']

export const PlatformOptionList = [];

export const PlatformSourceList = [];



/**
 * 平台账号类型
 * - 0 - 电话
 * - 1 - 邮箱
 */
export const SocialAccountType = {};

/**
 * 平台 投送行为 评论字数最大值
 */
export const CommentMaxLengthMap = { default: 10000 };

/**
 * 平台 投送行为 评论字数最小值
 */
export const CommentMinLengthMap = { default: 1 };

/**
 * 平台 投送行为 评论配置
 */
export const CommentConfigMap = {};

/**
 * 平台 投送行为 加好友配置
 */
export const addFriendsConfigMap = {};

/**
 * 平台 投送行为 私信配置
 */
export const PrivateMsgConfigMap = {};

/**
 * 投送行为表
 */
export const SocialActionMap = {};

/**
 * 自动任务 各平台支持采集类型
 */
export const CollectionType = {};

/**
 * 各平台支持的采集目标类型
 */
export const PlatformCollectionType = {};
/**
 * 平台 代理/人设 使用限制
 */
export const PlatformRuleMap = {};

/**
 * 平台-可发帖，图片
 */
export const PlatformPostTypeImage = [];

/**
 * 平台-可发帖，视频
 */
export const PlatformPostTypeVideo = [];

/**
 * 平台投送 目标人物 投送方式
 */
const messageTarget = {
  default: { type: "url", label: "主页的url地址" },
  phone: { type: "phone", label: "手机号" },
  user: { type: "user", label: " " }
};
export const MessageTargetMap = {};

/**
 * 平台投送 发帖配置
 */
export const PostConfigMap = {};
Object.keys(PlatformFeatures).forEach(item => {
  const itemInfo = PlatformFeatures[item];
  PlatformOptionList.push(item)
  // 目标人物 投送方式
  MessageTargetMap[item] = itemInfo.messageTargetType
    ? messageTarget[itemInfo.messageTargetType]
    : messageTarget.default;

  if (itemInfo.postConfig && itemInfo.postConfig.length) {
    itemInfo.postConfig.forEach(it => {
      if (it.type === 'image') {
        PlatformPostTypeImage.push({ label: itemInfo.name, value: item })
      } else if (it.type === 'video') {
        PlatformPostTypeVideo.push({ label: itemInfo.name, value: item })
      }
    })
  }

  // 平台名称
  PlatformMap[item] = itemInfo.name;

  // 平台索引排序
  PlatformIdxMap[item] = itemInfo.sortIdx || 999;

  // 平台采集结果
  PlatformResMap[item] = itemInfo.collectionResultTypes || ["article"];

  // 平台下拉列表
  if (!__excludePlatform.includes(item)) {
    PlatformOptions.push({ label: itemInfo.name, value: item, ...itemInfo });
  }

  //文章来源下拉列表
  if (itemInfo.supportTask && itemInfo.supportTask.includes('collect')) {
    PlatformSourceList.push({ label: itemInfo.name, value: [item] })
  }
  // 平台账号类型
  const targetType = itemInfo.accountType;
  if (typeof targetType === "number") {
    SocialAccountType[item] = targetType;
  }

  // 投送行为分类汇总
  const actionList = itemInfo.action;
  if (actionList) {
    actionList.forEach(action => {
      if (!SocialActionMap[action]) {
        SocialActionMap[action] = [item];
      } else {
        SocialActionMap[action].push(item);
      }
    });
  }

  // 采集目标分类汇总
  const typeList = itemInfo.collectionType;
  PlatformCollectionType[item] = [];
  if (typeList) {
    typeList.forEach(type => {
      let typeName = "";
      if (typeof type === "string") {
        typeName = type;
        if (type !== "hot") {
          PlatformCollectionType[item].push(type);
        }
      } else {
        typeName = type.type;
        PlatformCollectionType[item].push(type.type);
      }
      if (!CollectionType[typeName]) {
        CollectionType[typeName] = [item];
      } else {
        CollectionType[typeName].push(item);
      }
    });
  }

  // 平台 代理/人设 使用限制
  const defaultRule = {
    PROXY_MAX_SUCCESS_COUNT: 1,
    PROXY_MAX_FAIL_COUNT: 5,
    PROXY_AVAILABLE: 60,
    PROXY_COOL_DOWN: 30 * TimeConfig.minute,
    PERSON_COOL_DOWN: 1 * TimeConfig.day,
    URL_REGEXP: {
      platformBasicUrl: {
        regexp: new RegExp('^(https?|ftp):\\/\\/(\\w+\\.)?' + item + '\\.\\S+', 'iu'),
        err: '非该平台URL格式'
      },
      visitUrl: {
        err: 'url不符合' + item + '平台访问的格式'
      },
      postUrl: {
        err: 'url不符合' + item + '平台目标帖子的格式'
      },
      userUrl: {
        err: 'url不符合' + item + '平台目标人物的格式'
      },
      groupUrl: {
        err: 'url不符合' + item + '平台目标群的格式'
      },
      personUrl: {
        err: 'url不符合' + item + '平台个人主页的格式'
      },
      publicPageUrl: {
        err: 'url不符合' + item + '平台公共主页的格式'
      },
      commentUrl: {
        err: 'url不符合' + item + '平台目标评论的格式'
      },
      postForumUrl: {
        err: 'url不符合' + item + '平台，发帖目标板块的格式'
      },
      commentForumUrl: {
        err: 'url不符合' + item + '平台，评论目标板块的格式'
      },
      answerUrl: {
        err: 'url不符合' + item + '平台目标回答的格式'
      },
      communityUrl: {
        err: 'url不符合' + item + '平台目标社群的格式'
      },
      channelUrl: {
        err: 'url不符合' + item + '平台目标频道的格式'
      },
      messagelUrl: {
        err: 'url不符合' + item + '平台目标消息的格式'
      },
    }
  };
  PlatformRuleMap[item] = merge(defaultRule, itemInfo.rule || {});

  // // 发帖配置
  __mergeDefaultConfig(item, 'postConfig');

  // // 评论配置
  __mergeDefaultConfig(item, 'commentConfig')

  // 私信配置
  __mergeDefaultConfig(item, 'privateMsgConfig');

  /* 加好友配置 */
  __mergeDefaultConfig(item, 'addFriendsConfig');


});
// 将每个平台自定义的配置与默认配置合并
function __mergeDefaultConfig(item, type) {
  const itemInfo = PlatformFeatures[item];
  let defaultConfig, resultMap;
  switch (type) {
    case 'postConfig':
      defaultConfig = Object.assign({}, DefaultPostConfig);
      resultMap = PostConfigMap;
      break;
    case 'commentConfig':
      defaultConfig = Object.assign({}, DefaultCommentConfig);
      resultMap = CommentConfigMap;
      break;
    case 'privateMsgConfig':
      defaultConfig = Object.assign({}, DefaultPrivateMsgConfig);
      resultMap = PrivateMsgConfigMap;
      break;
    case 'addFriendsConfig':
      defaultConfig = Object.assign({}, DefaultaddFriendsConfig);
      resultMap = addFriendsConfigMap;
      break;
  }
  const computeConfig = itemInfo[type];
  const mFormatter = (type, prop, value, root) => {
    switch (prop) {
      case "maxLength":
      case "limit":
      case "size":
        return Math.max(value, defaultConfig[type][prop]);
      case "minLength":
        return Math.max(value, defaultConfig[type][prop])
      case "suffix":
        root.suffixTip = value.join(",");
        return value;
      default:
        return value;
    }
  };

  if (computeConfig) {
    resultMap[item] = {};
    computeConfig.forEach(config => {
      const isString = typeof config === 'string';
      const prop = isString ? config : config.type;
      if (isString) {
        resultMap[item][prop] = {
          ...defaultConfig[prop]
        };
        return;
      }

      resultMap[item][prop] = {
        ...defaultConfig[prop]
      };
      Object.keys(config).forEach(it => {
        resultMap[item][prop][it] = mFormatter(
          prop,
          it,
          config[it],
          resultMap[item][prop]
        );
      });
    });
  }

}

/**
 * 平台映射数组，附带新闻网站
 * - 示例：[{label: 'Twitter', value: 'twitter'}, {label: '新闻网站', value: 'news'}]
 */
export const PlayformWithNewsOptions = [
  ...PlatformOptions,
  {
    label: "新闻网站",
    value: "news"
  }
];

/**
 * 全量平台所含地区
 */
export const PlayformLocations = [];
Object.keys(PlatformFeatures).forEach(item => {
  const location = PlatformFeatures[item]["location"];
  if (location && !PlayformLocations.includes(location)) {
    PlayformLocations.push(location);
  }
});

/**
 * 判断平台是否在社交平台中
 * @param {*} platform
 */
export function isPlatform(platform) {
  return !!PlatformMap[platform];
}

export const zxPlatformList = [
  {
    name: 'facebook',
    label: i18n.t('assets["Facebook"]')
  },
  {
    name: 'twitter',
    label: i18n.t('assets["Twitter"]')
  },
  {
    name: 'threads',
    label: i18n.t('assets["Threads"]')
  },
  {
    name: 'instagram',
    label: i18n.t('assets["Ins"]')
  },
  {
    name: 'tiktok',
    label: i18n.t('assets["Tiktok"]')
  }
];

