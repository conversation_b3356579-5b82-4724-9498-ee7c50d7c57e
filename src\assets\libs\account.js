// 账号相关映射
import i18n from '@/lang'
/**
 * 活跃度数组
 */
export const ActivityLevelOptions = [
  {
    label:  i18n.t('assets["一级"]'),
    value: 1
  },
  {
    label:  i18n.t('assets["二级"]'),
    value: 2
  },
  {
    label:  i18n.t('assets["三级"]'),
    value: 3
  },
  {
    label:  i18n.t('assets["四级"]'),
    value: 4
  },
  {
    label:  i18n.t('assets["五级"]'),
    value: 5
  }
];

export const ActivityNameMap = {};
ActivityLevelOptions.forEach(item => {
  ActivityNameMap[item.value] = item.label;
});

/**
 * 账号状态数组
 */
export const AccountStatusOptions = [
  {
    label:  i18n.t('assets["正常"]'),
    value: 0,
    prop: "normal",
    color: "#0fd2be"
  },
  {
    label:  i18n.t('assets["被封"]'),
    value: 3,
    prop: "forbidden",
    color: "#ea5a73"
  },
  {
    label:  i18n.t('assets["维护中"]'),
    value: 4,
    prop: "maintain",
    color: "#f9a946"
  },
  {
    label:  i18n.t('assets["已导出"]'),
    value: 11,
    prop: "export",
    color: "#76b1fa"
  },
  {
    label:  i18n.t('assets["已销号"]'),
    value: 1,
    prop: "sales",
    color: "#B2BBCA"
  }
];

// 活跃版块加入进度
export const forumProgressStatus = [
  {
    label:  i18n.t('assets["待加入"]'),
    value: -1
  },
  {
    label:  i18n.t('assets["加入中"]'),
    value: 0
  },
  {
    label:  i18n.t('assets["加入失败"]'),
    value: 1,
  },
  {
    label:  i18n.t('assets["加入成功"]'),
    value: 2,
  }
]


//展示状态数组
export const AccountStatusOptionsList = [
  {
    label: i18n.t('assets["等待培育"]'),
    value: 0,
    prop: "normal",
    color: "#abb8cd"
  },
  {
    label: i18n.t('assets["培育中"]'),
    value: 2,
    prop: "running",
    color: "#3569e7"
  },
  {
    label: i18n.t('assets["培育完成"]'),
    value: 3,
    prop: "complete",
    color: "#0fd2be"
  },
  {
    label:i18n.t('assets["培育失败"]'),
    value: 4,
    prop: "fail",
    color: "#ff274b"
  },
];

//展示状态数组
export const relationTypeList = [
  {
    label: i18n.t('assets["粉丝"]'),
    value: 'followerCount',
    color: "#76b1fa"
  },
  {
    label: i18n.t('assets["相似人员"]'),
    value: 'userSimilarCount',
    color: "#f9a946"
  },
];
/**
 * 账号状态映射
 */
export const AccountStatusMap = {};
AccountStatusOptions.forEach(item => {
  AccountStatusMap[item.prop] = item.value;
});

/**
 * 导入账号状态映射
 */
export const ImportedAccountStatusMap = {
  normal: [AccountStatusMap.normal],
  banned: [3]
};

/**
 * 公共主页开通状态列表
 */
export const AccountPublicPageStatusOptions = [
  {
    prop: "init",
    label:i18n.t('assets["未开通"]'),
    value: 0
  },
  {
    prop: "success",
    label:i18n.t('assets["开通成功"]'),
    value: 1
  },
  {
    prop: "pendding",
    label:i18n.t('assets["开通中"]'),
    value: 2
  },
  {
    prop: "fail",
    label: i18n.t('assets["开通失败"]'),
    value: 3
  }
];

/**
 * 公共主页开通状态映射表
 */
export const AccountPublicPageStatusMap = {};
AccountPublicPageStatusOptions.forEach(item => {
  AccountPublicPageStatusMap[item.prop] = item.value;
});
