<template>
  <titan-dialog
    :title="$t('components. 培育标签管理')"
    :visible.sync="show"
    width="80%"
    :config="{
      showCancel: false
    }"
    @open="handleOpen"
    @close="handleClose"
  >
    <tags
      height="580px"
      :primary="primary"
      :is-manager="false"
    />
  </titan-dialog>
</template>

<script>
import Tags from '@/views/data-center/pages/tags/pages/module';
import { mapActions } from 'vuex';

export default {
  name: 'SysTagManager',
  components: { Tags },
  props: {
    primary: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    show: {
      set(val) {
        this.$emit('update:visible', val);
      },
      get() {
        return this.visible;
      }
    }
  },
  methods: {
    ...mapActions('tag', ['refreshTagStore']),
    handleOpen() {},
    handleClose() {
      this.refreshTagStore(this.primary);
    }
  }
};
</script>

<style scoped lang="scss">
.titan-dialog{
  ::v-deep .el-dialog__body{
    padding-top: 0;
  }
}
.common__wrapper{
  height: 600px;
  ::v-deep .common-main__main{
    padding: 0;
  }
}</style>
