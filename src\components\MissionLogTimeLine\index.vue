<template>
  <div class="log-timeline-container">
    <div
      v-if="lastTimeTxt"
      class="top-fix-warp"
    >
      <div>
        <div class="category">
          {{ lastTimeTxt }}
        </div>
        <span>
          {{ $t('components. 原子任务数') }}：{{ taskTotalCount }}
        </span>
      </div>
      <div class="right-box">
        <titan-select
          v-model="taskStatus"
          clearable
          style="width: 200px;margin-right:10px"
          :placeholder="$t('components. 请选择任务状态')"
          :config="{
            dataSource: missionStatusList,
            itemLabel: 'label',
            itemValue: 'value'
          }"
        />
        <el-date-picker
          v-model="recordStart"
          type="date"
          style="width:160px;margin-right:10px"
          value-format="timestamp"
          :picker-options="dateOptions"
          :placeholder="$t('components. 请选择开始日期')"
          @Change="handleStartTimeChange"
        />
        <el-date-picker
          v-model="recordEnd"
          type="date"
          style="width:160px;margin-right:10px"
          value-format="timestamp"
          :picker-options="dateOptions"
          :placeholder="$t('components. 请选择结束日期')"
          @Change="handleEndTimeChange"
        />
        <el-cascader
          v-model="device"
          :options="allDeviceType"
          clearable
          :placeholder="$t('components. 请选择设备类型')"
          :show-all-levels="false"
          :collapse-tags="true"
          :props="{
            expandTrigger: 'click',
            multiple: true,
            value: 'code',
            label: 'name'
          }"
        />
      </div>
    </div>
    <div
      v-for="(item, index) in list"
      :key="index"
      class="log-time"
    >
      <div
        v-if="item.showCategory"
        class="category"
      >
        {{ item.category }}
      </div>
      <div class="log-time-inner">
        <div
          class="icon-status-warp"
          :style="taskStatusIcon(item)['style']"
        >
          <i :class="taskStatusIcon(item)['icon']" />
        </div>
        <div class="status-warp">
          <span :style="styFunc(item)">{{ textFunc(item) }}</span>

          <!-- 只有策略任务才显示完成度，策略任务没有independentTask-->
          <span
            v-if="!independentTask"
            :style="completeFunc(item).sty"
          >
            {{ $t('components. 完成度') }}：{{ completeFunc(item).txt }}
          </span>
          <span v-if="item.logTime"> {{ item.logTime | formatTime('fmt:Hs') }}</span>
        </div>
        <p class="msg">
          {{ item.message }}
        </p>
        <div class="form-box">
          <div class="form-box-item w200">
            <span>{{ $t('components. 账号名称') }}</span>
            <el-tooltip
              :content="item.accountName"
              :disabled="!item.accountName"
              placement="top"
              effect="light"
            >
              <span
                style="cursor: pointer;"
                @click="handleClickAccount(item)"
              >{{ item.accountName || '--' }}</span>
            </el-tooltip>
          </div>
          <div class="form-box-item">
            <span>{{ $t('components.账号ID') }}</span>
            <el-tooltip
              :content="item.accountId"
              :disabled="!item.accountId"
              placement="top"
              effect="light"
            >
              <span>{{ item.accountId || '--' }}</span>
            </el-tooltip>
          </div>
          <div class="form-box-item w200">
            <span>{{ $t('components. 设备类型') }}</span>
            <el-tooltip
              :content="item.webRegLabel"
              :disabled="!item.webRegLabel"
              placement="top"
              effect="light"
            >
              <span>{{ item.webRegLabel || '--' }}</span>
            </el-tooltip>
          </div>
          <div class="form-box-item">
            <span>{{ $t('components. 账号代理') }}</span>
            <el-tooltip
              :content="item.hashValue"
              :disabled="!item.hashValue"
              placement="top"
              effect="light"
            >
              <span>{{ item.hashValue || '--' }}</span>
            </el-tooltip>
          </div>
          <div class="form-box-item longer">
            <span>{{ $t('components. 账号最近使用IP') }}</span>
            <el-tooltip
              :content="item.proxyIp"
              :disabled="!item.proxyIp"
              placement="top"
              effect="light"
            >
              <span>{{ item.proxyIp || '--' }}</span>
            </el-tooltip>
          </div>
        </div>
        <div class="step-flow">
          <step-card
            v-for="(one , o) in item.stepMapList"
            :key="o"
            :step-name="one.name"
            :step="one.step"
            :time="one.time"
            :status="stepStatusFunc(item,one)"
            :description="one.msg"
            :error-stack="one.error_stack"
          />
        </div>
        <el-collapse v-if="item.logList && item.logList.length > 0">
          <el-collapse-item
            :title="$t('components. 查看任务日志')"
            name="1"
          >
            <div
              v-for="(log, l) in item.logList"
              :key="l"
            >
              <div class="header-detail">
                {{ log.date | formatTime('fmt:Hs') }}
                <b>{{ log.actionType }}</b>
                <a
                  :href="log.url"
                  target="_blank"
                >
                  {{ log.url }}
                </a>
                <span
                  :style="{
                    color: formatLogItemColor(log)
                  }"
                >
                  {{ colorSty[log.status]['label'] }}
                  {{ log.errorMsg }}
                </span>
                <el-image
                  v-if="log.executeResultPic"
                  :src="log.executeResultPic"
                  :preview-src-list="item.imgList"
                />
              </div>
              <tasl-detail-item
                v-for="(gatherDataItem,gatherDataIndex) in getGatherData({ ...log,...item })"
                :key="gatherDataIndex+gatherDataItem.data_type"
                :detail="gatherDataItem"
              />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <account-info
      :visible.sync="showUserInfo"
      :user-id="currentAccount.id"
      :user-data="currentAccount"
    />
  </div>
</template>

<script>
import { formatTime } from '@/utils/time-utils';
import { STEP_DICT, MissionStatusListMap, ActionMap } from '@/assets/libs/mission';
import StepCard from './components/StepCard';
import { MissionAPI } from '@/api/modules/mission';
import dayjs from 'dayjs';
import TaslDetailItem from '@/views/account-center/pages/virtual/pages/sys-account/components/LogCompoents/components/TaslDetailItem.vue';
import { getObjectValue, isEmpty } from '@/utils';
import AccountInfo from '@/views/account-center/pages/virtual/components/AccountInfo';

export default {
  name: 'LogCardTimeline',
  components: { StepCard, TaslDetailItem, AccountInfo },
  props: {
    result: {
      type: Array,
      default: () => []
    },
    platform: {
      type: String,
      default: ''
    },
    webRegs: {
      type: Array,
      default: () => []
    },
    registerTypeList: {
      type: Array,
      default: () => []
    },
    lastTimeTxt: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: ''
    },
    startTime: {
      type: [String, Number],
      default: null
    },
    endTime: {
      type: [String, Number],
      default: null
    },
    taskTotalCount: {
      type: Number,
      default: 0
    },
    errorCodeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showUserInfo: false,
      currentAccount: {},
      allDeviceType: [],
      dateOptions: { disabledDate: time => time.getTime() > new Date() },
      missionStatusList: [
        {
          'label': this.$t('components[" 待处理"]'),
          'value': 'CREATE'
        },
        {
          'label': this.$t('components[" 进行中"]'),
          'value': 'RUN'
        },
        {
          'label': this.$t('components["已完成"]'),
          'value': 'SUCCESS'
        },
        {
          'label': this.$t('components[" 异常"]'),
          'value': 'FAILURE'
        }
      ]
    };
  },
  computed: {
    colorSty() {
      const obj = {};
      MissionStatusListMap.forEach(item => {
        obj[item.code] = item;
      });
      return obj;
    },
    list() {
      const categoryMap = {};
      return this.result.map(item => {
        const category = formatTime(item.logTime, 'fmt:YD');
        if (!categoryMap[category]) {
          categoryMap[category] = 1;
          item.showCategory = true;
        } else {
          item.showCategory = false;
        }

        const stepMapList = (item.steps || []).map(one => {
          let _info;
          if (!_.isEmpty(one.info) && _.isString(one.info)) {
            _info = JSON.parse(one.info);
          } else {
            _info = {};
          }
          return {
            name: STEP_DICT[one.step.toUpperCase()],
            step: one.step.toLowerCase(),
            time: one.logTime,
            status: one.stepStatus,
            msg: one.message,
            error_stack: _info.error_stack || null
          };
        });
        const webRegLabel = this.registerTypeList.find(it => it.code === item.webReg)?.name || '--';
        const imgList = !_.isArray(item.logList) ? [] : item.logList.filter(a => a.executeResultPic).map(a => a.executeResultPic);
        return {
          ...item,
          category,
          stepMapList,
          webRegLabel,
          imgList
        };
      });
    },
    breedAction() {
      const obj = {};
      Object.keys(ActionMap).forEach(key => {
        obj[key.toUpperCase()] = `【${ActionMap[key]}】${this.$t('components["任务"]')}`;
      });
      return obj;
    },
    independentTask() {
      return this.$route.query?.independent;
    },
    device: {
      set(val) {
        this.$emit('update:webRegs', val);
      },
      get() {
        return this.webRegs;
      }
    },
    taskStatus: {
      set(val) {
        this.$emit('update:status', val);
      },
      get() {
        return this.status;
      }
    },
    recordStart: {
      set(val) {
        this.$emit('update:startTime', val);
      },
      get() {
        return this.startTime;
      }
    },
    recordEnd: {
      set(val) {
        this.$emit('update:endTime', val);
      },
      get() {
        return this.endTime;
      }
    }
  },
  watch: {
    list() {
      this.$nextTick(() => {
        // 如果是首次渲染
        if (!this.lastTimeTxt) {
          const _lastTimeTxt = this?.list[0]?.category;
          if (_lastTimeTxt) this.$emit('update:lastTimeTxt', _lastTimeTxt);
          this.listenListScroll();
        }
      });
    }
  },
  created() {
    this.getAllDeviceType();
  },
  methods: {
    listenListScroll() {
      const self = this;
      const __getLastTimeNode = (nodeList, index = 0) => {
        const el = nodeList[index];
        if (!el) return nodeList[index - 1]; // 当前元素为空, 则返回上一个元素
        const next_el = nodeList[index + 1]; // 下一个元素为空, 则返回当前的元素
        if (!next_el) return nodeList[index];

        const { top } = next_el.getBoundingClientRect();
        if (top < 150) {
          index += 1;
          return __getLastTimeNode(nodeList, index);
        } else {
          return el;
        }
      };
      const listEl = document.querySelector('.log-container');
      const listener = _.debounce(function() {
        const timeNodeList = document.querySelectorAll('.category');
        const timeNode = __getLastTimeNode(timeNodeList);
        self.$emit('update:lastTimeTxt', timeNode.innerText);
      }, 200);
      listEl.removeEventListener('scroll', listener);
      listEl.addEventListener('scroll', listener);
    },
    taskStatusIcon(item) {
      const allValuesIncluded = this.getAllValuesIncluded(item);
      const one = this.colorSty[item.status] || {};
      if (allValuesIncluded) {
        return {
          icon: 'fa ' + one.icon,
          style: {
            backgroundColor: '#f7c531',
            color: '#FFFFFF'
          }
        };
      } else {
        return {
          icon: 'fa ' + one.icon,
          style: {
            backgroundColor: one.borderColor,
            color: '#FFFFFF'
          }
        };
      }
    },
    formatLogItemColor(log) {
      if (this.errorCodeList.includes(log.errorCodeParentType)) {
        return '#f7c531';
      }
      return this.colorSty[log.status]['borderColor'];
    },
    styFunc(item) {
      const allValuesIncluded = this.getAllValuesIncluded(item);
      const sty = this.colorSty[item.status] || {};
      if (allValuesIncluded) {
        return {
          backgroundColor: '#f7c531',
          color: '#FFFFFF'
        };
      } else {
        return {
          backgroundColor: sty['borderColor'],
          color: '#FFFFFF'
        };
      }
    },
    textFunc(item) {
      const taskLabel = (this.breedAction || {})[item.actionType];
      const statusLabel = (this.colorSty[item.status] || {})['label'];

      return `${taskLabel}，${statusLabel}：${item.successCount}/${item.count}`;
    },
    completeFunc(item) {
      const dict = {
        'PASS': {
          txt: this.$t('components[" 合格"]'),
          sty: {
            color: '#53cbbf',
            borderColor: '#53cbbf'
          }
        },
        'NO_PASS': {
          txt: this.$t('components[" 不合格"]'),
          sty: {
            color: '#ff274b',
            borderColor: '#ff274b'
          }
        },
        'PENDING': {
          txt: this.$t('components[" 待计算"]'),
          sty: {
            color: '#989898',
            borderColor: '#989898'
          }
        }
      };
      return dict[item.completionDegree];
    },

    /* 特殊code展示判断颜色 */
    getAllValuesIncluded(item) {
      return item.logList.filter(a => a.status === 'FAILURE').length !== 0 && item.logList.filter(a => a.status === 'FAILURE').every(it => this.errorCodeList.includes(it.errorCodeParentType));
    },
    stepStatusFunc(item, one) {
      const allValuesIncluded = this.getAllValuesIncluded(item);
      if (allValuesIncluded && one.step === 'do_job') {
        return 'PAUSED';
      } else {
        return one.status;
      }
    },

    /* 获取所有的注册设备树结构 */
    async getAllDeviceType() {
      const resp = await MissionAPI.getAllDeviceType({ allDevices: true });
      this.allDeviceType = resp.data.map(item => {
        if (item.name === this.$t('components[" 浏览器"]')) {
          item.code = [0, 6];
          item.children = null;
        }
        return item;
      });
    },
    handleStartTimeChange(val) {
      if (val && !this.recordEnd) {
        return;
      } else if (new Date(val).getTime() > new Date(this.recordEnd).getTime()) {
        this.$message.error(this.$t('components[" 请选择小于结束时间的时间"]'));
        this.recordStart = new Date('2020-01-01').getTime();
      }
    },
    handleEndTimeChange(val) {
      if (this.recordStart && !val) {
        return;
      } else if (new Date(val).getTime() < new Date(this.recordStart).getTime()) {
        this.$message.error(this.$t('components[" 请选择大于开始时间的时间"]'));
        this.recordEnd = +dayjs().endOf('day');
      }
    },
    getGatherData(val) {
      if (val?.info) {
        const info = JSON.parse(val?.info);
        if (getObjectValue(info, 'gatherData.gatherData')) {
          if (val.actionType === 'SEND_MSG') {
            return JSON.parse(getObjectValue(info, 'gatherData.gatherData')).filter((item, index) => index === 0);
          } else {
            return JSON.parse(getObjectValue(info, 'gatherData.gatherData'));
          }
        } else {
          return [];
        }
      } else {
        return [];
      }
    },
    handleClickAccount(item) {
      this.currentAccount = item;
      this.currentAccount.id = item.accountId;
      this.showUserInfo = true;
    }
  }
};
</script>

<style lang="scss" scoped>

.log-timeline-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  font-size: 14px;
  .category{
    position: relative;
    display: inline-block;
    width: 127px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #e0f0fc;
    border-radius: 14px;
    color: #3569e7;
    flex: 0 0 auto;
    margin-right: 10px;
    &::after {
      content: '';
      position: absolute;
      right: 2px;
      bottom: -5px;
      width: 0;
      height: 0;
      border-top: 14px solid transparent;
      border-right: 14px solid #e0f0fc;
      border-bottom: 14px solid transparent;
      border-left: 14px solid transparent;
    }
  }

  .log-time{
    margin-bottom: 30px;
    &-inner{
      position: relative;
      border-left: dashed 1px #dcdfe6;
      padding-left: 20px;
      margin: 20px 0 0 10px;
      .icon-status-warp{
        position: absolute;
        top: 0;
        left: -12px;
        width: 24px;
        height: 24px;
        line-height: 24px;
        border-radius: 24px;
        text-align: center;
        font-size: 16px;
      }
      .status-warp{
        height: 24px;
        line-height: 24px;
        border-radius: 3px;
        box-sizing: border-box;
        span{
          display: inline-block;

          &:first-child{
            padding-right: 10px;
            padding-left: 2px;
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
          }
          &:nth-child(2){
            padding: 0 10px;
            border: 1px solid;
            line-height: 22px;
            border-left: none;
          }
          &:last-child{
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
            border: solid 1px #dde0e7;
            line-height: 22px;
            background: #FFFFFF;
            color: #606266;
            padding: 0 10px;
            margin: auto;
            border-left: none;
          }
        }
      }
      .msg{
        color: #606266;
        min-height: 20px;
        line-height: 20px;
        padding: 7px 0;
      }
      .form-box{
        display: flex;
        justify-content: space-between;

        &-item{
          color: #606266;
          white-space: nowrap;
          >span{
            display: inline-block;
            height: 24px;
            line-height: 24px;
            padding: 0 8px;
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
            vertical-align: middle;
            &:first-child{
              background: #d6dde7;
              width: 75px;
            }
            &:last-child{
              width: 155px;
              border: solid 1px #dde0e7;
              border-left: 0;
              border-radius: 0 3px 3px 0;
              height: 24px;
              line-height: 24px;
              @include utils-ellipsis();
            }
          }
          &.longer{
            >span{
              &:first-child{
                width: 110px;
              }
            }
          }
        }
        .w200{
          width: 180px;
          >span{
            &:last-child{
              width: 115px;
            }
          }
        }
      }
      .step-flow{
        display: flex;
        border: solid 1px #dde0e7;
        padding: 4px 10px;
        flex-wrap: wrap;
        border-radius: 3px;
        margin: 8px 0 0;
      }
      ::v-deep .el-collapse{
        border: none;
        .el-collapse-item__header{
          border: none;
          height: 30px;
          line-height: 30px;
          color: #3569e7;
          font-size: 14px;
          background-color: rgba(255, 255, 255, 0.25);
        }
        .el-collapse-item__arrow{
          position: absolute;
          left: 110px;
        }
        .el-collapse-item__wrap{
          background-color: #f7fafd;
          border-radius: 8px;
          border: solid 1px #c2d0e2;
          color: #606266;
          padding: 5px 10px;
          .el-collapse-item__content{
            padding-bottom: 0;
            overflow: auto;
            &>div{
              display: flex;
              flex-direction: column;
              border-bottom: 1px solid #d6dde7;
              margin-bottom: 10px;
              padding-bottom: 10px;
             .header-detail{
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              b{
                margin: 0 5px;
              }
              a{
                color: #2c94fc;
                text-decoration: underline;
                margin-right: 5px;
                max-width: 80%;
                display: inline-block;
                @include utils-ellipsis(1);
              }
              &>span{
                display: inline-block;
                max-width: calc(100% - 60px);
              }
              .el-image{
                width: 50px;
                height: 50px;
                margin-left: auto;
                align-self: flex-start;
              }
             }
            }

          }
        }
      }
    }
  }
  .top-fix-warp{
    position: absolute;
    top: 92px;
    z-index: 2;
    background: #f0f6fc;
    margin-left: -20px;
    padding: 10px 20px;
    width: calc(100% - 668px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right-box{
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    .el-cascader{
      float: right;
    }
  }
}
</style>
