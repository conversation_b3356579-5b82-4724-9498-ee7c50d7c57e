<template>
  <div class="card-box">
    <div
      v-if="titleIsArray"
      class="title"
    >
      <el-tabs
        v-model="selectedTab"
        class="tabs"
      >
        <el-tab-pane
          v-for="item in title"
          :key="item"
          :label="item"
          :name="item"
        />
      </el-tabs>
    </div>
    <div
      v-else
      class="title"
    >
      {{ title }}
    </div>
    <div class="content-box">
      <span class="left-top" />
      <span class="left-bottom" />
      <span class="right-bottom" />
      <span class="right-top" />
      <div class="conten-main">
        <slot name="content" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TCard',
  props: { title: { type: String || Array } },
  data() {
    return { selectedTab: '' };
  },
  computed: {
    titleIsArray() {
      return Array.isArray(this.title);
    }
  },
  watch: {
    selectedTab: {
      handler(val) {
        this.$emit('change', val);
      }
    }
  },
  created() {
    this.selectedTab = this.title[0];
  }
};
</script>

<style lang="scss" scoped>
$color-pale: #a9d0f5;
$color-bright: #e1f0ff;
$color-light-blue: #73ceff;
$color-border-line: #2e455a;
$color-card-bg: #1e2f41;
::v-deep .el-tabs {
  .el-tabs__header {
    margin: 0;
    .el-tabs__item {
      font-size: 14px;
      height: 32px;
      line-height: 32px;
      color: $color-pale;
      &:first-child {
        padding: 0 10px 0 0;
      }
      &:last-child {
        padding: 0;
      }
    }
    .is-active {
      color: $color-bright;
    }
    .el-tabs__active-bar,
    .el-tabs__nav-wrap::after {
      bottom: 0px;
      background-color: #ffa22e;
    }
    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }
  }
}
.card-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .title {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;
    margin-bottom: 3px;
    padding: 0 5px;
    background-image: url("~@/assets/titleBg.png");
    color: #84dcff;
    box-sizing: border-box;
    padding-left:15px;
  }
  .content-box {
    width: 100%;
    // height: 100%;
    flex: 1;
    border: solid 1px #305263;
    box-sizing: border-box;
    position: relative;
    padding: 17px 10px;
    .conten-main {
      width: 100%;
      height: calc(100% - 30px);
      // overflow-y: scroll;
    }
    .left-top {
      display: inline-block;
      width: 27px;
      height: 34px;
      background-image: url("~@/assets/left-top.png");
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
      left: 0;
    }
    .left-bottom {
      display: inline-block;
      width: 27px;
      height: 34px;
      background-image: url("~@/assets/left-bottom.png");
      background-repeat: no-repeat;
      position: absolute;
      left: 0;
      bottom: 0ch;
    }
    .right-top {
      display: inline-block;
      width: 27px;
      height: 34px;
      background-image: url("~@/assets/right-top.png");
      background-repeat: no-repeat;
      position: absolute;
      right: 0;
      top: 0;
    }
    .right-bottom {
      display: inline-block;
      width: 27px;
      height: 34px;
      background-image: url("~@/assets/right-bottom.png");
      background-repeat: no-repeat;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
}
</style>
