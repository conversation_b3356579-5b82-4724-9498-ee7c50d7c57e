import request from '@/api/request';
import { MessageCodeMap } from '@/assets/libs/enum';
import URL from '../urls';

export class Message {
  /**
   * 获取消息列表
   */
  static getList(params) {
    return request.post(URL.message.list, params);
  }

  /**
   * 标记消息为已读
   */
  static markRead(item) {
    if (typeof item === 'string') {
      return request.post(URL.message.read, { id: item });
    } else if (item.typeCode === 1 || item.typeCode === 5 || item.typeCode === 6) {
      return request.post(URL.message.read, item);
    } else {
      return request.post(URL.message.read, { id: item.id });
    }
  }

  static getTypes() {
    return request.get(URL.message.type);
  }

  /**
   * 根据消息id获取消息详情 - 旧日报详情接口
   * @param {*} params
   */
  static getDetail(id) {
    return request.get(URL.message.detail + id);
  }

  /**
   * 获取日报消息详情 - 新日报详情接口
   * @param {*} params
   */
  static getDailyDetail(data) {
    return request.post(URL.message.dailyDetail, data);
  }

  /**
   * 根据消息类型 设置全部已读
   * @param {*} data
   */
  static readAll(data) {
    return request.post(URL.message.readAll, data);
  }

  /**
   * 获取月报详情
   * @param {*} data
   */
  static getMonthlyReport(data) {
    return request.post(URL.message.getMonthlyReport, data);
  }

  /**
   * 获取长显示的故障消息
   * @param {*} data
   */
  static getFaultMessage(data) {
    return request.get(URL.message.getFaultMessage, data);
  }

  /**
   * 获取长显示的故障消息
   * @param {*} params
   */
  static judgeFileDownloaded(fileName) {
    return request.get(URL.message.judgeFileDownloaded(fileName));
  }

  /**
   * 获取发版通知的维护消息
   */
  static getSystemMessage() {
    return request.get(URL.message.getSystemMessage);
  }
}
