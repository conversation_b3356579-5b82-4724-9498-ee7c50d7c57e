<template>
  <titan-dialog
    :title="m_title"
    :visible.sync="showDialog"
    :config="{
      showConfirm: true,
      confirmText: $t('components.确定'),
      cancelText: $t('components.取消'),
      confirmLoading
    }"
    @confirm="preHandleAdd"
    @open="dialogOpen"
    @closed="dialogClosed"
  >
    <div class="top-title">
      <span>{{ $t('components. 所属板块') }}</span>
      <span>{{ $t('unit.' + primary) }}</span>
    </div>
    <div
      v-for="(item, it) in group"
      :key="it"
      class="form-warp"
    >
      <titan-form
        :ref="'form_' + it"
        :key="it"
        label-width="200px"
        :model="item.form"
        :column="item.column"
        :rules="item.rules"
      />
      <div
        v-if="it > 0"
        class="delete-btn"
        @click="() => {
          group.splice(it, 1)
        }"
      >
        <i class="fa fa-minus" />
      </div>
    </div>

    <div
      v-if="multiple"
      class="btn-bottom"
      @click="appendGroup"
    >
      <i class="fa fa-plus" /> {{ $t('components. 新增') }}
    </div>
  </titan-dialog>
</template>

<script>
import { TagManagerAPI } from '@/api/modules/tag-manager';
import { mapState, mapMutations, mapGetters } from 'vuex';
import { tagPrimaryAlias } from '@/assets/libs/enum';
import CountryNameMap from '@/assets/libs/country';
import i18n from '@/lang';
import { clearForm } from '@/utils';

export default {
  name: 'SysTagDialog',
  inject: {
    tagTypeLabel: { default: i18n.t('components["标签类型"]') },
    tagNameLabel: { default: i18n.t('components["标签名称"]') },
    tagTxt: { default: i18n.t('components["标签"]') }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    primary: {
      type: String,
      default: '角色培育通用'
    },
    formValue: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: true
    },
    confirmAction: {
      type: [String, Function],
      default: ''
    },
    defaultTagType: {
      type: String,
      default: '',
      info: '默认选中的标签类型'
    },
    defaultTagTypeEditable: {
      type: Boolean,
      default: true,
      info: '默认选中的标签类型是否可以被修改'
    },
    isRequiredTags: {
      type: Boolean,
      default: false
    },
    translateName: {
      type: String,
      default: ''
    },
    // 是否显示添加标签成功的提示
    showMsg: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      group: [],
      confirmLoading: false,
      categoryList: []
    };
  },
  computed: {
    ...mapGetters(['canImport']),
    ...mapState('tag', {
      tagDict(state) {
        const primary = tagPrimaryAlias[this.primary];
        return state[primary].tagDict;
      },
      typeDict(state) {
        const primary = tagPrimaryAlias[this.primary];
        return state[primary].typeDict;
      }
    }),
    showDialog: {
      set(val) {
        this.$emit('update:visible', val);
      },
      get() {
        return this.visible;
      }
    },
    m_title() {
      if (this.title) return this.title;
      if (this.primary === '角色培育通用') {
        return _.isFunction(this.confirmAction) ? this.$t('components[" 批量打培育标签"]') : this.$t('components[" 新增培育标签"]');
      }
      if (this.primary === '职业通用') {
        return _.isFunction(this.confirmAction) ? this.$t('dataCenter["批量设置职业"]') : this.$t('dataCenter["新增职业"]');
      }
      return '';
    }
  },
  methods: {
    ...mapMutations('tag', ['ADD_TAG_DICT']),
    preHandleAdd() {
      const p_arr = [];
      this.group.forEach((item, index) => {
        const ref_key = 'form_' + index;
        p_arr.push(this.$refs[ref_key][0].validate());
      });

      Promise.all(p_arr)
        .then(val => this.handleAdd())
        .catch(err => console.error(err));
    },
    async handleAdd() {
      try {
        this.confirmLoading = true;
        const post_data = [];
        this.group.forEach(item => {
          item.form.name.forEach(a => {
            const one = {
              ...item.form,
              main: this.primary,
              editable: true,
              name: a
            };
            if (one.tag_type_id === 'NO-TYPE') delete one.tag_type_id;
            post_data.push(one);
          });
        });
        const { data } = await TagManagerAPI.addTag(post_data);
        const tag_list = []; // 更新 store tag 字典
        const already_list = []; // 已有的标签字典
        for (let i = 0; i < data.length; i++) {
          const id = data[i]['id'];
          // 已有的标签直接跳过
          if (this.tagDict[id]) {
            already_list.push(this.tagDict[id]);
            continue;
          }
          const add_one = post_data[i];
          const type_id = add_one.tag_type_id;
          tag_list.push(clearForm({
            id,
            name: add_one.name,
            type_id,
            type_name: this.typeDict[type_id]?.name
          }));
        }
        this.ADD_TAG_DICT({ tags: tag_list, primary: this.primary }); // 更新标签字典
        const ids = data.map(a => a.id);
        if (!_.isFunction(this.confirmAction)) {
          this.$emit('add-tag', ids, already_list);
        } else {
          await this.confirmAction(ids);
          this.showMsg && this.$message.success(this.$t('components["设置"]') + this.tagTxt + this.$t('components["成功"]'));
          this.$emit('batch-success', ids, already_list);
        }
        this.showDialog = false;
      } catch (err) {
        console.error(err);
      } finally {
        this.confirmLoading = false;
      }
    },
    async dialogOpen() {
      const { data } = await TagManagerAPI.getTagCategoriesList({ main: this.primary });
      const result = data || [];
      result.unshift({ id: 'NO-TYPE', name: this.$t('dataCenter.无标签类型') });
      this.categoryList = result;
      const group_item = {
        column: [
          {
            label: this.tagTypeLabel,
            prop: 'tag_type_id',
            render: (h, form) => (
              <titan-select
                v-model={form.tag_type_id}
                placeholder={this.$t('info["请选择"]') + this.tagTypeLabel}
                clearable
                disabled={!this.defaultTagTypeEditable}
                config={{
                  dataSource: this.categoryList,
                  respFormatter: option => option.filter(item => {
                    const had_select = this.group.map(a => a.form.tag_type_id);
                    return !had_select.includes(item.id);
                  }),
                  queryKey: 'name',
                  itemLabel: a => {
                    if (!this.translateName) return a.name;
                    if (this.translateName === 'AreaCode') return CountryNameMap[a.name] || a.name;
                  },
                  itemValue: 'id'
                }}
                onChange={() => {
                  form.name = [];
                }}
              />
            )
          },
          {
            label: this.tagNameLabel,
            prop: 'name',
            render: (h, form) => {
              const query = form.tag_type_id !== 'NO-TYPE'
                ? {
                  main: this.primary,
                  tag_type_id: form.tag_type_id,
                  can_import: !this.canImport, // syh false/不传:全部  true:过滤##SPARE(刚好与vuex里面的canImport的逻辑相反)
                  need_tag_type: true
                }
                : {
                  main: this.primary,
                  empty_tag_type: true,
                  can_import: !this.canImport // syh false/不传:全部  true:过滤##SPARE(刚好与vuex里面的canImport的逻辑相反)
                };

              return (
                <titan-select
                  v-model={form.name}
                  multiple={true}
                  allowCreate={this.primary !== '职业通用'}
                  clearable
                  multiple-limit={this.multiple ? 0 : 1}
                  placeholder={
                    this.multiple
                      ? this.$t('dataCenter["支持多选，相同名称的"]') + this.tagTxt + this.$t('dataCenter["不会被重复添加"]')
                      : this.$t('info["请选择"]') + this.tagTxt
                  }
                  config={{
                    method: TagManagerAPI.getTagList,
                    requestParams: query,
                    queryKey: 'name',
                    itemLabel: 'name',
                    itemValue: 'name',
                    maxLength: 15
                  }}
                  v-on:change={() => {
                    const regexp = /^\s*|\s*$/ug;
                    const end = form.name.map(a => a.replace(regexp, '')).filter(a => a && a.length > 0);
                    form.name = _.uniq(end);
                  }}
                />
              );
            }
          }
        ],
        rules: {
          tag_type_id: [
            { required: this.isRequiredTags, message: this.$t('info["请选择"]') + this.tagTypeLabel, trigger: 'blur' }
          ],
          name: [
            { required: this.isRequiredTags, message: this.tagTxt + this.$t('dataCenter["不能为空"]'), trigger: 'blur' }
          ]
        }
      };

      const form_value = _.cloneDeep(this.formValue).filter(a => this.tagDict[a]);
      if (_.isEmpty(form_value)) {
        this.group = [{
          form: {
            tag_type_id: '',
            name: []
          },
          ...group_item
        }];

        // 如果选择了默认的标签类型
        if (this.defaultTagType) {
          this.$nextTick(() => {
            this.group[0].form.tag_type_id = this.defaultTagType;
          });
        }
      } else {
        const opt = {};
        for (let i = 0; i < form_value.length; i++) {
          const id = form_value[i];
          const tag = this.tagDict[id];
          if (!tag) continue;

          // 如果有标签类型
          if (tag.type_id) {
            if (opt[tag.type_id]) {
              opt[tag.type_id].name.push(tag.name);
            } else {
              opt[tag.type_id] = {
                tag_type_id: tag.type_id,
                name: [tag.name]
              };
            }
          } else if (opt['NO-TYPE']) {
            // 如果没有标签类型，且opt['NO-TYPE'] 有值
            opt['NO-TYPE'].name.push(tag.name);
          } else {
            // 如果没有标签类型，且opt['NO-TYPE'] 无值
            opt['NO-TYPE'] = {
              tag_type_id: 'NO-TYPE',
              name: [tag.name]
            };
          }
        }

        const result = [];
        Object.values(opt).forEach((item, index) => {
          result.push({
            form: {
              tag_type_id: '',
              name: item.name
            },
            ...group_item
          });
        });
        this.group = result;
        // 赋值标签类型
        this.$nextTick(() => {
          Object.keys(opt).forEach((item, index) => {
            result[index].form.tag_type_id = item;
          });
        });
      }
    },
    dialogClosed() {
      this.group = [];
    },
    appendGroup() {
      const column = _.cloneDeep(this.group[0].column);
      const rules = _.cloneDeep(this.group[0].rules);
      this.group.push({
        form: {
          tag_type_id: '',
          name: []
        },
        column,
        rules
      });
    }
  }
};
</script>

<style scoped lang="scss">
.top-title{
  margin-bottom: 18px;
  span:first-child{
    font-size: 14px;
    color: #606266;
    padding-right: 14px;
    font-weight: 700;
  }
}
.btn-bottom{
  text-align: center;
  color: #3381d0;
  font-size: 16px;
  width: 100px;
  margin: auto;
  cursor: pointer;
}
.form-warp{
  position: relative;
  ::v-deep .titan-form-item__render {
    width: 90%;
    .el-select{
      width: 100%;
    }
  }
  .delete-btn{
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    font-size: 16px;
    text-align: center;
    line-height: 20px;
    background: #F56C6C;
    color: #FFFFFF;
    cursor: pointer;
    right: 0;
    top: 28px;
  }
}
</style>
