<script>

/**
 * 需要显示内容的培育行为
 */
const showContentActions = ['comment', 'repost', 'send_msg', 'recv_msg'];

import {
  AccountStatusOptions,
  PlatformResMap,
  TargetSiteResMap,
  CollectionResultTypeMap,
  SocialAccountType,
  ResourceTypeLabelMap
} from '@/assets/libs/enum';
import SocialActionList from '@/views/mission/utils/social-actions';
import { checkType, isEmpty } from '@/utils';
import querystring from 'querystring';

export default {
  name: 'RecordInfo',
  props: {
    result: {
      type: [String, Object],
      default: () => ({})
    },
    label: {
      type: String,
      default: ''
    },
    element: {
      type: String,
      default: '.log-container'
    },
    platform: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showMore: false,
      mainCreateStatus: {
        0: this.$t('components["队列中"]'),
        1: this.$t('components["进行中"]'),
        2: this.$t('components["已完成"]'),
        3: this.$t('components["已失败"]'),
        4: this.$t('components["已暂停"]'),
        5: this.$t('components["已停止"]'),
        6: this.$t('components["暂停中"]')
      },
      subCreateStatus: {
        0: this.$t('components["队列中"]'),
        1: this.$t('components["中"]'),
        2: this.$t('components["成功"]'),
        3: this.$t('components["失败"]'),
        4: this.$t('components["已暂停"]'),
        5: this.$t('components["已停止"]'),
        6: this.$t('components["暂停中"]')
      },
      breedActions: {
        post: this.$t('components["发帖"]'),
        comment: this.$t('components["评论"]'),
        repost: this.$t('components["转发"]'),
        like: this.$t('components["点赞"]'),
        dislike: this.$t('components["点踩"]'),
        follow: this.$t('components["关注"]'),
        send_msg: this.$t('components["发送消息"]'),
        recv_msg: this.$t('components["接收消息"]'),
        join: this.$t('components["加入社区"]'),
        bypass: this.$t('components["通过好友请求"]'),
        visit: this.$t('components["访问"]'),
        upload_avatar: this.$t('components["上传头像"]'),
        upload_background: this.$t('components["上传背景图"]')
      }
    };
  },
  computed: {
    socialActionMap() {
      const res = {};
      SocialActionList.forEach(item => {
        res[item.schema] = item.label;
      });
      return res;
    },
    accountStatusLabelMap() {
      const res = {};
      AccountStatusOptions.forEach(item => {
        res[item.value] = item.label;
      });
      return res;
    }
  },
  methods: {

    /**
     * 生成【复制代理】按钮
     */
    generateProxyBtn(proxyInfo) {
      const handleCopy = async() => {
        await this.$copyText(proxyInfo);
        this.$message.success(this.$t('components["【代理信息】复制成功"]'));
      };
      if (proxyInfo) {
        return (
          <titan-link type='primary' onClick={handleCopy}>
            {this.$t('components["复制代理"]')}
          </titan-link>
        );
      }
      return '';
    },

    /**
     * 生成【新标签页】链接
     */
    generateLinkBtn(href, label) {
      const handleView = () => {
        window.open(href);
      };
      if (href) {
        return (
          <titan-link type='primary' onClick={handleView}>
            {label}
          </titan-link>
        );
      }
      return '';
    },

    /**
     * 生成【账户】按钮
     */
    generateAccountBtn(account, hasBorder) {
      const handleView = () => {
        const url =
          '/#/account-center/virtual/sys-account?' +
          querystring.stringify({
            name: account,
            platform: this.platform
          });
        window.open(url);
      };
      const ele = (
        <titan-link type='primary' onClick={handleView}>
          {account}
        </titan-link>
      );
      if (hasBorder) {
        return <span style='display: flex;'>{this.$t('components["账号"]')}【{ele}】</span>;
      }
      return ele;
    },

    /**
     * 生成【展开/收起】按钮
     */
    generateToggleBtn() {
      const handleToggle = () => {
        // 当前点击项
        const item = this.$parent.$el;
        // 滚动盒子元素
        const wrapper = item.closest(this.element);
        const extraHeight = wrapper.offsetTop;
        wrapper.scrollTop = item.offsetTop - extraHeight;
        this.showMore = !this.showMore;
      };
      return (
        <titan-link
          type='primary'
          style='verticalAlign: top;'
          onClick={handleToggle}
        >
          {this.showMore ? this.$t('components["收起"]') : this.$t('components["点击查看全部"]')}
        </titan-link>
      );
    },

    /**
     * 格式化【注册账号】日志条目
     */
    formatCreateLogItem(label) {
      let line1 = '';
      let line2 = '';
      let expandLink = '';

      // 注册日志
      line1 = `${label}${this.mainCreateStatus[this.result.status]}`;
      if (!isEmpty(this.result.count)) {
        // line1 += `，已完成${this.result.count}个注册账号，剩余${
        //   this.result.remain > 0 ? this.result.remain : 0
        // }个，当前运行数为${
        //   this.result.executionIndex
        // }`;
        // if (this.result.remain > 0) {
        //   line1 += `，正在注册第${this.result.count + 1}个账号`;
        // }
      }
      line1 += `${this.result.message ? '，' + this.result.message : ''}`;
      line2 = (this.result.result || []).map(info => {
        const resourceTypeValue = SocialAccountType[this.platform];
        const methodList = [];
        ['email', 'phone', 'union_platform'].forEach(prop => {
          if (info[prop]) {
            methodList.push(
              `${ResourceTypeLabelMap[resourceTypeValue]}（${info[prop]}）`
            );
          }
        });
        return (
          <p>
            {!isEmpty(methodList) ? this.$t('components["使用"]') + methodList.join('+') : ''}
            {this.$t('components["注册"]') + this.subCreateStatus[info.status]}
            {info.status === 2 && '，' + this.$t('components["注册账号为"]')}
            {info.status === 2 && this.generateAccountBtn(info.account)}
            {info.message && `，${info.message}`}
            {this.generateProxyBtn(info.proxy)}
            {this.generateLinkBtn(info.log_href, this.$t('components["实时日志"]'))}
            {this.generateLinkBtn(info.url, this.$t('components["结果URL"]'))}
          </p>
        );
      });
      if (line2.length > 3) {
        expandLink = this.generateToggleBtn();
      }
      return (
        <div class='record-info'>
          <p>
            {line1} {this.showMore ? expandLink : ''}
          </p>
          <p>{line2.slice(0, 3)}</p>
          <p>{this.showMore ? line2.slice(3) : ''}</p>
          <p>{expandLink}</p>
        </div>
      );
    },

    /**
     * 格式化【培育账号】日志条目
     */
    formatBreedLogItem(label) {
      let line1 = '';
      let line2 = '';
      let expandLink = '';

      const subInfo = this.result.result || [];
      // 培育日志
      line1 = `${label}${this.mainCreateStatus[this.result.status]}`;
      line1 += `${this.result.message ? '，' + this.result.message : ''}`;
      if (subInfo.length === 1) {
        const account = subInfo[0].account;
        const records = subInfo[0].record || [];
        line2 = records.map(record => {
          const actionName = this.breedActions[record.action] || record.action;
          return (
            <p>
              {this.generateAccountBtn(account, true)}{this.$t('components["进行"]')}
              <span class='action'>{actionName}：</span>
              <titan-link
                type='primary'
                href={record.url}
                target='_blank'
                class='link'
                underline={true}
              >
                {record.url}
              </titan-link>
              {showContentActions.includes(record.action) &&
                record.content &&
                `，${actionName}${this.$t('components["内容"]')}：${record.content}`}
            </p>
          );
        });
      } else {
        line2 = subInfo.map(info => (
          <p>
            {this.generateAccountBtn(info.account, true)}
            {this.accountStatusLabelMap[info.status]}
            {info.msg ? '，' + info.msg : ''}
          </p>
        ));
      }
      if (line2.length > 3) {
        expandLink = this.generateToggleBtn();
      }

      return (
        <div class='record-info'>
          <p>
            {line1} {this.showMore ? expandLink : ''}
            {this.generateProxyBtn((this.result.proxy || {}).share_url)}
            {this.generateLinkBtn(this.result.log_href, this.$t('components["实时日志"]'))}
            {this.generateLinkBtn(this.result.url, this.$t('components["结果URL"]'))}
          </p>
          <p>{line2.slice(0, 3)}</p>
          <p>{this.showMore ? line2.slice(3) : ''}</p>
          <p>{expandLink}</p>
        </div>
      );
    },

    /**
     * 格式化【采集信息】日志条目
     */
    formatCollectLogItem(label) {
      // 采集日志
      let line1 = '';
      let line2 = '';
      let expandLink = '';
      line1 = `${label}${this.mainCreateStatus[this.result.status]}${
        this.result.message ? '，' + this.result.message : ''
      }`;
      line2 = (this.result.result || []).map(info => {
        const str = `${this.$t('components["采集"]')}${this.subCreateStatus[info.status]}${
          info.message ? '，' + info.message : ''
        }`;
        const collectTypes = (
          PlatformResMap[this.platform] ||
          TargetSiteResMap[this.platform] ||
          []
        )
          .map(item => CollectionResultTypeMap[item])
          .join('，');
        return (
          <p>
            {str}
            {info.author ? `，${this.$t('components["作者"]')}：${info.author}` : ''}
            {info.keyword ? `，${this.$t('components["关键字"]')}：${info.keyword}` : ''}
            {info.topic ? `，${this.$t('components["话题"]')}：${info.topic}` : ''}
            {!_.isNil(info.data_count)
              ? `，${this.$t('components["采集到"]')}${collectTypes}${this.$t('components["话未去重前数据题"]')}${info.data_count}${this.$t('components["条"]')}`
              : ''}
          </p>
        );
      });
      if (line2.length > 4) {
        expandLink = this.generateToggleBtn();
      }
      return (
        <div class='record-info'>
          {line1} {this.showMore ? expandLink : ''}
          {line2.slice(0, 4)}
          {this.showMore ? line2.slice(4) : ''}
          <p>{expandLink}</p>
        </div>
      );
    },

    formatMissionLogItem() {
      const missionLog = this.result;
      const subTaskList = missionLog.result || [];

      let line1 = '';
      let line2 = '';
      let expandLink = '';

      if (checkType(missionLog) === 'string') {
        return <div>{missionLog}</div>;
      }

      line1 = `${this.socialActionMap[missionLog.mission_type]}${this.$t('components["任务"]')}${
        this.mainCreateStatus[missionLog.status]
      }`;
      if (!isEmpty(missionLog.count)) {
        line1 += `，${this.$t('components["已完成"]')}${missionLog.count}${this.$t('components["个任务，剩余"]')}${
          missionLog.remain
        }${this.$t('components["个，当前运行数为"]')}${
          missionLog.current_running
        }`;
      }
      line1 += missionLog.message ? `，${missionLog.message}` : '';
      line2 = subTaskList.map(item => (
        <p>
          {item.account ? this.generateAccountBtn(item.account, true) : ''}
          {this.socialActionMap[missionLog.mission_type] +
            this.$t('components["任务"]') +
            this.mainCreateStatus[item.status]}
          {item.message && `，${item.message}`}
          {this.generateProxyBtn(item.proxy)}
          {this.generateLinkBtn(item.log_href, this.$t('components["实时日志"]'))}
          {this.generateLinkBtn(item.url, this.$t('components["结果URL"]'))}
        </p>
      ));

      if (line2.length > 3) {
        expandLink = this.generateToggleBtn();
      }
      return (
        <div class='record-info'>
          <p>
            {line1} {this.showMore ? expandLink : ''}
          </p>
          <p>{line2.slice(0, 3)}</p>
          <p>{this.showMore ? line2.slice(3) : ''}</p>
          <p>{expandLink}</p>
        </div>
      );
    }
  },
  render: function(h) {
    const label = this.label ? this.label + '：' : '';
    if (typeof this.result === 'string') {
      return (
        <span>
          {label}
          {this.result}
        </span>
      );
    }

    switch (this.type) {
      case 'REGISTER':
        return this.formatCreateLogItem(label);
      case 'BREED':
        return this.formatBreedLogItem(label);
      case 'GATHER':
        return this.formatCollectLogItem(label);
      default:
        return this.formatMissionLogItem(label);
    }
  }
};
</script>

<style lang="scss" scoped>
.record-info {
  p {
    width: 100%;

    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .link {
      overflow: hidden;
      display: inline-block;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .action {
    color: #3569e7;
  }
}
</style>
