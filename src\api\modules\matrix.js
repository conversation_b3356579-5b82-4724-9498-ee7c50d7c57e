import http from '@/api/request';
import URL from '../urls';

export class MatrixAPI {
  /**
   * 可用账号平台分布
   */
  static availableAccountPlatforms(data) {
    return http.get(URL.matrix.availableAccountPlatforms, { params: data });
  }

  /**
   * 可用账号地区分布
   */
  static availableAccountArea(data) {
    return http.post(URL.matrix.availableAccountAera, data);
  }

  /* 主要账号列表 */
  static mainAccountList(data) {
    return http.post(URL.matrix.mainAccountList, data);
  }

  /**
   * 中心账号地区分布
   */
  static centralAccountArea(data) {
    return http.get(URL.matrix.centralAccountArea, { params: data });
  }

  /**
   * 矩阵分布
   */
  static matrixDistribution(data) {
    return http.get(URL.matrix.matrixDistribution, { params: data });
  }

  /**
   * 账号行为，账号影响力
   */
  static accountActions(data) {
    return http.get(URL.matrix.accountActions, { params: data });
  }
}
