<template>
  <div
    class="log-card"
  >
    <div class="title">
      {{ summary }}
    </div>
    <div class="step-flow">
      <step-card
        v-for="(item,index) in stepMapList"
        :key="index"
        :step-name="item.name"
        :step="item.step"
        :time="item.time"
        :status="item.status"
        :description="item.msg"
        :error-stack="item.error_stack"
        @copy-proxy="handleCopy"
      />
    </div>
  </div>
</template>

<script>
import StepCard from './StepCard';
import { MissionLabelMap, STEP_DICT, $m_a, SchemaNameMap } from '@/assets/libs/mission';

export default {
  name: 'LogCard',
  components: { StepCard },
  props: {
    info: {
      type: [Object, String],
      default: () => ({})
    }
  },
  data() {
    return { };
  },
  computed: {
    socialActionMap() {
      return SchemaNameMap;
    },
    stepMapList() {
      return (this.info.steps || []).map(item => {
        if (!_.isEmpty(item.info)) {
          if (_.isString(item.info)) {
            item.info = JSON.parse(item.info);
          }
        } else {
          item.info = {};
        }
        return {
          name: STEP_DICT[item.step.toUpperCase()],
          step: item.step.toUpperCase(),
          time: item.logTime,
          status: item.stepStatus,
          msg: item.message,
          error_stack: item.info?.error_stack ? item.info.error_stack : null
        };
      });
    },
    summary() {
      const missionLog = this.info;
      const type = $m_a(missionLog.actionType);
      if (missionLog.message === null) {
        if (missionLog.taskType === 'REGISTER') {
          return `${this.$t('components["创建虚拟账号任务"]')} - ${MissionLabelMap[missionLog.status]}`;
        } else {
          return `【${this.socialActionMap[type]}】${this.$t('components["任务"]')} - ${MissionLabelMap[missionLog.status]}`;
        }
      } else if (missionLog.taskType === 'REGISTER') {
        return `${this.$t('components["创建虚拟账号任务"]')} - ${MissionLabelMap[missionLog.status]} - ${missionLog.message}`;
      } else {
        return `【${this.socialActionMap[type]}】${this.$t('components["任务"]')} - ${MissionLabelMap[missionLog.status]} - ${missionLog.message}`;
      }
    }
  },
  methods: {
    async handleCopy() {
      if (this.info.steps[0].proxy) {
        await this.$copyText(this.info.steps[0].proxy);
        this.$message.success(this.$t('components["复制代理成功"]'));
      }
    }
  }
};
</script>

<style lang="scss" scoped>

.log-card {
  width: 100%;
  .title {
    width: 100%;
    color: #303133;
    margin-bottom: 14px;
    // @include utils-ellipsis(1)
  }
  .step-flow {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
}
</style>
